{"name": "eternia-survey-app-order-microservice", "version": "1.0.0", "description": "Eternia Survey App - Order Management", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js", "generateDevAPIDoc": "node api_doc/dev/create.js", "generateStagAPIDoc": "node api_doc/stag/create.js", "generateProdAPIDoc": "node api_doc/prod/create.js"}, "repository": {"type": "git", "url": "https://gitlab.openxcell.dev/eternia-survey-app/eternia-survey-app-order-microservice.git"}, "author": "Eternia", "license": "ISC", "dependencies": {"aws-sdk": "^2.1472.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "cluster": "^0.7.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.19.2", "express-rate-limit": "^7.3.1", "firebase-admin": "^11.11.0", "helmet": "^7.1.0", "https": "^1.0.0", "joi": "^17.10.2", "joi-password-complexity": "^5.2.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "nodemon": "^3.0.1", "otp-generator": "^4.0.1", "postman-to-openapi": "^3.0.1", "querystring": "^0.2.1", "requireg": "^0.2.2", "sequelize": "^6.33.0", "sequelize-cli": "^6.6.1", "sharp": "^0.32.6", "twilio": "^4.18.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}}