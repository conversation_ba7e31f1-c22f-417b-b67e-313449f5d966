const fs = require("fs");
const path = require("path");
const postmanToOpenApi = require("postman-to-openapi");
const apiGatewayIntegration = require("./apigateway_integration");
const optionsReq = require("../options");

const postmanCollection = path.resolve(__dirname + "/../postman_collection.json");

const outputFile = path.resolve(__dirname + "/open_api.json");
const apiGatewayOutputFile = path.resolve(__dirname + '/apigateway_rest_apis.json');

const options = {
    defaultTag: "General",
    servers: [
        {
            url: process.env.DEV_BASE_URL,
            description: `dev environment server`
        }
    ],
    outputFormat: "json",
    info: {
        title: "dev_api",
        description: `dev environment server`,
        version: new Date().toISOString()
    },
}

postmanToOpenApi(postmanCollection, outputFile, options)
    .then(result => {
        console.log(`OpenAPI success`);
        try {
            let collection = JSON.parse(result);
            const allAPIs = Object.keys(collection.paths);

            allAPIs.forEach((api, index) => {
                const allMethods = Object.keys(collection.paths[api])
                allMethods.map((method) => {
                    // Add api gateway configuration into all methods
                    collection.paths[api][method] = {
                        ...collection.paths[api][method],
                        ...apiGatewayIntegration(api, method)
                    };
                });

                // Add options request in all api for api gateway
                collection.paths[api]["options"] = optionsReq;

                if (index === (allAPIs.length - 1)) {
                    const updateData = JSON.stringify(collection, null, 4);
                    // Create or update json for api gateway
                    fs.writeFileSync(apiGatewayOutputFile, updateData);
                    console.log(`ApiGateway json created`);
                }
            });
        } catch (error) {
            console.log("Convert to API gateway json failed", error.message);
        }
    })
    .catch(err => {
        console.log("OpenAPI error", err)
    })
