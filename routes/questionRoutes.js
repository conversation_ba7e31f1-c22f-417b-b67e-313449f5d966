const express = require("express");
const router = express.Router();

const questionController = require("../controller/questionController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/questionValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
  .route(API_PATH.QUESTIONS.CREATE)
  .post(
    authenticateUser,
    validateParams(validator.createSchema),
    questionController.create
  );

router
  .route(API_PATH.QUESTIONS.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    questionController.get
  );

router
  .route(API_PATH.QUESTIONS.UPDATE)
  .post(
    authenticateUser,
    validateParams(validator.updateSchema),
    questionController.update
  );

router
  .route(API_PATH.QUESTIONS.CHANGE_STATUS)
  .post(
    authenticateUser,
    validateParams(validator.changeStatuSchema),
    questionController.changeStatus
  );

module.exports = router;
