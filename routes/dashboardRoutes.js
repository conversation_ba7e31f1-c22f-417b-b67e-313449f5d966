const express = require("express");
const router = express.Router();

const dashboardController = require("../controller/dashboardController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");
const { roleAccess } = require("../middleware/roleAccess.js");
const { CONSTANTS } = require("../helpers/constants.js");

router
    .route(API_PATH.DASHBOARD.GET)
    .get(
        authenticateUser,
        roleAccess([CONSTANTS.ROLES.NAMES.FABRICATOR, CONSTANTS.ROLES.NAMES.SUPERVISOR, CONSTANTS.ROLES.NAMES.SERVICE_HEAD, CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER]),
        dashboardController.getDashboardData
    );

module.exports = router;
