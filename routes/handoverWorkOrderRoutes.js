const express = require("express");
const router = express.Router();

const handoverWorkOrderController = require("../controller/handoverWorkOrderController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const { validateParams } = require("../middleware/validateParams");
const { roleAccess } = require("../middleware/roleAccess.js");
const validator = require("../validator/handoverWorkOrderValidator.js");
const { CONSTANTS } = require("../helpers/constants.js");
const { uploadSingleFile, uploadAnyFiles } = require("../middleware/multer.js");

const imgMaxSize = CONSTANTS.FILE_SIZE_LIMIT.IMAGE.MAX;

router
  .route(API_PATH.HANDOVER_WORKORDER.ADD)
  .post(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.FABRICATOR]),
    validateParams(validator.createSchema),
    handoverWorkOrderController.create
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.UPDATE)
  .post(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.FABRICATOR]),
    validateParams(validator.updateSchema),
    handoverWorkOrderController.update
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.GET)
  .get(validateParams(validator.getSchema), handoverWorkOrderController.get);

router
  .route(API_PATH.HANDOVER_WORKORDER.GET_WORK_ORDER_QUESTION)
  .get(
    validateParams(validator.getByIdSchema),
    handoverWorkOrderController.getById
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.SUBMIT_ANSWER)
  .post(
    uploadAnyFiles(imgMaxSize),
    validateParams(validator.submitAnswerSchema),
    handoverWorkOrderController.submitAnswer
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.CHECK_IN)
  .post(
    authenticateUser,
    validateParams(validator.checkInSchema),
    handoverWorkOrderController.checkIn
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.CHECK_OUT)
  .post(
    authenticateUser,
    validateParams(validator.checkInSchema),
    handoverWorkOrderController.checkOut
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.SEND_OTP)
  .post(
    validateParams(validator.sendOtpSchema),
    handoverWorkOrderController.sendOtpToCustomer
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.VERIFY_OTP)
  .post(
    validateParams(validator.verifyOtpSchema),
    handoverWorkOrderController.verifyOtp
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.ADD_SIGNATURE)
  .post(
    uploadSingleFile("signature", imgMaxSize),
    validateParams(validator.addSignatureSchema),
    handoverWorkOrderController.addSignature
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.REJECT)
  .post(
    validateParams(validator.rejectWorkrderSchema),
    handoverWorkOrderController.reject
  );

router
  .route(API_PATH.HANDOVER_WORKORDER.RAISE_COMPLAINT)
  .get(handoverWorkOrderController.raiseComplaint);

module.exports = router;
