const express = require("express");
const router = express.Router();

const orderController = require("../controller/orderController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/orderValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");
const { uploadSingleFile } = require("../middleware/multer");
const { CONSTANTS } = require("../helpers/constants.js");

const imgMaxSize = CONSTANTS.FILE_SIZE_LIMIT.IMAGE.MAX;

router
  .route(API_PATH.ORDERS.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    orderController.get
  );

router
  .route(API_PATH.ORDERS.GET_SPACE_SCOPE)
  .get(
    authenticateUser,
    validateParams(validator.getSpaceScopeSchema),
    orderController.getOrderSpaceScope
  );

router
  .route(API_PATH.ORDERS.GET_STATUS)
  .get(authenticateUser, orderController.getStatus);

router.route(API_PATH.ORDERS.SYNC).get(authenticateUser, orderController.sync);

router
  .route(API_PATH.ORDERS.FETCH_ZOHO)
  .post(
    authenticateUser,
    validateParams(validator.fetchZohoOrderSchema),
    orderController.fetchZohoOrders
  );

router
  .route(API_PATH.ORDERS.FETCH_SYNC_ZOHO_ORDERS)
  .get(authenticateUser, orderController.fetchAndSyncZohoOrders);

router
  .route(API_PATH.ORDERS.SYNC_MASTER_ORDER_AND_ORDER)
  .get(orderController.syncMasterOrderAndOrderAmount);

router
  .route(API_PATH.ORDERS.SYNC_MASTER_ORDER_AND_ORDER_LINE_ITEM)
  .get(orderController.syncMasterOrderAndOrderLineItem);

router
  .route(API_PATH.ORDERS.SYNC_ORDER_STATUS)
  .get(orderController.syncOrderStatus);

router
  .route(API_PATH.ORDERS.UPLOAD_IMAGE)
  .post(
    authenticateUser,
    uploadSingleFile("order_image", imgMaxSize),
    validateParams(validator.uploadImageSchema),
    orderController.uploadImage
  );

router
  .route(API_PATH.ORDERS.DELETE_IMAGE)
  .delete(authenticateUser, orderController.deleteImage);

router
  .route(API_PATH.ORDERS.HANDOVER_EMAIL)
  .post(
    authenticateUser,
    validateParams(validator.sendHandoverEmailSchema),
    orderController.sendHandoverEmail
  );

router
  .route(API_PATH.ORDERS.GET_HANDOVER_LINK)
  .post(
    authenticateUser,
    validateParams(validator.sendHandoverEmailSchema),
    orderController.sendHandoverLink
  );

router
  .route(API_PATH.ORDERS.SEND_WARRANTY_EMAIL)
  .post(
    authenticateUser,
    validateParams(validator.sendWarrantyEmailSchema),
    orderController.sendWarrantyMail
  );

router
  .route(API_PATH.ORDERS.SYNC_ORDER_ZOHO_STAGE)
  .get(authenticateUser, orderController.syncOrderZohoSatge);

module.exports = router;
