const express = require("express");
const router = express.Router();

const workOrderTypeController = require("../controller/workOrderTypeController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const validator = require("../validator/workOrderTypeValidator.js");
const { validateParams } = require("../middleware/validateParams");

router
    .route(API_PATH.WORK_ORDER_TYPE.ADD)
    .post(
        authenticateUser,
        validateParams(validator.createSchema),
        workOrderTypeController.create
    );

router
    .route(API_PATH.WORK_ORDER_TYPE.UPDATE)
    .post(
        authenticateUser,
        validateParams(validator.updateSchema),
        workOrderTypeController.update
    );

router
    .route(API_PATH.WORK_ORDER_TYPE.GET)
    .get(
        authenticateUser,
        validateParams(validator.getSchema),
        workOrderTypeController.get
    );

router
    .route(API_PATH.WORK_ORDER_TYPE.CHANGE_STATUS)
    .post(
        authenticateUser,
        validateParams(validator.changeStatusSchema),
        workOrderTypeController.changeStatus
    );

module.exports = router;
