const express = require("express");
const router = express.Router();

const workOrderController = require("../controller/workOrderController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const { validateParams } = require("../middleware/validateParams");
const { roleAccess } = require("../middleware/roleAccess.js");
const validator = require("../validator/workOrderValidator.js");
const { CONSTANTS } = require("../helpers/constants.js");
const { uploadSingleFile } = require("../middleware/multer.js");

const imgMaxSize = CONSTANTS.FILE_SIZE_LIMIT.IMAGE.MAX;

router.route(API_PATH.WORK_ORDER.ADD).post(
  authenticateUser,
  // roleAccess([CONSTANTS.ROLES.NAMES.FABRICATOR]),
  validateParams(validator.createSchema),
  workOrderController.create
);

router
  .route(API_PATH.WORK_ORDER.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    workOrderController.get
  );

router
  .route(API_PATH.WORK_ORDER.UPDATE)
  .post(
    authenticateUser,
    validateParams(validator.updateSchema),
    workOrderController.update
  );

router
  .route(API_PATH.WORK_ORDER.GET_ASSIGNED)
  .get(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.SUPERVISOR]),
    validateParams(validator.getAssignedWorkOrderSchema),
    workOrderController.getAssignedWorkOrders
  );

router
  .route(API_PATH.WORK_ORDER.GET_ASSIGNED_WITHOUT_PAGINATION)
  .get(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.SUPERVISOR]),
    validateParams(validator.getAssignedWorkOrderWithoutPaginationSchema),
    workOrderController.getAssignedWorkOrdersWithoutPagination
  );

router
  .route(API_PATH.WORK_ORDER.CHECK_IN)
  .post(
    authenticateUser,
    uploadSingleFile("selfie", imgMaxSize),
    validateParams(validator.checkInSchema),
    workOrderController.checkIn
  );

router
  .route(API_PATH.WORK_ORDER.CHECK_OUT)
  .post(
    authenticateUser,
    validateParams(validator.checkOutSchema),
    workOrderController.checkOut
  );

router
  .route(API_PATH.WORK_ORDER.SEND_OTP_FOR_SUBMIT_WO)
  .post(
    authenticateUser,
    validateParams(validator.sendOtpForSubmitWorkOrderSchema),
    workOrderController.sendOtpForSubmitWorkOrder
  );

router
  .route(API_PATH.WORK_ORDER.VERIFY_OTP_FOR_SUBMIT_WO)
  .post(
    authenticateUser,
    validateParams(validator.verifyOtpForSubmitWorkOrderSchema),
    workOrderController.verifyOtpForSubmitWorkOrder
  );

router
  .route(API_PATH.WORK_ORDER.REVIEW_LIST)
  .get(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
    ]),
    validateParams(validator.getReviewListSchema),
    workOrderController.getReviewList
  );

router
  .route(API_PATH.WORK_ORDER.REVIEW)
  .post(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
    ]),
    validateParams(validator.reviewSchema),
    workOrderController.review
  );

router
  .route(API_PATH.WORK_ORDER.REVIEW_QUESTION_TOUCH_POINT)
  .post(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
    ]),
    validateParams(validator.reviewQuestionTouchPointSchema),
    workOrderController.reviewQuestionTouchPoint
  );

router
  .route(API_PATH.WORK_ORDER.GET_STATUS)
  .get(authenticateUser, workOrderController.getStatus);

router
  .route(API_PATH.WORK_ORDER.SEND_PDF)
  .post(
    authenticateUser,
    validateParams(validator.sendSurveyPdfToZohoSchema),
    workOrderController.sendSurveyPdfToZoho
  );

router
  .route(API_PATH.WORK_ORDER.DELETE)
  .delete(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.FABRICATOR]),
    workOrderController.deleteWorkOrder
  );

router
  .route(API_PATH.WORK_ORDER.ACCEPT_REJECT_TAG)
  .post(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
    ]),
    validateParams(validator.acceptRejectTagRequestWorkOrderSchema),
    workOrderController.acceptRejectTagRequest
  );

router
  .route(API_PATH.WORK_ORDER.SE_SH_CHECK_IN)
  .post(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
    ]),
    validateParams(validator.seShCheckInSchema),
    workOrderController.seShCheckIn
  );

router
  .route(API_PATH.WORK_ORDER.SE_SH_CANCEL_CHECK_IN)
  .post(
    authenticateUser,
    roleAccess([
      CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
      CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
    ]),
    validateParams(validator.cancelSeShCheckInSchema),
    workOrderController.cancelSeShCheckIn
  );

router
  .route(API_PATH.WORK_ORDER.SE_SH_TAG_REQUEST)
  .post(
    authenticateUser,
    roleAccess([CONSTANTS.ROLES.NAMES.SUPERVISOR]),
    validateParams(validator.seShTagRequestSchema),
    workOrderController.seShTagRequest
  );

module.exports = router;
