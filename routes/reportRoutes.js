const express = require("express");
const router = express.Router();

const reportController = require("../controller/reportController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/reportValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
    .route(API_PATH.REPORTS.ORDER)
    .get(
        authenticateUser,
        validateParams(validator.getOrderReportSchema),
        reportController.getOrderReport
    );

module.exports = router;
