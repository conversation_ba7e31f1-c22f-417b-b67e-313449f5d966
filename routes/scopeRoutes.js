const express = require("express");
const router = express.Router();

const scopeController = require("../controller/scopeController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/scopeValidator.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const { validateParams } = require("../middleware/validateParams");
const { uploadSingleFile } = require("../middleware/multer.js");
const { CONSTANTS } = require("../helpers/constants.js");

const imgMaxSize = CONSTANTS.FILE_SIZE_LIMIT.IMAGE.MAX;

router
    .route(API_PATH.SCOPES.ADD)
    .post(
        authenticateUser,
        uploadSingleFile("scope_image", imgMaxSize),
        validateParams(validator.createSchema),
        scopeController.create
    );

/*
router
    .route(API_PATH.SCOPES.UPDATE)
    .post(
        authenticateUser,
        validateParams(validator.updateSchema),
        scopeController.update
    );
*/

router
    .route(API_PATH.SCOPES.GET)
    .get(
        authenticateUser,
        validateParams(validator.getSchema),
        scopeController.get
    );

router
    .route(API_PATH.SCOPES.CHANGE_STATUS)
    .post(
        authenticateUser,
        validateParams(validator.changeStatusSchema),
        scopeController.changeStatus
    );

module.exports = router;
