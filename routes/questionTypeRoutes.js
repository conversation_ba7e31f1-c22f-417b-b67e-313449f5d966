const express = require("express");
const router = express.Router();

const questionTypeController = require("../controller/questionTypeController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/questionTypeValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
    .route(API_PATH.QUESTION_TYPE.GET)
    .get(
        authenticateUser,
        validateParams(validator.getSchema),
        questionTypeController.get
    );

module.exports = router;
