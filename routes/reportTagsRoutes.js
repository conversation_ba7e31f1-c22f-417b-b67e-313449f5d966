const express = require("express");
const router = express.Router();

const reportTagsController = require("../controller/reportTagsController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/reportTagsValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
  .route(API_PATH.REPORT_TAGS.CREATE)
  .post(
    authenticateUser,
    validateParams(validator.create),
    reportTagsController.create
  );

router
  .route(API_PATH.REPORT_TAGS.GET)
  .get(
    authenticateUser,
    validateParams(validator.get),
    reportTagsController.get
  );

router
  .route(API_PATH.REPORT_TAGS.EDIT)
  .post(
    authenticateUser,
    validateParams(validator.edit),
    reportTagsController.edit
  );

module.exports = router;
