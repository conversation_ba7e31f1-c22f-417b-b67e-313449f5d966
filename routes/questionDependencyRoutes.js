const express = require("express");
const router = express.Router();
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");
const validator = require("../validator/questionDependencyValidator.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const questionDependencyController = require("../controller/questionDependency.js");

router
  .route(API_PATH.QUESTION_DEPENCENDY.MAP)
  .post(
    authenticateUser,
    validateParams(validator.mapQuestionDependencySchema),
    questionDependencyController.mapQuestionDependency
  );

router
  .route(API_PATH.QUESTION_DEPENCENDY.EDIT)
  .post(
    authenticateUser,
    validateParams(validator.updateDependencySchema),
    questionDependencyController.updateDependency
  );

router
  .route(API_PATH.QUESTION_DEPENCENDY.UNMAP)
  .delete(
    authenticateUser,
    validateParams(validator.deactivateDependencySchema),
    questionDependencyController.deactivateDependency
  );

module.exports = router;
