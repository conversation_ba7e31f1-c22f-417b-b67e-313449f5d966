const express = require("express");
const router = express.Router();

const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/spaceValidator.js");
const spaceController = require("../controller/spaceController.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const { validateParams } = require("../middleware/validateParams");

router
    .route(API_PATH.SPACES.ADD)
    .post(
        authenticateUser,
        validateParams(validator.createSchema),
        spaceController.create
    );

router
    .route(API_PATH.SPACES.UPDATE)
    .post(
        authenticateUser,
        validateParams(validator.updateSchema),
        spaceController.update
    );

router
    .route(API_PATH.SPACES.GET)
    .get(
        authenticateUser,
        validateParams(validator.getSchema),
        spaceController.get
    );

router
    .route(API_PATH.SPACES.CHANGE_STATUS)
    .post(
        authenticateUser,
        validateParams(validator.changeStatusSchema),
        spaceController.changeStatus
    );


module.exports = router;
