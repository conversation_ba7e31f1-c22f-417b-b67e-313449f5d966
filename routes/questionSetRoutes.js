const express = require("express");
const router = express.Router();

const questionSetController = require("../controller/questionSetController.js");
const { API_PATH } = require("../helpers/apiEndPoints.js");
const validator = require("../validator/questionSetValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
  .route(API_PATH.QUESTION_SET.CREATE)
  .post(
    authenticateUser,
    validateParams(validator.createSchema),
    questionSetController.create
  );

router
  .route(API_PATH.QUESTION_SET.CHANGE_STATUS)
  .post(
    authenticateUser,
    validateParams(validator.changeStatusSchema),
    questionSetController.changeStatus
  );

router
  .route(API_PATH.QUESTION_SET.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    questionSetController.get
  );

router
  .route(API_PATH.QUESTION_SET.GET_BY_WOTYPE_SCOPE)
  .get(
    authenticateUser,
    validateParams(validator.getByWoTypeScopeSchema),
    questionSetController.getByWoTypeScope
  );

router
  .route(API_PATH.QUESTION_SET.EDIT)
  .post(
    authenticateUser,
    validateParams(validator.editSchema),
    questionSetController.editQuestionSet
  );

module.exports = router;
