const express = require("express");
const router = express.Router();

const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/taskValidator.js");
const taskController = require("../controller/taskController.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator");
const { validateParams } = require("../middleware/validateParams");
const { uploadSingleFile } = require("../middleware/multer");
const { CONSTANTS } = require("../helpers/constants.js");

const imgMaxSize = CONSTANTS.FILE_SIZE_LIMIT.IMAGE.MAX;

router
  .route(API_PATH.TASKS.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    taskController.get
  );

router
  .route(API_PATH.TASKS.SUBMIT_ANSWER)
  .post(
    authenticateUser,
    validateParams(validator.submitAnswerSchema),
    taskController.submitAnswer
  );

router
  .route(API_PATH.TASKS.UPLOAD_IMAGE_ANSWER)
  .post(
    authenticateUser,
    uploadSingleFile("answer_image", imgMaxSize),
    validateParams(validator.uploadImageAnswerSchema),
    taskController.uploadImageAnswer
  );

router
  .route(API_PATH.TASKS.GENERATE_VIDEO_ANSWER_SIGNED_URL)
  .post(
    authenticateUser,
    validateParams(validator.generateTaskAnswerSignedUrlSchema),
    taskController.generateTaskAnswerSignedUrl
  );

router
  .route(API_PATH.TASKS.UPDATE_VIDEO_URL)
  .post(
    authenticateUser,
    validateParams(validator.updateVideoUrlSchema),
    taskController.updateVideoUrl
  );

router
  .route(API_PATH.TASKS.REVIEW)
  .get(
    authenticateUser,
    validateParams(validator.getReviewSchema),
    taskController.getTaskReview
  );

router
  .route(API_PATH.TASKS.DELETE_VIDEO_ANSWER_SIGNED_URL)
  .delete(authenticateUser, taskController.deleteTaskVideoAnswerSignedUrl);

module.exports = router;
