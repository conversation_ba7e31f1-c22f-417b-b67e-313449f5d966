const express = require("express");
const router = express.Router();

const pointsController = require("../controller/pointsController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");
const { validateParams } = require("../middleware/validateParams.js");
const validator = require("../validator/pointsValidator.js");

router
    .route(`${API_PATH.POINTS.RATE.BASE}${API_PATH.POINTS.RATE.DEFINE_PER_POINTS}`)
    .post(
        authenticateUser,
        validateParams(validator.defineRatePerPointsSchema),
        pointsController.defineRatePerPoints
    );

router
    .route(`${API_PATH.POINTS.RATE.BASE}${API_PATH.POINTS.RATE.GET}`)
    .get(
        authenticateUser,
        validateParams(validator.getRateOfPointsSchema),
        pointsController.getRateOfPoints
    );

router
    .route(API_PATH.POINTS.DEFINE_POINTS_PER_WO_TYPE)
    .post(
        authenticateUser,
        validateParams(validator.definePointsPerWoTypeSchema),
        pointsController.definePointsPerWoType
    );

router
    .route(API_PATH.POINTS.GET)
    .get(
        authenticateUser,
        validateParams(validator.getPointsSchema),
        pointsController.getPoints
    );

router
    .route(API_PATH.POINTS.GET_BY_WO_TYPE)
    .get(
        authenticateUser,
        validateParams(validator.getPointsByWorkOrderTypeSchema),
        pointsController.getPointsByWorkOrderType
    );

router
    .route(API_PATH.POINTS.HISTORY)
    .get(
        authenticateUser,
        validateParams(validator.getPointsHistoryOfUserSchema),
        pointsController.getPointsHistoryOfUser
    );

router
    .route(API_PATH.POINTS.BALANCE)
    .get(
        authenticateUser,
        validateParams(validator.getBalanceOfUserSchema),
        pointsController.getBalanceOfUser
    );

router
    .route(API_PATH.POINTS.REDEEM)
    .post(
        authenticateUser,
        validateParams(validator.redeemEarningSchema),
        pointsController.redeemEarning
    );

router
    .route(API_PATH.POINTS.REDEEM_HISTORY)
    .get(
        authenticateUser,
        validateParams(validator.redeemedEarningHistorySchema),
        pointsController.redeemedEarningHistory
    );

module.exports = router;
