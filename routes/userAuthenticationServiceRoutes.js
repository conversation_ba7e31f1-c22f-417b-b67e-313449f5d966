const express = require("express");
const router = express.Router();

const userAuthenticationServiceController = require("../controller/userAuthenticationServiceController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/userAuthenticationServiceValidator.js");
const { validateParams } = require("../middleware/validateParams");

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.IS_ASSIGNED_ORDER_WO_COMPLETED)
  .post(
    validateParams(validator.isAssignedWorkOrderCompletedSchema),
    userAuthenticationServiceController.isAssignedWorkOrderCompleted
  );

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.GET_ASSIGNED_ORDERS)
  .get(
    validateParams(validator.getAssignedOrdersSchema),
    userAuthenticationServiceController.getAssignedOrders
  );

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.GET_ASSIGNED_ORDERS_BY_STATUS)
  .get(
    validateParams(validator.getAssignedOrdersByStatusSchema),
    userAuthenticationServiceController.getAssignedOrdersByStatus
  );

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.GET_ASSIGNED_WORK_ORDERS)
  .get(
    validateParams(validator.getAssignedWorkOrdersSchema),
    userAuthenticationServiceController.getAssignedWorkOrders
  );

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.GET_POINTS_EARNINGS_OF_USER)
  .get(
    // validateParams(getPointsEarningsOfUserSchema),
    userAuthenticationServiceController.getPointsEarningsOfUser
  );

router
  .route(API_PATH.ORDER_MANAGEMENT_SERVICE_APIS.UPDATE_FABRICATOR_IN_ORDER)
  .post(
    validateParams(validator.updateFabricatorInOrderSchema),
    userAuthenticationServiceController.updateFabricatorInOrder
  );

module.exports = router;
