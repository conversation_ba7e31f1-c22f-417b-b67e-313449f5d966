const express = require("express");
const router = express.Router();

const handoverQuestionController = require("../controller/handoverQuestionController.js");
const { API_PATH } = require("../helpers/apiEndPoints");
const validator = require("../validator/handoverQuestionValidator.js");
const { validateParams } = require("../middleware/validateParams.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
  .route(API_PATH.HANDOVER_QUESTION.CREATE)
  .post(
    authenticateUser,
    validateParams(validator.createSchema),
    handoverQuestionController.create
  );

router
  .route(API_PATH.HANDOVER_QUESTION.GET)
  .get(
    authenticateUser,
    validateParams(validator.getSchema),
    handoverQuestionController.get
  );

router
  .route(API_PATH.HANDOVER_QUESTION.UPDATE_STATUS)
  .post(
    authenticateUser,
    validateParams(validator.updateStatusSchema),
    handoverQuestionController.updateStatus
  );

module.exports = router;
