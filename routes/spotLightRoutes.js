const express = require("express");
const router = express.Router();

const spotLightController = require("../controller/spotLightController.js");
const { API_PATH } = require("../helpers/apiEndPoints.js");
const { authenticateUser } = require("../middleware/serviceAuthenticator.js");

router
    .route(API_PATH.SPOT_LIGHT.PRO_SCORE)
    .get(
        authenticateUser,
        spotLightController.getBSProScore
    );

module.exports = router;
