FROM node:18-alpine as build
WORKDIR /app
COPY package.json .
RUN npm install

FROM node:18-alpine as final
ARG APP_NAME=app
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV APP_NAME=${APP_NAME}
RUN npm install -g pm2
WORKDIR /app
COPY --from=build /app/node_modules /app/node_modules
COPY .env.production .env
COPY . .
RUN npx sequelize db:migrate || exit 0
RUN npx sequelize-cli db:seed:all || exit 0
CMD pm2-runtime start index.js --name ${APP_NAME}
