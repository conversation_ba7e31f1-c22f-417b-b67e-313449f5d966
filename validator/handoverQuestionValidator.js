const joi = require("joi");
const { CONSTANTS } = require("../helpers/constants");

const createSchema = joi.object({
  que_type: joi
    .string()
    .valid(...Object.values(CONSTANTS.HANDOVER_QUESTION_TYPES))
    .required(),
  order: joi.number().required(),
  title: joi.string().required(),
  min_number: joi.number().when("que_type", {
    is: CONSTANTS.HANDOVER_QUESTION_TYPES.RATING,
    then: joi.required(),
    otherwise: joi.optional(),
  }),
  max_number: joi.number().when("que_type", {
    is: CONSTANTS.HANDOVER_QUESTION_TYPES.RATING,
    then: joi.required(),
    otherwise: joi.optional(),
  }),
  options: joi.array().when("que_type", {
    is: joi.valid(
      CONSTANTS.HANDOVER_QUESTION_TYPES.DESCRIPTIVE,
      CONSTANTS.HANDOVER_QUESTION_TYPES.RATING,
      CONSTANTS.HANDOVER_QUESTION_TYPES.BOOLEAN
    ),
    then: joi.optional(),
    otherwise: joi.required(),
  }),
  attachment_required: joi.number().valid(0, 1).required(),
  comment_required: joi
    .object({
      required: joi.boolean().required(),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .valid("Equals", "Not equals", "Always")
              .required(),
            value: joi.alternatives().conditional("type", {
              switch: [
                {
                  is: "Always",
                  then: joi.string().optional().allow("", null),
                },
              ],
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
});

const getSchema = joi.object({
  id: joi.number().optional(),
  search: joi.string().min(3).optional(),
  que_type: joi
    .string()
    .valid(...Object.values(CONSTANTS.HANDOVER_QUESTION_TYPES))
    .optional(),
  status: joi.number().valid(0, 1).optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
});

const updateStatusSchema = joi.object({
  id: joi.number().required(),
  status: joi.number().valid(0, 1).required(),
});

module.exports = {
  createSchema,
  getSchema,
  updateStatusSchema,
};
