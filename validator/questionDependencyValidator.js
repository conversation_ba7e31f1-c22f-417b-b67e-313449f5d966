const Joi = require("joi");

// Sub-schema for individual trigger conditions
const triggerConditionSchema = Joi.object({
  option_ids: Joi.array().items(Joi.number().positive()).when("operator", {
    is: "OPTION_SELECTED",
    then: Joi.required(),
    otherwise: Joi.forbidden(),
  }),

  condition_type: Joi.string().valid("AND", "OR").when("option_ids", {
    is: Joi.exist(),
    then: Joi.required(),
    otherwise: Joi.forbidden(),
  }),

  condition_value: Joi.alternatives().conditional("operator", {
    switch: [
      {
        is: "EQUALS",
        then: Joi.string().required(),
      },
      {
        is: Joi.valid("GREATER_THAN", "LESS_THAN"),
        then: Joi.number().required(),
      },
      {
        is: "CONTAINS",
        then: Joi.string().required(),
      },
      {
        is: "OPTION_SELECTED",
        then: Joi.forbidden(),
      },
    ],
  }),

  operator: Joi.string()
    .valid(
      "EQUALS",
      "GREATER_THAN",
      "LESS_THAN",
      "CONTAINS",
      "OPTION_SELECTED",
      "NOT_EQUALS",
      "ALWAYS"
    )
    .required(),
});

// Main schema for mapQuestionDependency function
const mapQuestionDependencySchema = Joi.object({
  parentQuestionId: Joi.number().positive().required().messages({
    "number.base": "Parent question ID must be a number",
    "number.positive": "Parent question ID must be positive",
    "any.required": "Parent question ID is required",
  }),

  childQuestionId: Joi.number()
    .positive()
    .invalid(Joi.ref("parentQuestionId")) // Prevent self-referencing
    .required()
    .messages({
      "number.base": "Child question ID must be a number",
      "number.positive": "Child question ID must be positive",
      "any.required": "Child question ID is required",
      "any.invalid": "Child question cannot reference itself",
    }),

  triggerConditions: Joi.array()
    .items(triggerConditionSchema)
    .min(1)
    .required()
    .messages({
      "array.base": "Trigger conditions must be an array",
      "array.min": "At least one trigger condition is required",
      "any.required": "Trigger conditions are required",
    }),

  logicalOperator: Joi.string().valid("AND", "OR").default("AND").messages({
    "string.base": "Logical operator must be a string",
    "any.only": 'Logical operator must be either "AND" or "OR"',
  }),
});

// Validation schemas
const deactivateDependencySchema = Joi.object({
  dependencyId: Joi.number().positive().required().messages({
    "number.base": "Dependency ID must be a number",
    "number.positive": "Dependency ID must be positive",
    "any.required": "Dependency ID is required",
  }),
});

const updateDependencySchema = Joi.object({
  dependencyId: Joi.number().positive().required().messages({
    "number.base": "Dependency ID must be a number",
    "number.positive": "Dependency ID must be positive",
    "any.required": "Dependency ID is required",
  }),
  triggerConditions: Joi.array()
    .items(triggerConditionSchema)
    .min(1)
    .required()
    .messages({
      "array.base": "Trigger conditions must be an array",
      "array.min": "At least one trigger condition is required",
      "any.required": "Trigger conditions are required",
    }),
  logicalOperator: Joi.string().valid("AND", "OR").default("AND").messages({
    "string.base": "Logical operator must be a string",
    "any.only": 'Logical operator must be either "AND" or "OR"',
  }),
});

module.exports = {
  mapQuestionDependencySchema,
  deactivateDependencySchema,
  updateDependencySchema,
};
