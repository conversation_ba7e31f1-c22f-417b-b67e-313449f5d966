const joi = require("joi");
const { CONSTANTS } = require("../helpers/constants");

const defineRatePerPointsSchema = joi.object({
    points: joi
        .number()
        .required(),
    rate: joi
        .number()
        .required(),
});

const getRateOfPointsSchema = joi.object({
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
    status: joi
        .number()
        .valid(0, 1)
        .optional(),
});

const definePointsPerWoTypeSchema = joi.object({
    work_order_type_id: joi
        .number()
        .required(),
    points_to_assign: joi
        .number()
        .required(),
    transaction_type: joi
        .string()
        .valid(...Object.values(CONSTANTS.POINTS.TRANSACTION_TYPE))
        .optional()
});

const getPointsSchema = joi.object({
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
    search: joi
        .string()
        .optional(),
});

const getPointsByWorkOrderTypeSchema = joi.object({
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
    wo_type_id: joi
        .number()
        .required(),
});

const getPointsHistoryOfUserSchema = joi.object({
    user_id: joi
        .number()
        .required(),
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
});

const getBalanceOfUserSchema = joi.object({
    user_id: joi
        .number()
        .required(),
});

const redeemEarningSchema = joi.object({
    user_id: joi
        .number()
        .required(),
    amount: joi
        .number()
        .min(1)
        .required(),
    note: joi
        .string()
        .required()
});

const redeemedEarningHistorySchema = joi.object({
    user_id: joi
        .number()
        .required(),
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
});

module.exports = {
    defineRatePerPointsSchema,
    getRateOfPointsSchema,
    definePointsPerWoTypeSchema,
    getPointsSchema,
    getPointsByWorkOrderTypeSchema,
    getPointsHistoryOfUserSchema,
    getBalanceOfUserSchema,
    redeemEarningSchema,
    redeemedEarningHistorySchema,
}
