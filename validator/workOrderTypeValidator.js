const joi = require("joi");

const createSchema = joi.object({
    title: joi
        .string()
        .min(3)
        .required(),
    skill_ids: joi
        .array()
        .required(),
});

const updateSchema = joi.object({
    id: joi
        .number()
        .required(),
    title: joi
        .string()
        .min(3)
        .required(),
});

const getSchema = joi.object({
    id: joi
        .number()
        .optional(),
    search: joi
        .string()
        .min(3)
        .optional(),
    status: joi
        .number()
        .valid(0, 1)
        .optional(),
    page: joi
        .number()
        .required(),
    limit: joi
        .number()
        .required(),
});

const changeStatusSchema = joi.object({
    id: joi
        .number()
        .required(),
    status: joi
        .number()
        .valid(0, 1)
        .required(),
});

module.exports = {
    createSchema,
    updateSchema,
    getSchema,
    changeStatusSchema,
}
