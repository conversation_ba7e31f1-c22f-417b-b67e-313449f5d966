const joi = require("joi");

const createSchema = joi.object({
  work_order_type_id: joi.number().required(),
  scope_id: joi.number().required(),
  mapping_data: joi
    .array()
    .min(1)
    .items({
      touch_point: joi.number().required(),
      question_ids: joi.array().min(1).items(joi.number()).required(),
    })
    .required(),
});

const changeStatusSchema = joi.object({
  id: joi.number().required(),
  status: joi.number().valid(0, 1).required(),
});

const getSchema = joi.object({
  search: joi.string().optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
});

const getByWoTypeScopeSchema = joi.object({
  work_order_type_id: joi.number().required(),
  scope_id: joi.number().required(),
  task_id: joi.number().optional(),
});

const editSchema = joi.object({
  question_set: joi
    .array()
    .min(1)
    .items({
      touchpoint_id: joi.number().required(),
      new_question_ids: joi
        .array()
        .items(joi.alternatives().try(joi.string(), joi.number()))
        .required(),
      delete_question_ids: joi
        .array()
        .items(joi.alternatives().try(joi.string(), joi.number()))
        .required(),
    })
    .required(),
});

module.exports = {
  createSchema,
  changeStatusSchema,
  getSchema,
  getByWoTypeScopeSchema,
  editSchema,
};
