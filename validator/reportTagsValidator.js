const joi = require("joi");

const create = joi.object({
  name: joi.string().required(),
  is_mandatory: joi.boolean().optional(),
  number: joi.number().optional(),
});

const get = joi.object({
  id: joi.number().optional(),
  search: joi.string().min(3).optional(),
  page: joi.number().optional(),
  limit: joi.number().optional(),
  is_active: joi.number().valid(0, 1).optional(),
});

const edit = joi.object({
  id: joi.number().required(),
  name: joi.string().optional(),
  is_mandatory: joi.boolean().optional(),
  is_active: joi.boolean().optional(),
  number: joi.number().optional(),
});

module.exports = {
  create,
  get,
  edit,
};
