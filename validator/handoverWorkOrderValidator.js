const joi = require("joi");
const { CONSTANTS } = require("../helpers/constants");

const createSchema = joi.object({
  order_id: joi.number().required(),
});

const updateSchema = joi.object({
  id: joi.number().required(),
  assigned_to_user_id: joi.number().optional(),
  assigned_to_user_role_id: joi.number().optional(),
  scheduled_start_date: joi
    .date() // MM/DD/YYYY
    .optional(),
  scheduled_start_time: joi.string().optional(),
});

const getSchema = joi.object({
  work_order_id: joi.number().optional(),
  order_id: joi.number().optional(),
  work_order_status: joi.number().optional(),
  assigned_to_user_id: joi.number().optional(),
  search: joi.string().optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
  order_by: joi
    .string()
    .valid(...Object.values(CONSTANTS.WORK_ORDER.ORDER_BY))
    .optional(),
  sort: joi
    .string()
    .valid(...Object.values(CONSTANTS.SORT_BY))
    .optional(),
});

const getByIdSchema = joi.object({
  id: joi.string().required(),
  is_encrypted: joi.boolean().optional(),
});

const submitAnswerSchema = {
  body: joi.object({
    work_order_id: joi.string().required(),
    is_encrypted: joi.boolean().optional(),
    question_answer: joi
      .array()
      .min(1)
      .items({
        question_id: joi.number().required(),
        answers: joi
          .array()
          .min(1)
          .items(joi.alternatives().try(joi.string(), joi.number()))
          .required(),
        comment: joi.string().optional(),
        image_answers_ids: joi.array().items(joi.number()).optional(),
      })
      .required(),
  }),
};

const checkInSchema = joi.object({
  work_order_id: joi.number().required(),
});

const sendOtpSchema = joi.object({
  work_order_id: joi.string().required(),
  is_encrypted: joi.boolean().optional(),
});

const verifyOtpSchema = joi.object({
  work_order_id: joi.string().required(),
  otp: joi.string().required(),
  is_encrypted: joi.boolean().optional(),
});

const addSignatureSchema = {
  body: joi.object({
    work_order_id: joi.string().required(),
    is_encrypted: joi.boolean().optional(),
    is_encrypted: joi.boolean().optional(),
  }),
  signature: joi.any().required(),
};

const rejectWorkrderSchema = joi.object({
  work_order_id: joi.string().required(),
  is_encrypted: joi.boolean().optional(),
});

module.exports = {
  createSchema,
  getSchema,
  getByIdSchema,
  submitAnswerSchema,
  checkInSchema,
  sendOtpSchema,
  verifyOtpSchema,
  addSignatureSchema,
  updateSchema,
  rejectWorkrderSchema,
};
