const Joi = require("joi");

// Define the Joi schema for is_attachment_required validation
const isAttachmentRequiredSchema = Joi.alternatives().try(
  Joi.array().items(
    Joi.array().items(
      Joi.object({
        type: Joi.string()
          .valid(
            "Equals",
            "Contains",
            "Greater_than",
            "Less_than",
            "Not_Required"
          )
          .required(),
        value: Joi.array().items(Joi.string()).required(),
      })
    )
  ),
  Joi.array().length(0) // This allows for a single empty array
);

const questionConditionValidatorSchema = Joi.alternatives().try(
  Joi.array().items(
    Joi.array().items(
      Joi.object({
        type: Joi.string()
          .valid("Equals", "Contains", "Greater_than", "Less_than")
          .required(),
        value: Joi.array().items(Joi.string()).required(),
      })
    )
  ),
  Joi.array().length(0) // This allows for a single empty array
);

module.exports = {
  isAttachmentRequiredSchema,
  questionConditionValidatorSchema,
};
