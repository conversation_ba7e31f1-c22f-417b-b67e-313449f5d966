const joi = require("joi");
const { CONSTANTS } = require("../helpers/constants");

const createSchema = joi.object({
  order_id: joi.number().required(),
  customer_id: joi.number().required(),
  work_order_type_id: joi.number().required(),
  space_scopes: joi
    .array()
    .min(1)
    .items({
      space_id: joi.number().required(),
      scopes: joi.array().min(1).items({
        scope_id: joi.number().required(),
        scope_count: joi.number().required(),
      }),
    })
    .required(),
  assigned_to_user_id: joi.number().required(),
  assigned_to_user_role_id: joi.number().required(),
  instructions: joi.string().optional(),
  scheduled_start_date: joi
    .date() // MM/DD/YYYY
    .required(),
  scheduled_start_time: joi.string().required(),
  scheduled_duration: joi.string().required(),
});

const getSchema = joi.object({
  work_order_id: joi.number().optional(),
  order_id: joi.number().optional(),
  work_order_status_id: joi.number().optional(),
  assigned_to_user_id: joi.number().optional(),
  search: joi.string().optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
  order_by: joi
    .string()
    .valid(...Object.values(CONSTANTS.WORK_ORDER.ORDER_BY))
    .optional(),
  sort: joi
    .string()
    .valid(...Object.values(CONSTANTS.SORT_BY))
    .optional(),
});

const updateSchema = joi.object({
  work_order_id: joi.number().required(),
  assigned_to_user_id: joi.number().optional(),
  assigned_to_user_role_id: joi.number().optional(),
  scheduled_start_date: joi
    .date() // MM/DD/YYYY
    .optional(),
  scheduled_start_time: joi.string().optional(),
  scheduled_duration: joi.string().optional(),
  instructions: joi.string().optional(),
});

const getAssignedWorkOrderSchema = joi.object({
  id: joi.number().optional(),
  work_order_status_id: joi
    .string()
    .custom((value, helpers) => {
      try {
        const parsed = JSON.parse(value);
        if (!Array.isArray(parsed)) {
          throw new Error();
        }
        return parsed;
      } catch (error) {
        return helpers.error("any.invalid");
      }
    })
    .optional()
    .messages({
      "any.invalid": "Invalid format. Use ?work_order_status_id=[1,2,3]",
    }),
  page: joi.number().required(),
  limit: joi.number().required(),
});

const getAssignedWorkOrderWithoutPaginationSchema = joi.object({
  id: joi.number().optional(),
  work_order_status_id: joi
    .string()
    .custom((value, helpers) => {
      try {
        const parsed = JSON.parse(value);
        if (!Array.isArray(parsed)) {
          throw new Error();
        }
        return parsed;
      } catch (error) {
        return helpers.error("any.invalid");
      }
    })
    .optional()
    .messages({
      "any.invalid": "Invalid format. Use ?work_order_status_id=[1,2,3]",
    }),
});

const checkInSchema = {
  body: joi.object({
    work_order_id: joi.number().required(),
    internal_team_id: joi.number().optional(),
    date: joi
      .date() // MM/DD/YYYY
      .optional(),
    start_time: joi.string().optional(),
  }),
  selfie: joi.any().required(),
};

const checkOutSchema = joi.object({
  work_order_id: joi.number().required(),
});

const sendOtpForSubmitWorkOrderSchema = joi.object({
  work_order_id: joi.number().required(),
  country_code: joi.string().required(),
  mobile: joi.string().required(),
});

const verifyOtpForSubmitWorkOrderSchema = joi.object({
  work_order_id: joi.number().required(),
  country_code: joi.string().required(),
  mobile: joi.string().required(),
  otp: joi.string().required(),
});

const getReviewListSchema = joi.object({
  page: joi.number().required(),
  limit: joi.number().required(),
});

const reviewSchema = joi.object({
  work_order_id: joi.number().required(),
  order_id: joi.number().required(),
  review_status: joi.number().valid(0, 1).required(),
  reason: joi.string().optional().allow(null, ""),
  task_details: joi.alternatives().conditional("review_status", {
    is: 0,
    then: joi
      .array()
      // .min(1)
      .items({
        id: joi.number().required(),
        status: joi.number().valid(0, 1).required(),
        reason: joi.string().optional().allow(null, ""),
      }),
    otherwise: joi.optional(),
  }),
});

const sendSurveyPdfToZohoSchema = joi.object({
  action: joi
    .string()
    .valid(...Object.values(CONSTANTS.PDF.SURVEY.ACTION))
    .required(),
  work_order_id: joi.number().required(),
});

const deleteWorkOrderSchema = joi.object({
  work_order_id: joi.number().required(),
});

const acceptRejectTagRequestWorkOrderSchema = joi.object({
  work_order_id: joi.number().required(),
  status: joi.boolean().required(),
  alert_id: joi.number().optional(),
});

const reviewQuestionTouchPointSchema = joi.object({
  work_order_id: joi.number().required(),
  task_id: joi.number().required(),
  question_details: joi
    .array()
    .items({
      id: joi.number().required(),
      status: joi.number().valid(0, 1).required(),
      touch_point: joi.number().required(),
      reason: joi.string().optional().allow(null, ""),
    })
    .optional(),
  touch_point_details: joi
    .array()
    .items({
      id: joi.number().required(),
      status: joi.number().valid(0, 1).required(),
      reason: joi.string().optional().allow(null, ""),
    })
    .optional(),
});

const seShCheckInSchema = joi.object({
  work_order_id: joi.number().required(),
});

const cancelSeShCheckInSchema = joi.object({
  id: joi.number().required(),
});

const seShTagRequestSchema = joi.object({
  work_order_id: joi.number().required(),
  internal_team_id: joi.number().required(),
});

module.exports = {
  createSchema,
  getSchema,
  updateSchema,
  getAssignedWorkOrderSchema,
  checkInSchema,
  checkOutSchema,
  sendOtpForSubmitWorkOrderSchema,
  verifyOtpForSubmitWorkOrderSchema,
  getReviewListSchema,
  reviewSchema,
  sendSurveyPdfToZohoSchema,
  deleteWorkOrderSchema,
  acceptRejectTagRequestWorkOrderSchema,
  getAssignedWorkOrderWithoutPaginationSchema,
  reviewQuestionTouchPointSchema,
  seShCheckInSchema,
  cancelSeShCheckInSchema,
  seShTagRequestSchema,
};
