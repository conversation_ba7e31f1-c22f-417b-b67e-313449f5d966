const joi = require("joi");

const triggerConditionSchema = joi.object({
  option_ids: joi.array().items(joi.number().positive()).when("operator", {
    is: "OPTION_SELECTED",
    then: joi.required(),
    // otherwise: joi.forbidden(),
  }),

  condition_type: joi.string().valid("AND", "OR").when("option_ids", {
    is: joi.exist(),
    then: joi.required(),
    otherwise: joi.forbidden(),
  }),

  condition_value: joi.alternatives().conditional("operator", {
    switch: [
      {
        is: "EQUALS",
        then: joi.string().required(),
      },
      {
        is: joi.valid("GREATER_THAN", "LESS_THAN"),
        then: joi.number().required(),
      },
      {
        is: "NOT_EQUALS",
        then: joi.string().required(),
      },
      {
        is: "CONTAINS",
        then: joi.string().required(),
      },
      {
        is: "OPTION_SELECTED",
        then: joi.forbidden(),
      },
      {
        is: "ALWAYS",
        then: joi.string().optional().allow("", null),
      },
    ],
  }),

  operator: joi
    .string()
    .valid(
      "EQUALS",
      "GREATER_THAN",
      "LESS_THAN",
      "CONTAINS",
      "OPTION_SELECTED",
      "NOT_EQUALS",
      "ALWAYS"
    )
    .required(),
});

const mapQuestionDependencySchema = joi.object({
  childQuestionId: joi
    .number()
    .positive()
    .invalid(joi.ref("parentQuestionId")) // Prevent self-referencing
    .required()
    .messages({
      "number.base": "Child question ID must be a number",
      "number.positive": "Child question ID must be positive",
      "any.required": "Child question ID is required",
      "any.invalid": "Child question cannot reference itself",
    }),
  id: joi.number().positive().optional(),
  triggerConditions: joi
    .array()
    .items(triggerConditionSchema)
    .min(1)
    .required()
    .messages({
      "array.base": "Trigger conditions must be an array",
      "array.min": "At least one trigger condition is required",
      "any.required": "Trigger conditions are required",
    }),

  logicalOperator: joi.string().valid("AND", "OR").default("AND").messages({
    "string.base": "Logical operator must be a string",
    "any.only": 'Logical operator must be either "AND" or "OR"',
  }),
});

const createSchema = joi.object({
  que_type_id: joi.number().required(),
  report_tag_id: joi.number().optional(),
  title: joi.string().required(),
  options: joi.array().min(1).optional(),
  attachment_required: joi.number().valid(0, 1).required(),
  is_numeric: joi.number().valid(0, 1).optional(),
  is_video_required: joi.number().valid(0, 1).optional(),
  is_attachment_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .when(joi.ref("/que_type_id"), {
                is: 2,
                then: joi.valid("Equals", "Not equals", "Contains", "Always"),
                otherwise: joi.valid(
                  "Equals",
                  "Not equals",
                  "Contains",
                  "Greater than",
                  "Less than",
                  "Always"
                ),
              })
              .required(),
            value: joi.alternatives().conditional("type", {
              switch: [
                {
                  is: "Always",
                  then: joi.string().optional().allow("", null),
                },
                {
                  is: joi.valid("Greater than", "Less than"),
                  then: joi.number().required(),
                },
              ],
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
  video_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .when(joi.ref("/que_type_id"), {
                is: 2,
                then: joi.valid("Equals", "Not equals", "Contains", "Always"),
                otherwise: joi.valid(
                  "Equals",
                  "Not equals",
                  "Contains",
                  "Greater than",
                  "Less than",
                  "Always"
                ),
              })
              .required(),
            value: joi.alternatives().conditional("type", {
              switch: [
                {
                  is: "Always",
                  then: joi.string().optional().allow("", null),
                },
                {
                  is: joi.valid("Greater than", "Less than"),
                  then: joi.number().required(),
                },
              ],
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
  dependencies: joi
    .array()
    .items(mapQuestionDependencySchema) // Allows nested dependency objects
    .optional()
    .messages({
      "array.base": "Dependencies must be an array",
    }),
  comment_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .valid("Equals", "Not equals", "Always")
              .required(),
            value: joi.alternatives().conditional("type", {
              switch: [
                {
                  is: "Always",
                  then: joi.string().optional().allow("", null),
                },
              ],
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
});

const getSchema = joi.object({
  id: joi.number().optional(),
  que_type_id: joi.number().optional(),
  search: joi.string().min(3).optional(),
  status: joi.number().valid(0, 1).optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
});

const updateSchema = joi.object({
  id: joi.number().required(),
  title: joi.string().optional(),
  options: joi.array().min(1).optional(),
  attachment_required: joi.number().valid(0, 1).optional(),
  is_video_required: joi.number().valid(0, 1).optional(),
  report_tag_id: joi.number().optional(),
  is_attachment_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .valid(
                "Equals",
                "Not equals",
                "Contains",
                "Greater than",
                "Less than",
                "Always"
              )
              .required(),
            value: joi.string().when("type", {
              is: "Always",
              then: joi.string().optional().allow("", null),
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
  video_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .valid(
                "Equals",
                "Not equals",
                "Contains",
                "Greater than",
                "Less than",
                "Always"
              )
              .required(),
            value: joi.string().when("type", {
              is: "Always",
              then: joi.string().optional().allow("", null),
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
  dependencies: joi
    .array()
    .items(mapQuestionDependencySchema)
    .optional()
    .messages({
      "array.base": "Dependencies must be an array",
    }),
  comment_required: joi
    .object({
      required: joi.boolean().required(),
      logicalOperator: joi.string().valid("AND", "OR").when("required", {
        is: true,
        then: joi.required(),
        otherwise: joi.optional(),
      }),
      conditions: joi
        .array()
        .items(
          joi.object({
            type: joi
              .string()
              .valid("Equals", "Not equals", "Always")
              .required(),
            value: joi.alternatives().conditional("type", {
              switch: [
                {
                  is: "Always",
                  then: joi.string().optional().allow("", null),
                },
              ],
              otherwise: joi.string().required(),
            }),
          })
        )
        .min(1)
        .when("required", {
          is: true,
          then: joi.required(),
          otherwise: joi.optional(),
        }),
    })
    .optional(),
});

const changeStatuSchema = joi.object({
  id: joi.number().required(),
  status: joi.number().valid(0, 1).required(),
});

module.exports = {
  createSchema,
  getSchema,
  updateSchema,
  changeStatuSchema,
};
