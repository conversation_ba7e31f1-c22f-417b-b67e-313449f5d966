const joi = require("joi");

const { CONSTANTS } = require("../helpers/constants");

const createSchema = {
  body: joi.object({
    scope_id: joi.number().optional(),
    model: joi.string().required(),
    name: joi.string().required(),
    touch_points: joi
      .array()
      .min(1)
      .items({
        touch_point: joi.number().optional(),
        x_coordinate: joi.number().optional(),
        y_coordinate: joi.number().optional(),
        title: joi.string().optional(),
      })
      .required(),
    scope_image: joi
      // .string()
      .any()
      .optional(),
  }),
  scope_image: joi
    // .string()
    .any()
    .required(),
};

/*
const updateSchema = joi.object({
    id: joi
        .number()
        .required(),
    title: joi
        .string()
        .required(),
});
*/

const getSchema = joi.object({
  id: joi.number().optional(),
  search: joi.string().min(2).optional(),
  status: joi.number().valid(0, 1).optional(),
  source: joi
    .number()
    .valid(CONSTANTS.DATA_SOURCE.CMS, CONSTANTS.DATA_SOURCE.ZOHO)
    .optional(),
  page: joi.number().optional(),
  limit: joi.number().optional(),
  order_id: joi.number().optional(),
});

const changeStatusSchema = joi.object({
  id: joi.number().required(),
  status: joi.number().valid(0, 1).required(),
});

module.exports = {
  createSchema,
  // updateSchema,
  getSchema,
  changeStatusSchema,
};
