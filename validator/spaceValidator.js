const joi = require("joi");

const { CONSTANTS } = require("../helpers/constants");

const createSchema = joi.object({
  title: joi.string().required(),
  source: joi
    .string()
    .valid(...Object.values(CONSTANTS.DATA_SOURCE))
    .optional(),
});

const updateSchema = joi.object({
  id: joi.number().required(),
  title: joi.string().required(),
});

const getSchema = joi.object({
  id: joi.number().optional(),
  search: joi.string().min(3).optional(),
  status: joi.number().valid(0, 1).optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
  order_id: joi.number().optional(),
});

const changeStatusSchema = joi.object({
  id: joi.number().required(),
  status: joi.number().valid(0, 1).required(),
});

module.exports = {
  createSchema,
  updateSchema,
  getSchema,
  changeStatusSchema,
};
