const joi = require("joi");

const isAssignedWorkOrderCompletedSchema = joi.object({
  user_id: joi.number().required(),
  role: joi
    .object()
    // .items({
    //     id: joi
    //         .number()
    //         .required(),
    //     name: joi
    //         .string()
    //         .required(),
    // })
    .required(),
});

const getAssignedOrdersSchema = joi.object({
  fabricator_ids: joi.array().min(1).items(joi.number()).required(),
});

const getAssignedOrdersByStatusSchema = joi.object({
  fabricator_ids: joi.array().min(1).items(joi.number()).required(),
  order_status_title: joi.string().required(),
});

const getAssignedWorkOrdersSchema = joi.object({
  supervisor_ids: joi.array().min(1).items(joi.number()).required(),
});

const updateFabricatorInOrderSchema = joi.object({
  fabricator_email: joi.string().email().required(),
  fabricator_id: joi.number().required(),
});

module.exports = {
  isAssignedWorkOrderCompletedSchema,
  getAssignedOrdersSchema,
  getAssignedWorkOrdersSchema,
  updateFabricatorInOrderSchema,
  getAssignedOrdersByStatusSchema,
};
