const joi = require("joi");
const { CONSTANTS } = require("../helpers/constants");

const getSchema = joi.object({
  work_order_id: joi.number().required(),
  que_required: joi.number().valid(0, 1).optional(),
  status: joi
    .string()
    .custom((value, helpers) => {
      try {
        const parsed = JSON.parse(value);
        if (!Array.isArray(parsed)) {
          throw new Error();
        }

        // Validate that all values are within the allowed statuses
        if (
          !parsed.every((status) =>
            Object.values(CONSTANTS.TASK_STATUS).includes(status)
          )
        ) {
          return helpers.error("any.invalid");
        }
        return parsed;
      } catch (error) {
        return helpers.error("any.invalid");
      }
    })
    .optional()
    .messages({
      "any.invalid":
        "Invalid format. Use ?status=[Pending, Completed, Rejected, Not Reviewed]",
    }),
});

const submitAnswerSchema = joi.object({
  task_id: joi.number().required(),
  additional_info: joi.string().optional(),
  task_data: joi
    .array()
    .min(1)
    .items({
      touch_point: joi.number().required(),
      image_answers_ids: joi
        .array()
        .items({
          report_tag_id: joi.number().required(),
          id: joi.array().min(1).items(joi.number()).required(),
        })
        .optional(),
      question_answer: joi
        .array()
        .min(1)
        .items({
          question_id: joi.number().required(),
          answers: joi
            .array()
            .min(1)
            .items(joi.alternatives().try(joi.string(), joi.number()))
            .required(),
          image_answers_ids: joi
            .array()
            // .min(1)
            .items(joi.number())
            .optional(),
          comment:joi.string().optional(),
          video_answer_id: joi.number().optional(),
          isSubquestion: joi.boolean().optional(),
          parent_question_id: joi
            .alternatives()
            .try(joi.number(), joi.valid(null))
            .optional(),
        })
        .required(),
    })
    .optional(),
});

const uploadImageAnswerSchema = {
  body: joi.object({
    task_id: joi.number().required(),
    touch_point: joi.number().required(),
    question_id: joi.number().optional(),
    report_tag_id: joi.number().optional(),
  }),
  answer_image: joi.string().required(),
};

const generateTaskAnswerSignedUrlSchema = joi.object({
  task_id: joi.number().required(),
  touch_point: joi.number().required(),
  question_id: joi.number().required(),
});

const updateVideoUrlSchema = joi.object({
  id: joi.number().required(),
});

const getReviewSchema = joi.object({
  task_id: joi.number().required(),
});

module.exports = {
  getSchema,
  submitAnswerSchema,
  uploadImageAnswerSchema,
  generateTaskAnswerSignedUrlSchema,
  getReviewSchema,
  updateVideoUrlSchema,
};
