const joi = require("joi");

const getSchema = joi.object({
  id: joi.number().optional(),
  order_status_id: joi.number().optional(),
  fabricator_id: joi.number().optional(),
  search: joi.string().optional(),
  page: joi.number().required(),
  limit: joi.number().required(),
  is_create: joi.boolean().optional(),
});

const getSpaceScopeSchema = joi.object({
  id: joi.number().required(),
  work_order_type: joi.number().required(),
});

const fetchZohoOrderSchema = joi.object({
  // opportunity_ids: joi
  //     .array()
  //     .required(),
});

const syncOrderSchema = joi.object({
  page: joi.number().required(),
  limit: joi.number().required(),
});

const uploadImageSchema = {
  body: joi.object({
    order_id: joi.number().required(),
  }),
  order_image: joi.string().required(),
};

const sendHandoverEmailSchema = joi.object({
  order_id: joi.number().required(),
});

module.exports = {
  getSchema,
  fetchZohoOrderSchema,
  syncOrderSchema,
  getSpaceScopeSchema,
  uploadImageSchema,
  sendHandoverEmailSchema,
};
