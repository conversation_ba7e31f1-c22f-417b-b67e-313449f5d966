const validateParams = (schema) => async (request, response, next) => {
    try {
        schema = schema?.body || schema;
        let params;
        if (request.method === "GET") {
            params = request.query
        }
        else if (request.method === "POST") {
            params = request.body
        }

        const isValid = await schema.validate(params);

        if (isValid.error) {
            return response.handler.validationError(isValid.error.details[0].message);
        }

        return next();
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    validateParams
}
