const axios = require("axios");
const { isEmpty } = require("lodash");

const { API_PATH } = require("../helpers/apiEndPoints");
const { MESSAGE } = require("../helpers/messages");

const authenticateUser = async (request, response, next) => {
  try {
    const jwtToken = request?.headers?.token;
    if (!jwtToken) {
      return response.handler.unauthorized(MESSAGE.TOKEN_NOT_FOUND);
    }

    const config = {
      method: "GET",
      url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.AUTHENTICATE_USER}`,
      headers: {
        token: request?.headers?.token,
        platform: request?.headers?.platform,
      },
    };

    await axios(config)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (
            apiResponse.data?.status === 1 &&
            !isEmpty(apiResponse?.data?.data)
          ) {
            request.login_user = await apiResponse?.data?.data;
            next();
          } else if (apiResponse?.data?.status === 0) {
            return response.handler.unauthorized(apiResponse.data.message);
          } else {
            return response.handler.badRequest(MESSAGE.GENERAL_ERROR);
          }
        } else {
          return response.handler.badRequest(MESSAGE.AUTHORIZATION_ERROR);
        }
      })
      .catch((error) => {
        return response.handler.unauthorized(
          error?.response?.data?.message || error.message
        );
      });
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  authenticateUser,
};
