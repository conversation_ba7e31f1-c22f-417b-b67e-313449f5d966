const { CONSTANTS } = require("../helpers/constants");
const { MESSAGE } = require("../helpers/messages");

const validatePlatformHeader = (request, response, next) => {
    if (!request.headers[CONSTANTS.HEADERS.PLATFORM.KEY]) {
        return response.handler.badRequest(MESSAGE.HEADER.MISSING);
    }
    else if (![CONSTANTS.HEADERS.PLATFORM.VALUES.ANDROID, CONSTANTS.HEADERS.PLATFORM.VALUES.IOS, CONSTANTS.HEADERS.PLATFORM.VALUES.WEB].includes(request.headers[CONSTANTS.HEADERS.PLATFORM.KEY])) {
        return response.handler.badRequest(MESSAGE.HEADER.INVALID);
    }

    next();
}

module.exports = {
    validatePlatformHeader,
}
