const { MESSAGE } = require("../helpers/messages");

const roleAccess = (roles) => async (request, response, next) => {
    try {
        const loggedInUser = await request?.login_user?.data;

        if (
            roles.length
            && loggedInUser?.role?.name
            && roles.includes(loggedInUser?.role?.name)
        ) {
            next();
        }
        else {
            return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    roleAccess,
}
