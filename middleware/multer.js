const multer = require("multer");
const path = require("path");
const os = require("os");
const tmpdir = os.tmpdir();

const { CONSTANTS } = require("../helpers/constants");

const storage = multer.diskStorage({
  destination: (request, file, cb) => cb(null, tmpdir), // cb -> callback
  filename: (request, file, cb) => {
    const uniqueName = `${Date.now()}-${Math.round(
      Math.random() * 1e9
    )}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  },
});

const uploadSingleFile = (
  fileKey,
  maxLimit = CONSTANTS.FILE_SIZE_LIMIT.DEFAULT_MAX
) => {
  const upload = multer({
    storage: storage,
    limits: { fileSize: maxLimit * 1024 * 1024 },
  }).single(fileKey);

  return (request, response, next) => {
    upload(request, response, function (err) {
      if (err instanceof multer.MulterError) {
        return response.handler.badRequest(
          `Maximum file size should be less then ${maxLimit} MB`
        );
      } else if (err) {
        return response.handler.badRequest(
          "An error occurred while uploading the file."
        );
      }

      next();
    });
  };
};

const uploadAnyFiles = (maxLimit = CONSTANTS.FILE_SIZE_LIMIT.DEFAULT_MAX) => {
  const upload = multer({
    storage,
    limits: { fileSize: maxLimit * 1024 * 1024 },
  }).any(); // Accepts all files

  return (req, res, next) => {
    upload(req, res, (err) => {
      if (err instanceof multer.MulterError) {
        return res.handler.badRequest(
          `Maximum file size should be less than ${maxLimit} MB`
        );
      } else if (err) {
        return res.handler.badRequest(
          "An error occurred while uploading the files."
        );
      }
      next();
    });
  };
};

module.exports = {
  uploadSingleFile,
  uploadAnyFiles,
};
