const { Op } = require("sequelize");
const { isEmpty } = require("lodash");
const fs = require("fs");

const db = require("../database/models");

const {
  getUniqueAndNotNullValueFromArray,
  filterMobileNumber,
  getUniqueValueFromArray,
  getPadString,
  replaceData,
  encrypt,
} = require("../helpers/functions");
const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const common = require("./commonFunctions.js");
const aws = require("../helpers/awsS3.js");
const zoho = require("../helpers/zohoApis.js");
const { logger } = require("../helpers/logger");
const { sendMail } = require("../helpers/mail");
const { default: axios } = require("axios");
const { sendSMS } = require("../helpers/twilio.js");

const get = async (request, response) => {
  try {
    const {
      id,
      search,
      order_status_id,
      fabricator_id,
      is_create = false,
    } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    //check if requested work order status is exist or not.
    if (order_status_id) {
      const orderStatusExist = await common.getOrderStatusById(order_status_id);
      if (!orderStatusExist) {
        return response.handler.notFound(MESSAGE.ORDER.STATUS.NOT_EXIST);
      }
    }

    const loggedInUser = await request?.login_user?.data;

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];
    let customerWhereCond = [];
    id && whereCond.push({ id });
    order_status_id && whereCond.push({ order_status_id });

    if (fabricator_id) {
      /*
                - when we fetch the order from zoho at that time we are checcking the attached fabricator is available in database or not. If available then we will map the fabricator id with the order.
                - But When fetching the order from zoho, if fabricator is not available then fabricator will not mapped. So while fetching order in app we can check if there are any order which has fabricator_id is not mappeed with logged in fabricator's id. So we can mapp those orders.
            */
      await db.Orders.update(
        { fabricator_id },
        { where: { fabricator_id: 0, fabricator_email: loggedInUser.email } }
      );

      whereCond.push({ fabricator_id });
    }

    // only same zone orders will display
    loggedInUser?.zone?.name &&
      customerWhereCond.push({ zone: loggedInUser?.zone?.name });
    if (
      loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.FABRICATOR &&
      loggedInUser?.id
    ) {
      // only assigned order will display to fabricator.
      whereCond.push({ fabricator_id: fabricator_id || loggedInUser.id });
    }

    if (search) {
      const searchArr = [
        { "$Orders.title$": { [Op.like]: `%${search}%` } },
        { "$Orders.actual_order_id$": { [Op.like]: `%${search}%` } },
        { "$customer_data.email$": { [Op.like]: `%${search}%` } },
        { "$customer_data.name$": { [Op.like]: `%${search}%` } },
        { "$order_status.title$": { [Op.like]: `%${search}%` } },
      ];

      whereCond.push({ [Op.or]: searchArr });
    }

    const { count: totalCount, rows: ordersData } =
      await db.Orders.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        distinct: true,
        attributes: ATTRIBUTES.ORDERS,
        order: [["id", "DESC"]],
        include: [
          {
            model: db.Customers,
            as: "customer_data",
            where: customerWhereCond,
            required: true,
            attributes: ATTRIBUTES.CUSTOMERS,
          },
          {
            model: db.OrderStatus,
            as: "order_status",
            required: true,
            attributes: ATTRIBUTES.ORDER_STATUS,
          },
          {
            model: db.OrderSpaces,
            as: "order_spaces",
            required: false,
            attributes: ATTRIBUTES.ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
              {
                model: db.OrderScopes,
                as: "order_scopes",
                required: false,
                attributes: ATTRIBUTES.ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.SCOPES,
                      [
                        db.sequelize.fn(
                          "CONCAT",
                          db.sequelize.col("height"),
                          " ⨯ ",
                          db.sequelize.col("width")
                        ),
                        "dimension",
                      ],
                    ],
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                        include: [
                          {
                            model: db.ScopeImageTouchPoints,
                            as: "touch_points",
                            required: false,
                            attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.OrderImages,
            as: "order_images",
            required: false,
            attributes: ATTRIBUTES.ORDER_IMAGES,
            where: { is_active: 1, is_deleted: 0 },
          },
        ],
      });

    if (!ordersData) {
      return response.handler.badRequest(MESSAGE.ORDERS.FOUND.ERROR);
    } else if (totalCount === 0 || ordersData.length === 0) {
      return response.handler.notFound(MESSAGE.ORDERS.FOUND.FAILED);
    } else {
      const fabricator_user_ids = await ordersData.map(
        (od) => od.fabricator_id
      );

      // get fabricator user from user authentication service.
      let fabricatorData = [];
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: fabricator_user_ids,
        headers: request?.headers,
      });

      if (userAPI) {
        fabricatorData = userAPI;
      }

      let respData = [];
      for (let i = 0; i < ordersData.length; i++) {
        let fab = {};
        if (fabricatorData.length) {
          fab = await fabricatorData.find(
            (ele) => ele?.id === ordersData[i].fabricator_id
          );
        }

        // get count of total workorders which are created under order.
        const totalWorkOrdersCount = await db.WorkOrders.count({
          where: { order_id: ordersData[i]?.id },
        });

        // get count of completed work order
        const completedWorkOrderStatus = await common.getWorkOrderStatusByTitle(
          CONSTANTS.WORK_ORDER.STATUS.COMPLETED
        );

        const completedWorkOrdersCount = await db.WorkOrders.count({
          where: [
            { order_id: ordersData[i]?.id },
            { work_order_status_id: completedWorkOrderStatus.id },
          ],
        });
        if (is_create) {
          const workOrderData = await db.WorkOrders.findAll({
            where: { order_id: ordersData[i]?.id },
            include: [
              {
                model: db.WorkOrderSpaces,
                as: "work_order_spaces",
                attributes: ATTRIBUTES.WORK_ORDER_SPACES,
                include: [
                  {
                    model: db.WorkOrderScopes,
                    as: "work_order_scopes",
                    attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                  },
                ],
              },
            ],
          });

          if (workOrderData?.length) {
            const spaceData = workOrderData?.flatMap(
              (item) => item?.work_order_spaces
            );

            const resOrderSpaceData = [];
            ordersData[i]?.order_spaces?.forEach((space) => {
              const spaceIndex = spaceData?.findIndex(
                (val) => val?.space_id === space?.space_id
              );

              let orderSpaceData = { ...JSON.parse(JSON.stringify(space)) };

              if (spaceIndex !== -1) {
                const scopeId = spaceData[spaceIndex]?.work_order_scopes?.map(
                  (item) => item?.scope_id
                );

                const orderScopeData = orderSpaceData?.order_scopes?.filter(
                  (scope) => !scopeId.includes(scope?.scope_id)
                );

                orderSpaceData.order_scopes = orderScopeData;
              }
              if (orderSpaceData.order_scopes?.length)
                resOrderSpaceData.push(orderSpaceData);
            });
            respData.push({
              ...JSON.parse(JSON.stringify(ordersData[i])),
              order_spaces: resOrderSpaceData,
              work_orders_count: totalWorkOrdersCount,
              completed_work_orders_count: completedWorkOrdersCount,
              fabricator_data: fab,
            });
          } else {
            respData.push({
              ...JSON.parse(JSON.stringify(ordersData[i])),
              work_orders_count: totalWorkOrdersCount,
              completed_work_orders_count: completedWorkOrdersCount,
              fabricator_data: fab,
            });
          }
        } else {
          respData.push({
            ...JSON.parse(JSON.stringify(ordersData[i])),
            work_orders_count: totalWorkOrdersCount,
            completed_work_orders_count: completedWorkOrdersCount,
            fabricator_data: fab,
          });
        }
      }

      const orderRes = [];

      for (let i = 0; i < respData?.length; i++) {
        const masterOrderData = await db.MasterOrders.findOne({
          distinct: true,
          where: { zoho_order_id: ordersData[i]?.zoho_order_id },
          include: [
            {
              model: db.MasterOrderQuoteLineItems,
              as: "line_items_data",
              required: true,
              where: { is_active: 1, is_deleted: 0 },
              attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
            },
          ],
        });
        const resOrderSpaceData = [];

        ordersData[i]?.order_spaces?.forEach((space) => {
          let scopeName;
          const index = masterOrderData?.line_items_data?.findIndex(
            (master) => master?.room === space?.space_data?.title
          );
          if (index !== -1) {
            scopeName = masterOrderData?.line_items_data?.[index]?.product_name;
          }
          const scopeData = [];
          if (space?.order_scopes?.length) {
            space?.order_scopes?.forEach((scope) => {
              let tmpScopeData = { ...JSON.parse(JSON.stringify(scope)) };
              if (!scope?.scope_data?.is_part) {
                if (
                  scopeName &&
                  scope?.scope_data?.name !== scopeName &&
                  scope?.scope_data?.new_name === scopeName
                ) {
                  tmpScopeData = {
                    ...JSON.parse(JSON.stringify(scope)),
                    scope_data: {
                      ...JSON.parse(JSON.stringify(scope?.scope_data)),
                      name: scopeName,
                    },
                  };
                } else {
                  tmpScopeData = {
                    ...JSON.parse(JSON.stringify(scope)),
                  };
                }
                scopeData.push(tmpScopeData);
              }
            });
          }

          if (scopeData?.length) {
            resOrderSpaceData.push({
              ...JSON.parse(JSON.stringify(space)),
              order_scopes: scopeData,
            });
          }
        });

        orderRes.push({
          ...JSON.parse(JSON.stringify(respData[i])),
          order_spaces: resOrderSpaceData,
        });
      }

      return response.handler.success(MESSAGE.ORDERS.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: ordersData.length,
        orders: orderRes,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getStatus = async (request, response) => {
  try {
    const orderStatus = await db.OrderStatus.findAll({
      where: {
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ATTRIBUTES.ORDER_STATUS,
    });

    if (orderStatus) {
      return response.handler.success(MESSAGE.ORDER.STATUS.FOUND.SUCCESS, {
        order_status: orderStatus,
      });
    } else {
      return response.handler.badRequest(MESSAGE.ORDER.STATUS.FOUND.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const fetchZohoOrders = async (request, response) => {
  try {
    // const { opportunity_ids } = request.body;
    const fetch = await zoho.fetchAllOrders();
    return response.handler.success("Orders fetched successfully.");
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sync = async () => {
  try {
    const { count: totalCount, rows: masterOrderData } =
      await db.MasterOrders.findAndCountAll({
        where: { is_read: 0 },
        distinct: true,
        include: [
          {
            model: db.MasterOrderQuoteLineItems,
            as: "line_items_data",
            required: true,
            where: { is_active: 1, is_deleted: 0 },
            attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
          },
        ],
        logging: false,
      });

    if (totalCount === 0) {
      console.log("--- NO ORDERS AVAILABLE FOR SYNC ---");
      return {
        success: false,
        message: "No Orders available for sync.",
      };
    } else {
      const archivedWoStatus = await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.ARCHIVED
      );
      const newOrderStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.NEW
      );
      const completedOrderStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.COMPLETED
      );

      // get fabricator users from user service
      const fabricatorUsers =
        (await userService.getUsersByRoleName({
          headers: { platform: CONSTANTS.HEADERS.PLATFORM.VALUES.ANDROID },
          roles: [CONSTANTS.ROLES.NAMES.FABRICATOR],
        })) || [];

      for (let i = 0; i < masterOrderData.length; i++) {
        const masterOrder = masterOrderData[i];
        const lineItemData = masterOrder.line_items_data;
        const warranty_end_date = await getWarrantyEndDate(masterOrder);

        // check if same order id exist or not. if not then only create.
        const orderExist = await db.Orders.findOne({
          where: { zoho_order_id: masterOrder.zoho_order_id },
          include: [
            {
              model: db.Customers,
              as: "customer_data",
              required: true,
              attributes: ATTRIBUTES.CUSTOMERS,
            },
          ],
          logging: false,
        });

        const order_status_id =
          masterOrder?.stage === CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE
            ? completedOrderStatus.id
            : newOrderStatus.id;

        if (isEmpty(orderExist) && !orderExist) {
          let fab = {};
          if (masterOrder?.owner_email) {
            fab = await fabricatorUsers.find(
              (fab) => fab.email === masterOrder.owner_email.toLowerCase()
            );
          }

          const customer_id = await getCustomerID(masterOrder);

          // create order
          const addOrder = await db.Orders.create(
            {
              zoho_order_id: masterOrder?.zoho_order_id,
              zoho_stage: masterOrder?.stage,
              title: masterOrder?.deal_name,
              zoho_fabricator_id: masterOrder?.owner_id,
              fabricator_id: fab?.id || 0,
              fabricator_email: (masterOrder?.owner_email).toLowerCase(),
              customer_id: customer_id || null,
              order_status_id,
              zoho_warranty_id: masterOrder?.warranty_id,
              profile_warranty_end_date: warranty_end_date.profile,
              hardware_warranty_end_date: warranty_end_date.hardware,
              expected_date_of_survey: masterOrder?.expected_date_of_survey,
              amount: masterOrder?.amount,
              qc_mode: masterOrder?.qc_mode,
            },
            { logging: false }
          );

          if (addOrder?.id) {
            // update actual order id
            const actualOrderId = await getPadString(addOrder.id, "ORD");
            await db.Orders.update(
              { actual_order_id: actualOrderId },
              {
                where: { id: addOrder.id },
                logging: false,
              }
            );

            // general space & scope mapping with order.
            await mapGeneralSpaceAndScopeToOrder(addOrder.id);

            // order's spaces & scopes mapping
            await mapSpaceAndScopeToOrder(lineItemData, addOrder.id);

            // mark is_read 1 in master_order as it is synced.
            await updateMasterOrderIsReadTrue(masterOrder?.id);
          }
        } else {
          /*
                        1. Keep syncing the order and products until no work order has been created.
                        2. If work order type of survey has been created, keep syncing until that work order is submitted. After submission if there is any change (which is very likely the case), archive that survey work order and refresh all the products data from the order
                            - if survey wo not started then we need to delete the wo.
                        3. Once other type of work order is created. NO SYNCING will happen.
                    */

          let is_sync_required = false;
          let is_line_items_changed = false;

          /*
                        when we fetching order, at that time if line items changes, then we are marking previous line item as deleted. (is_deleted = 1).
                        So here, we will check for order that if order has any line item that is deleted or not. If data available with is_deleted = 1, that means line items is changed.
                    */
          const deletedMasterLineItem =
            await db.MasterOrderQuoteLineItems.findAll({
              where: {
                master_order_id: masterOrder.id,
                is_deleted: 1,
              },
              logging: false,
            });

          if (deletedMasterLineItem?.length) {
            is_line_items_changed = true;
          }

          // check if order has any work order or not.
          const workOrders = await db.WorkOrders.findAll({
            where: {
              order_id: orderExist.id,
              is_active: 1,
              is_deleted: 0,
            },
            include: [
              {
                model: db.WorkOrderTypes,
                as: "work_order_type",
                required: true,
                attributes: ATTRIBUTES.WORK_ORDER_TYPES,
              },
              {
                model: db.WorkOrderStatus,
                as: "work_order_status",
                required: true,
                attributes: ATTRIBUTES.WORK_ORDER_STATUS,
              },
            ],
          });

          // check if work order is available with only survey type or not.
          if (workOrders.length) {
            const woTypes = await workOrders.map(
              (wo) => wo.work_order_type.title
            );

            const hasOnlySurveyType = await woTypes.every(
              (element) => element === CONSTANTS.WORK_ORDER.TYPE.SURVEY
            );

            // when only survey work order type is there, then we need to update else for other type no update/sync required.
            if (hasOnlySurveyType) {
              is_sync_required = true;

              if (is_line_items_changed) {
                //check the status of survey type work order
                const surveyWorkOrders = await workOrders.filter(
                  (data) =>
                    data.work_order_type.title ===
                    CONSTANTS.WORK_ORDER.TYPE.SURVEY
                );

                for (let x = 0; x < surveyWorkOrders.length; x++) {
                  // if survey work order status is under-approval/partially-submitted/completed/rejected then only we need to archive else we need to delete
                  if (
                    [
                      CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL,
                      CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED,
                      CONSTANTS.WORK_ORDER.STATUS.COMPLETED,
                      CONSTANTS.WORK_ORDER.STATUS.REJECTED,
                    ].includes(surveyWorkOrders[x].work_order_status.title)
                  ) {
                    // change wo status to archive
                    await db.WorkOrders.update(
                      { work_order_status_id: archivedWoStatus.id },
                      {
                        where: { id: surveyWorkOrders[x].id },
                        logging: false,
                      }
                    );
                  } else {
                    // delete the work order
                    await db.WorkOrders.update(
                      { is_deleted: 1 },
                      {
                        where: { id: surveyWorkOrders[x].id },
                        logging: false,
                      }
                    );
                  }
                }
              }
            } else {
              is_sync_required = true;
            }
          } else {
            // no work order has been created so we need to update/sync the data.
            is_sync_required = true;
          }

          if (is_sync_required) {
            // update customer data
            await db.Customers.update(
              {
                zoho_customer_id: masterOrder?.customer_id,
                name: masterOrder?.customer_name,
                email: masterOrder?.email,
                mobile: (await filterMobileNumber(masterOrder?.mobile)) || null,
                phone: (await filterMobileNumber(masterOrder?.phone)) || null,
                gender: masterOrder?.gender,
                address: masterOrder?.street_name,
                zone: masterOrder?.region,
                location: masterOrder?.state_name,
                universe: masterOrder?.type_of_house,
                zip_code: masterOrder?.zip_code,
              },
              { where: { id: orderExist.customer_data.id }, logging: false }
            );

            // update order data
            await db.Orders.update(
              {
                order_status_id,
                zoho_stage: masterOrder?.stage,
                title: masterOrder?.deal_name,
                zoho_warranty_id: masterOrder?.warranty_id,
                profile_warranty_end_date: warranty_end_date.profile,
                hardware_warranty_end_date: warranty_end_date.hardware,
              },
              { where: { id: orderExist.id }, logging: false }
            );

            if (is_line_items_changed) {
              // line items changed, so delete all existing order_space & order_scope mapping other than general mapping, then create new mapping.

              const generalSpace = await common.getSpaceByTitle(
                CONSTANTS.SPACE.GENERAL.TITLE
              );
              const generalScope = await common.getScopeByModelAndName(
                CONSTANTS.SCOPE.GENERAL.MODEL,
                CONSTANTS.SCOPE.GENERAL.NAME
              );

              const generalSpaceOrderMapping = await db.OrderSpaces.findOne({
                where: {
                  order_id: orderExist.id,
                  space_id: generalSpace.id,
                },
                attributes: ATTRIBUTES.ORDER_SPACES,
                raw: true,
                logging: false,
              });

              const generalScopeOrderMapping = await db.OrderScopes.findOne({
                where: {
                  order_id: orderExist.id,
                  scope_id: generalScope.id,
                },
                attributes: ATTRIBUTES.ORDER_SCOPES,
                raw: true,
                logging: false,
              });

              // delete order_scopes
              let orderScopeWhere = [{ order_id: orderExist.id }];
              if (generalScopeOrderMapping?.id) {
                orderScopeWhere.push({
                  id: { [Op.ne]: generalScopeOrderMapping.id },
                });
              }

              await db.OrderScopes.destroy({
                where: orderScopeWhere,
                logging: false,
              });

              // delete order_spaces
              let orderSpaceWhere = [{ order_id: orderExist.id }];
              if (generalSpaceOrderMapping?.id) {
                orderSpaceWhere.push({
                  id: { [Op.ne]: generalSpaceOrderMapping.id },
                });
              }

              await db.OrderSpaces.destroy({
                where: orderSpaceWhere,
                logging: false,
              });

              // order's spaces & scopes mapping
              await mapSpaceAndScopeToOrder(lineItemData, orderExist.id);
            }
          }

          // mark is_read 1 in master_order as it is synced.
          await updateMasterOrderIsReadTrue(masterOrder?.id);
        }
      }

      console.log("--- ORDERS SYNCED SUCCESSFULLY ---");
      return {
        success: true,
        message: "Orders synced successfully.",
        data: masterOrderData.map((data) => data.zoho_order_id),
      };
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return {
      success: false,
      message: error.message,
    };
  }
};

const getCustomerID = async (masterOrder) => {
  let customer_id = null;

  // check if customer exist with same zoho customer id. if not available then create.
  if (masterOrder.customer_id) {
    const customerExist = await db.Customers.findOne({
      where: { zoho_customer_id: masterOrder.customer_id },
      raw: true,
      logging: false,
    });

    if (customerExist?.id) {
      customer_id = customerExist.id;
    }
  }

  if (!customer_id) {
    const addCustomer = await db.Customers.create(
      {
        zoho_customer_id: masterOrder?.customer_id,
        name: masterOrder?.customer_name,
        email: masterOrder?.email,
        mobile: (await filterMobileNumber(masterOrder?.mobile)) || null,
        phone: (await filterMobileNumber(masterOrder?.phone)) || null,
        gender: masterOrder?.gender,
        address: masterOrder?.street_name,
        zone: masterOrder?.region,
        location: masterOrder?.state_name,
        universe: masterOrder?.type_of_house,
        zip_code: masterOrder?.zip_code,
      },
      { logging: false }
    );

    if (addCustomer?.id) {
      customer_id = addCustomer.id;
      const actual_customer_id = await getPadString(customer_id, "CUS");
      await db.Customers.update(
        { actual_customer_id: actual_customer_id },
        {
          where: { id: addCustomer.id },
          logging: false,
        }
      );
    }
  }

  return customer_id;
};

const getWarrantyEndDate = async (masterOrder) => {
  const committed_date_of_delivery =
    masterOrder?.committed_date_of_delivery || null;
  const date_of_installation_complete =
    masterOrder?.date_of_installation_complete || null;

  const addYears = (date, years) => {
    const newDate = new Date(date);
    newDate.setFullYear(newDate.getFullYear() + years);
    return newDate;
  };

  let profile = null;
  let hardware = null;

  if (committed_date_of_delivery || date_of_installation_complete) {
    if (committed_date_of_delivery && date_of_installation_complete) {
      // if both date are available
      if (committed_date_of_delivery < date_of_installation_complete) {
        profile = addYears(committed_date_of_delivery, 12);
        hardware = addYears(committed_date_of_delivery, 2);
      } else if (date_of_installation_complete < committed_date_of_delivery) {
        profile = addYears(date_of_installation_complete, 12);
        hardware = addYears(date_of_installation_complete, 2);
      } else {
        // Both dates are equal
        profile = addYears(committed_date_of_delivery, 12);
        hardware = addYears(committed_date_of_delivery, 2);
      }
    } else {
      // if any one date is available
      if (committed_date_of_delivery) {
        profile = addYears(committed_date_of_delivery, 12);
        hardware = addYears(committed_date_of_delivery, 2);
      } else if (date_of_installation_complete) {
        profile = addYears(date_of_installation_complete, 12);
        hardware = addYears(date_of_installation_complete, 2);
      }
    }
  }

  return {
    profile,
    hardware,
  };
};

const mapGeneralSpaceAndScopeToOrder = async (order_id) => {
  // check if general space exist.
  const generalSpaceExist = await common.getSpaceByTitle(
    CONSTANTS.SPACE.GENERAL.TITLE
  );

  // check if general scope exist.
  const generalScopeExist = await common.getScopeByModelAndName(
    CONSTANTS.SCOPE.GENERAL.MODEL,
    CONSTANTS.SCOPE.GENERAL.NAME
  );

  if (generalSpaceExist && generalScopeExist) {
    const orderSpaceMapping = await db.OrderSpaces.create(
      {
        order_id,
        space_id: generalSpaceExist.id,
      },
      { logging: false }
    );

    if (orderSpaceMapping?.id) {
      const orderScopeMapping = await db.OrderScopes.create(
        {
          order_id,
          order_space_mapping_id: orderSpaceMapping.id,
          scope_id: generalScopeExist.id,
        },
        { logging: false }
      );
    }
  }
};

const mapSpaceAndScopeToOrder = async (lineItemData, order_id) => {
  /*
        space = room
        scope's model = description
        scope's name = produt_name
    */
  const spaces = await lineItemData.map((data1) => data1.room);

  // const uniqueSpaces = await getUniqueAndNotNullValueFromArray(spaces);
  const uniqueSpaces = await getUniqueValueFromArray(spaces);

  if (uniqueSpaces.length) {
    for (let j = 0; j < uniqueSpaces.length; j++) {
      // find line item records with same room (space)
      const lineItem = await lineItemData.filter(
        (data3) => data3.room === uniqueSpaces[j]
      );

      let spaceId;
      const spaceTitle = uniqueSpaces[j] || "Unknown Space";
      const space = await common.getSpaceByTitle(spaceTitle);
      if (space) {
        spaceId = space?.id;
      } else {
        const addSpace = await db.Spaces.create(
          {
            title: spaceTitle,
            source: CONSTANTS.DATA_SOURCE.ZOHO,
          },
          { logging: false }
        );

        if (addSpace?.id) {
          spaceId = addSpace?.id;
        }
      }

      // order space mapping
      const addOrderSpace = await db.OrderSpaces.create(
        {
          order_id: order_id,
          space_id: spaceId,
        },
        { logging: false }
      );

      let scopeId;
      if (lineItem.length) {
        for (let k = 0; k < lineItem.length; k++) {
          const model = lineItem[k]?.description || "Unknown Model";
          const name = lineItem[k]?.product_name || "Unknown Scope";

          const scope = await common.getScopeByModelAndName(model, name);
          /*
                        const scope = await db.Scopes.findOne({
                            where: [
                                { model: { [Op.regexp]: model.replace(/[^a-zA-Z0-9]/g, '') } },
                                { name: { [Op.regexp]: name.replace(/[^a-zA-Z0-9]/g, '') } }
                            ],
                            attributes: ATTRIBUTES.SCOPES,
                        });
                    */
          if (scope) {
            scopeId = scope?.id;
          } else {
            const addScope = await db.Scopes.create(
              {
                model: model,
                name: name,
                source: CONSTANTS.DATA_SOURCE.ZOHO,
                height: lineItem[k]?.height || 0,
                width: lineItem[k]?.width || 0,
                specification: lineItem[k]?.specification,
                reference: lineItem[k]?.reference,
                line_item: lineItem[k]?.line_item,
                is_part: lineItem[k]?.is_part,
              },
              { logging: false }
            );

            if (addScope?.id) {
              scopeId = addScope?.id;
            }
          }

          // order scope mapping
          const addOrderScope = await db.OrderScopes.create(
            {
              order_id: order_id,
              order_space_mapping_id: addOrderSpace?.id,
              scope_id: scopeId,
              count: lineItem[k]?.quantity,
            },
            { logging: false }
          );
        }
      }
    }
  }
};

const updateMasterOrderIsReadTrue = async (master_order_id) => {
  await db.MasterOrders.update(
    { is_read: 1 },
    {
      where: { id: master_order_id },
      logging: false,
    }
  );
};

const fetchAndSyncZohoOrders = async (request, response) => {
  try {
    const newUpdatedOrders = await zoho.getNewAndUpdatedOrders();

    if (newUpdatedOrders.success) {
      const syncOrders = await sync();
    }

    if (newUpdatedOrders.success) {
      return response.handler.success(newUpdatedOrders.message);
    } else {
      return response.handler.badRequest(newUpdatedOrders.message);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const syncMasterOrderAndOrderAmount = async (request, response) => {
  try {
    //get all master data
    const { page, limit } = request?.query;
    const masterOrderData = await db.MasterOrders.findAll({
      limit: Number(limit),
      offset: (page - 1) * limit,
    });
    const sliceData = masterOrderData;

    const token = await zoho.getZohoAuthToken();
    if (sliceData?.length > 0) {
      const promiseArr = [];
      const fields = "id,Grand_Total,Amount";
      sliceData.forEach((item) => {
        const url = `${process.env.ZOHO_API_URL}v6/Deals/${item?.zoho_order_id}?fields=${fields}`;
        const config = {
          method: "GET",
          url,
          headers: {
            Authorization: `Zoho-oauthtoken ${token}`,
          },
        };
        promiseArr.push(axios(config));
      });
      const crmResult = await Promise.allSettled(promiseArr);
      const result = [];
      const updateMasterOrderPromiseArray = [];
      crmResult.forEach((val, index) => {
        const data = val?.value?.data?.data?.[0];
        if (data?.Grand_Total || data?.Amount) {
          result.push({
            zoho_order_id: data?.id,
            amount: data?.Grand_Total || data?.Amount,
          });
          updateMasterOrderPromiseArray.push(
            db.MasterOrders.update(
              {
                amount: data?.Grand_Total || data?.Amount,
              },
              {
                where: { zoho_order_id: data?.id },
              }
            )
          );
          updateMasterOrderPromiseArray.push(
            db.Orders.update(
              {
                amount: data?.Grand_Total || data?.Amount,
              },
              {
                where: { zoho_order_id: data?.id },
              }
            )
          );
        }
      });
      await Promise.all(updateMasterOrderPromiseArray);
      return response.handler.success(result);
    }
    return response.handler.success();
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const syncMasterOrderAndOrderLineItem = async (request, response) => {
  try {
    //get all master data
    const { page, limit } = request?.query;
    const masterOrderData = await db.MasterOrders.findAll({
      limit: Number(limit),
      offset: (page - 1) * limit,
    });

    const token = await zoho.getZohoAuthToken();
    if (masterOrderData?.length > 0) {
      const promiseArr = [];
      const fields = "id,Product_Subform";

      //get data from zoho
      masterOrderData.forEach((item) => {
        const url = `${process.env.ZOHO_API_URL}v5/Deals/${item?.zoho_order_id}?fields=${fields}`;
        const config = {
          method: "GET",
          url,
          headers: {
            Authorization: `Zoho-oauthtoken ${token}`,
          },
        };
        promiseArr.push(axios(config));
      });

      const crmResult = await Promise.allSettled(promiseArr);
      const lineItemArray = [];

      //filter data that have Product_Subform
      crmResult?.forEach((data) => {
        if (data?.value?.data?.data?.[0]?.Product_Subform?.length) {
          lineItemArray.push(
            ...data?.value?.data?.data?.[0]?.Product_Subform?.filter(
              (item) => item?.Line_item
            )
          );
        }
      });

      if (lineItemArray?.length) {
        const masterOrderQuoteLineItemArr = [];
        const scopeUpdateArray = [];
        lineItemArray?.forEach((item) => {
          //update that data in master_order_quote_line_items
          masterOrderQuoteLineItemArr.push(
            db.MasterOrderQuoteLineItems.update(
              {
                line_item: item?.Line_item,
              },
              {
                where: { zoho_quote_line_item_id: item?.id },
              }
            )
          );

          //update the data in scope table
          scopeUpdateArray.push(
            db.Scopes.update(
              {
                line_item: item?.Line_item,
              },
              {
                where: {
                  model: item?.Description || "Unknown Model",
                  name: item?.Products?.name || "Unknown Scope",
                  height: item?.Height,
                  width: item?.Width,
                  specification: item?.Specification,
                  reference: item?.Reference,
                },
              }
            )
          );
        });

        await Promise.all(masterOrderQuoteLineItemArr);
        await Promise.all(scopeUpdateArray);
      }
      return response.handler.success(lineItemArray);
    }
    return response.handler.success();
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getOrderSpaceScope = async (request, response) => {
  try {
    const { id, work_order_type } = request.query;

    const loggedInUser = await request?.login_user?.data;

    let whereCond = [];
    let customerWhereCond = [];
    id && whereCond.push({ id });

    // only same zone orders will display
    customerWhereCond.push({ zone: loggedInUser?.zone?.name });

    const orderData = await db.Orders.findOne({
      where: whereCond,
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.Customers,
          as: "customer_data",
          where: customerWhereCond,
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
        {
          model: db.OrderStatus,
          as: "order_status",
          required: true,
          attributes: ATTRIBUTES.ORDER_STATUS,
        },
        {
          model: db.OrderSpaces,
          as: "order_spaces",
          required: false,
          attributes: ATTRIBUTES.ORDER_SPACES,
          include: [
            {
              model: db.Spaces,
              as: "space_data",
              required: false,
              attributes: ATTRIBUTES.SPACES,
            },
            {
              model: db.OrderScopes,
              as: "order_scopes",
              required: false,
              attributes: ATTRIBUTES.ORDER_SCOPES,
              include: [
                {
                  model: db.Scopes,
                  as: "scope_data",
                  required: false,
                  attributes: [
                    ...ATTRIBUTES.SCOPES,
                    [
                      db.sequelize.fn(
                        "CONCAT",
                        db.sequelize.col("height"),
                        " ⨯ ",
                        db.sequelize.col("width")
                      ),
                      "dimension",
                    ],
                  ],
                  include: [
                    {
                      model: db.ScopeImages,
                      as: "scope_image",
                      required: false,
                      attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                        (value) => value !== "base64"
                      ),
                      include: [
                        {
                          model: db.ScopeImageTouchPoints,
                          as: "touch_points",
                          required: false,
                          attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    });

    if (!orderData) {
      return response.handler.badRequest(MESSAGE.ORDERS.FOUND.FAILED);
    }
    const fabricator_user_ids = orderData?.fabricator_id;

    // get fabricator user from user authentication service.
    let fabricatorData = [];
    const userAPI = await userService.getUserDetailsByUserIds({
      user_ids: fabricator_user_ids,
      headers: request?.headers,
    });

    if (userAPI) {
      fabricatorData = userAPI;
    }

    let respData = { ...JSON.parse(JSON.stringify(orderData)) };

    //remove space and scope that work order is already created
    const workOrderData = await db.WorkOrders.findAll({
      where: { order_id: orderData?.id, work_order_type_id: work_order_type },
      include: [
        {
          model: db.WorkOrderSpaces,
          as: "work_order_spaces",
          attributes: ATTRIBUTES.WORK_ORDER_SPACES,
          include: [
            {
              model: db.WorkOrderScopes,
              as: "work_order_scopes",
              attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
            },
          ],
        },
      ],
    });

    if (workOrderData?.length) {
      const scopeArr = [
        ...new Set(
          workOrderData
            ?.flatMap((item) => item?.work_order_spaces)
            ?.flatMap((item) => item?.work_order_scopes)
            ?.map((item) => item?.scope_id)
        ),
      ];

      const resOrderSpaceData = [];
      orderData?.order_spaces?.forEach((space) => {
        let orderSpaceData = { ...JSON.parse(JSON.stringify(space)) };

        const orderScopeData = orderSpaceData?.order_scopes?.filter(
          (scope) => !scopeArr.includes(scope?.scope_id)
        );

        orderSpaceData = { ...orderSpaceData, order_scopes: orderScopeData };

        if (orderSpaceData.order_scopes?.length)
          resOrderSpaceData.push(orderSpaceData);
      });

      respData = {
        ...JSON.parse(JSON.stringify(orderData)),
        order_spaces: resOrderSpaceData,
      };
    } else {
      respData = {
        ...JSON.parse(JSON.stringify(orderData)),
      };
    }

    const masterOrderData = await db.MasterOrders.findOne({
      distinct: true,
      where: { zoho_order_id: orderData?.zoho_order_id },
      include: [
        {
          model: db.MasterOrderQuoteLineItems,
          as: "line_items_data",
          required: true,
          where: { is_active: 1, is_deleted: 0 },
          attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
        },
      ],
    });

    const resOrderSpaceData = [];

    respData?.order_spaces?.forEach((space) => {
      let scopeName;
      const index = masterOrderData?.line_items_data?.findIndex(
        (master) => master?.room === space?.space_data?.title
      );
      if (index !== -1) {
        scopeName = masterOrderData?.line_items_data?.[index]?.product_name;
      }
      const scopeData = [];
      if (space?.order_scopes?.length) {
        space?.order_scopes?.forEach((scope) => {
          let tmpScopeData = { ...JSON.parse(JSON.stringify(scope)) };
          if (!scope?.scope_data?.is_part) {
            if (
              scopeName &&
              scope?.scope_data?.name !== scopeName &&
              scope?.scope_data?.new_name === scopeName
            ) {
              tmpScopeData = {
                ...JSON.parse(JSON.stringify(scope)),
                scope_data: {
                  ...JSON.parse(JSON.stringify(scope?.scope_data)),
                  name: scopeName,
                },
              };
            } else {
              tmpScopeData = {
                ...JSON.parse(JSON.stringify(scope)),
              };
            }
            scopeData.push(tmpScopeData);
          }
        });
      }

      if (scopeData?.length) {
        resOrderSpaceData.push({
          ...JSON.parse(JSON.stringify(space)),
          order_scopes: scopeData,
        });
      }
    });

    respData = {
      ...JSON.parse(JSON.stringify(orderData)),
      order_spaces: resOrderSpaceData,
    };

    return response.handler.success(MESSAGE.ORDERS.FOUND.SUCCESS, respData);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const syncOrderStatus = async (request, response) => {
  try {
    //get all master data
    const { page, limit } = request?.query;
    const newOrderStatus = await common.getOrderStatusByTitle(
      CONSTANTS.ORDER_STATUS.NEW
    );

    //fetch order with status new with work order
    const newOrderData = await db.Orders.findAll({
      where: {
        order_status_id: newOrderStatus?.id,
        is_active: 1,
        is_deleted: 0,
      },
      include: [
        {
          model: db.WorkOrders,
          as: "work_order_data",
        },
      ],
      limit: Number(limit),
      offset: (page - 1) * limit,
    });

    if (!newOrderData?.length) {
      return response.handler.success(MESSAGE.ORDERS.FOUND.FAILED);
    }
    //filter order without work order
    const orderDataWithOutWorkOrder = newOrderData?.filter(
      (item) => item?.work_order_data?.length === 0
    );
    const token = await zoho.getZohoAuthToken();

    if (orderDataWithOutWorkOrder?.length > 0) {
      const promiseArr = [];
      const fields = "id,Stage";

      //get data from zoho
      orderDataWithOutWorkOrder.forEach((item) => {
        const url = `${process.env.ZOHO_API_URL}v5/Deals/${item?.zoho_order_id}?fields=${fields}`;
        const config = {
          method: "GET",
          url,
          headers: {
            Authorization: `Zoho-oauthtoken ${token}`,
          },
        };
        promiseArr.push(axios(config));
      });

      const result = await Promise.all(promiseArr);

      //get zoho order id for updating order
      const handoverOrder = result
        ?.filter((item) => item?.data?.data?.[0]?.Stage === "Handover")
        ?.map((item) => item?.data?.data?.[0]?.id);

      //get completd order status data
      const completdStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.COMPLETED
      );

      await db.Orders.update(
        { order_status_id: completdStatus?.id },
        { where: { zoho_order_id: { [Op.in]: handoverOrder } } }
      );

      return response.handler.success("", handoverOrder);
    }

    return response.handler.success();
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const uploadImage = async (request, response) => {
  try {
    let { order_id } = request.body;

    order_id = parseInt(order_id);

    // check if task is exist with requested id or not
    const orderExist = await db.Orders.findOne({ where: { id: order_id } });
    if (!orderExist) {
      return response.handler.badRequest(MESSAGE.ORDER.NOT_EXIST);
    }

    const token = await zoho.getZohoAuthToken();
    const fields = "id,Stage";
    const url = `${process.env.ZOHO_API_URL}v5/Deals/${orderExist?.zoho_order_id}?fields=${fields}`;

    const config = {
      method: "GET",
      url: url,
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    const zohoStatus = await axios(config);
    if (!zohoStatus?.data?.data?.[0]?.Stage) {
      return response.handler.badRequest(MESSAGE.ORDER.ZOHO_STATUS_NOT_FOUND);
    }
    if (
      !CONSTANTS.ORDER_IMAGE_UPLOAD_STATUS.includes(
        zohoStatus?.data?.data?.[0]?.Stage
      )
    ) {
      return response.handler.badRequest(
        MESSAGE.ORDER.UPLOAD_IMAGE.STATUS_INVALID
      );
    }

    // upload img to s3
    let Key, Location, metadata, mimetype;
    if (request.file) {
      mimetype = request.file?.mimetype;
      const path = request.file?.path;
      const blob = fs.readFileSync(path); //convert file to blob
      const fileName = request.file?.originalname;
      const uploadProfileImg = await aws.uploadFileS3(
        fileName,
        blob,
        CONSTANTS.S3_BUCKET.FOLDERS.ORDER.BASE,
        metadata,
        mimetype
      );

      if (uploadProfileImg) {
        Key = uploadProfileImg.Key;
        Location = uploadProfileImg.Location;
      }
    }
    // const base64 = await aws.convertS3UrlToBase64(Key);

    if (Key && Location) {
      let payload = {
        order_id,
        image_key: Key,
        image_url: Location,
        // base64: base64
      };
      const addImage = await db.OrderImages.create(payload);

      if (addImage?.id) {
        const uploadedImg = await db.OrderImages.findOne({
          where: { id: addImage?.id },
          attributes: ["id", "image_key", "image_url"],
          raw: true,
        });

        return response.handler.success(MESSAGE.ORDER.UPLOAD_IMAGE.SUCCESS, {
          image: {
            ...uploadedImg,
            order_id: order_id,
          },
        });
      }
    } else {
      return response.handler.badRequest(MESSAGE.TASK.UPLOAD_IMAGE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const deleteImage = async (request, response) => {
  try {
    const { id } = request.query;
    if (!id) {
      return response.handler.badRequest(MESSAGE.ORDERS.DELETE.ID);
    }
    const isImageExist = await db.OrderImages.findOne({ where: { id } });
    if (!isImageExist) {
      return response.handler.badRequest(MESSAGE.ORDERS.DELETE.NOT_EXIST);
    }
    await aws.deleteFileS3(isImageExist.image_key);
    await db.OrderImages.destroy({
      where: { id },
    });

    return response.handler.success(MESSAGE.ORDERS.DELETE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendHandoverEmail = async (request, response) => {
  try {
    const { order_id } = request.body;

    const orderData = await db.Orders.findOne({
      where: { id: order_id },
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
        {
          model: db.HandoverWorkOrders,
          as: "handover_work_order",
          required: false,
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDERS,
        },
      ],
    });

    if (!orderData) {
      return response.handler.badRequest(MESSAGE.ORDERS.FOUND.ERROR);
    }

    if (!orderData?.handover_work_order) {
      return response.handler.badRequest(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    const customerEmail = orderData?.customer_data?.email;
    const customerMobile = orderData?.customer_data?.mobile;
    const encryptId = encrypt(orderData?.handover_work_order?.id?.toString());
    const url = await replaceData(
      CONSTANTS.HANDOVER_EMAIL_LINK,
      "{WORKORDER_ID}",
      encryptId
    );

    const link = await common.shortenWithTinyURL(url);

    const emailContent = `<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Hi ${orderData?.customer_data?.name},</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Hope you're doing great! 👋</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re happy to share that your <b>Eternia windows have successfully passed our Quality Check (QC).</b>✅</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re now just <b>one step away</b> from handing over your order <b>${orderData?.actual_order_id}</b> and sharing your <b>official warranty card.</b></p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Please click the link below to begin the handover process:</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">${link}</p><br/>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Need any help? Call us at 📞 <b>97699 40000</b> — we're here for you.</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;"><b>– Team Eternia</b></p>`;

    const smsContent = `Hi ${orderData?.customer_data?.name},\n\nHope you're doing great! 👋\nWe’re happy to share that your Eternia windows have successfully passed our Quality Check (QC).✅\nWe’re now just one step away from handing over your order ${orderData?.actual_order_id} and sharing your official warranty card.\nPlease click the link below to begin the handover process:\n${link}\n\nNeed any help? Call us at 📞 97699 40000 — we're here for you.\n– Team Eternia`;

    await sendMail(customerEmail, "Handover Sheet Link", emailContent);
    await sendSMS(`+91${customerMobile}`, smsContent);

    return response.handler.success(MESSAGE.ORDER.HANDOVER_EMAIL.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendHandoverLink = async (request, response) => {
  try {
    const { order_id } = request.body;

    const orderData = await db.Orders.findOne({
      where: { id: order_id },
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
        {
          model: db.HandoverWorkOrders,
          as: "handover_work_order",
          required: false,
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDERS,
        },
      ],
    });

    if (!orderData) {
      return response.handler.badRequest(MESSAGE.ORDERS.FOUND.ERROR);
    }

    if (!orderData?.handover_work_order) {
      return response.handler.badRequest(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    const encryptId = encrypt(orderData?.handover_work_order?.id?.toString());
    const url = await replaceData(
      CONSTANTS.HANDOVER_EMAIL_LINK,
      "{WORKORDER_ID}",
      encryptId
    );

    const link = await common.shortenWithTinyURL(url);

    return response.handler.success(MESSAGE.ORDER.HANDOVER_LINK_SUCCESS, {
      link,
    });
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendWarrantyMail = async (request, response) => {
  try {
    const { zoho_order_id, date } = request.body;

    //get completed work order status data
    const completedWoStatus = await db.WorkOrderStatus.findOne({
      where: { title: CONSTANTS.WORK_ORDER.STATUS.COMPLETED },
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
    });

    //get quality check work order type data
    const qcWoType = await db.WorkOrderTypes.findOne({
      where: { title: CONSTANTS.WORK_ORDER.TYPE.QUALITY_CHECK },
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
    });

    //get order data based on zoho order id and also get qc work order data which is completed
    const orderData = await db.Orders.findOne({
      where: { zoho_order_id },
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
        {
          model: db.WorkOrders,
          as: "work_order_data",
          required: false,
          where: {
            work_order_type_id: qcWoType?.id,
            work_order_status_id: completedWoStatus?.id,
          },
          attributes: ATTRIBUTES.WORK_ORDERS,
        },
      ],
    });

    if (!orderData) {
      return response.handler.badRequest(MESSAGE.ORDERS.FOUND.ERROR);
    }

    //check if qc mode is app or not otherwise we can not send warranty email
    if (orderData?.qc_mode !== CONSTANTS.ZOHO.QC_MODE.APP) {
      return response.handler.badRequest(
        MESSAGE.ORDER.WARRANTY_EMAIL.INVALID_QC_MODE
      );
    }

    const mailIdData = [];

    if (orderData?.work_order_data?.length) {
      const ids = orderData?.work_order_data?.map((item) => item?.id);
      const reviewData = await db.WorkOrderReview.findAll({
        where: { work_order_id: { [Op.in]: ids }, review_status: 1 },
        attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
      });

      const reviewerIds = [
        ...new Set(reviewData?.map((item) => item?.reviewer_user_id)),
      ];

      const userData = await userService.getUserDetailsByUserIds({
        user_ids: [...reviewerIds, orderData?.fabricator_id],
        headers: request?.headers,
      });

      userData?.forEach((item) => {
        if (item?.email) {
          mailIdData.push({
            email: item?.email,
            name: item?.first_name + " " + item?.last_name,
          });
        }
      });
    }

    if (mailIdData?.length) {
      const promiseArray = [];
      mailIdData?.forEach(async (item) => {
        const mailContent = `<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Dear ${item?.name},</p>
            <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">This is to inform that Warranty has been issued to the Customer ${orderData?.customer_data?.name} for the opportunity ${orderData?.zoho_order_id} on ${date} from the Eternia team.</p>`;

        promiseArray.push(
          sendMail(item?.email, "Issue of warranty", mailContent)
        );
      });
      await Promise.all(promiseArray);
      return response.handler.success(MESSAGE.ORDER.WARRANTY_EMAIL.SUCCESS);
    }
    return response.handler.badRequest(MESSAGE.ORDER.WARRANTY_EMAIL.ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const syncOrderZohoSatge = async (request, response) => {
  try {
    const { page = 1, limit = 10 } = request.query;

    const orderData = await db.Orders.findAll({
      where: {
        is_active: 1,
        is_deleted: 0,
        zoho_stage: {
          [Op.in]: [
            CONSTANTS.ZOHO.ORDER_STAGE.INSTALLATION_ONGOING.TITLE,
            CONSTANTS.ZOHO.ORDER_STAGE.QC_IN_PROGRESS.TITLE,
          ],
        },
      },
      attributes: ATTRIBUTES.ORDERS,
      limit: Number(limit),
      offset: (page - 1) * limit,
    });

    if (!orderData?.length) {
      return response.handler.badRequest(
        MESSAGE.ORDERS.ZOHO_ORDERS.FOUND.FAILED
      );
    }

    const promiseArray = [];
    const token = await zoho.getZohoAuthToken();
    orderData?.forEach((item) => {
      const url = `${process.env.ZOHO_API_URL}v5/Deals/${item?.zoho_order_id}?fields=Id,Stage`;
      const config = {
        method: "GET",
        url,
        headers: {
          Authorization: `Zoho-oauthtoken ${token}`,
        },
      };
      promiseArray.push(axios(config));
    });
    const result = await Promise.all(promiseArray);
    const completedOrderStatus = await common.getOrderStatusByTitle(
      CONSTANTS.ORDER_STATUS.COMPLETED
    );
    const updatePromiseArray = [];
    result?.forEach((item) => {
      if (item?.data?.data?.[0]) {
        let updatePyalod = {
          zoho_stage: item?.data?.data?.[0]?.Stage,
        };
        if (
          item?.data?.data?.[0]?.Stage ===
          CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE
        ) {
          updatePyalod = {
            ...updatePyalod,
            order_status_id: completedOrderStatus?.id,
          };
        }
        const zoho_order_id = item?.data?.data?.[0]?.id;
        updatePromiseArray.push(
          db.Orders.update(updatePyalod, { where: { zoho_order_id } })
        );
      }
    });
    await Promise.all(updatePromiseArray);
    return response.handler.success(MESSAGE.ORDERS.ZOHO_ORDERS.UPDATE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  get,
  getStatus,
  fetchZohoOrders,
  sync,
  fetchAndSyncZohoOrders,
  syncMasterOrderAndOrderAmount,
  syncMasterOrderAndOrderLineItem,
  getOrderSpaceScope,
  syncOrderStatus,
  uploadImage,
  deleteImage,
  sendHandoverEmail,
  sendHandoverLink,
  sendWarrantyMail,
  syncOrderZohoSatge,
};
