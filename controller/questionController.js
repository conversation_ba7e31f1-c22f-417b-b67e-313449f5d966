const { Op } = require("sequelize");
const { isEmpty } = require("lodash");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");
const {
  isAttachmentRequiredSchema,
  questionConditionValidatorSchema,
} = require("../validator/questionConditionValidator.js");
const {
  mapQuestionDependenciesInBulk,
  handleQuestionDependencies,
} = require("./questionDependency.js");
const create = async (request, response) => {
  try {
    const {
      que_type_id,
      title,
      options,
      attachment_required,
      report_tag_id,
      is_numeric,
      is_video_required,
      is_attachment_required,
      video_required,
      dependencies,
      comment_required,
    } = request.body;
    // check if question type is exist or not.
    const queType = await common.getQuestionTypeById(que_type_id);
    if (!queType) {
      return response.handler.badRequest(MESSAGE.QUESTION_TYPE.NOT_EXIST);
    }

    // check if report tag exist or not
    if (report_tag_id) {
      const reportTagExist = await common.getReportTagById(report_tag_id);

      if (!reportTagExist) {
        return response.handler.badRequest(MESSAGE.REPORT_TAG.NOT_EXIST);
      }
    }

    // check if question with same title exist or not.
    const questionExist = await common.getQuestionByConditions([
      { title: { [Op.like]: title } },
      { is_active: 1 },
      { is_deleted: 0 },
    ]);

    if (questionExist) {
      return response.handler.conflict(MESSAGE.QUESTION.EXIST);
    }

    if (
      queType?.title !== CONSTANTS.QUESTION_TYPES.DESCRIPTIVE &&
      !options?.length
    ) {
      return response.handler.badRequest(MESSAGE.QUESTION.OPTIONS.REQUIRED);
    }

    const addQuestion = await db.Questions.create({
      question_type_id: que_type_id,
      report_tag_id: report_tag_id,
      title: title,
      attachment_required: attachment_required,
      is_attachment_required: is_attachment_required,
      is_numeric: is_numeric || 0,
      is_video_required: is_video_required || 0,
      video_required,
      comment_required,
    });

    if (addQuestion) {
      if (
        queType?.title !== CONSTANTS.QUESTION_TYPES.DESCRIPTIVE &&
        options?.length
      ) {
        let addQueOptions = [];
        for (let i = 0; i < options.length; i++) {
          addQueOptions.push({
            question_id: addQuestion?.id,
            title: options[i],
          });
        }

        await db.QuestionOptions.bulkCreate(addQueOptions);
      }

      if (dependencies?.length) {
        const updatedDependencies = dependencies.map((dependency) => ({
          ...dependency,
          parentQuestionId: addQuestion.id, // Add parentQuestionId
        }));

        await mapQuestionDependenciesInBulk(updatedDependencies);
      }

      return response.handler.success(MESSAGE.QUESTION.CREATE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.QUESTION.CREATE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const { id, que_type_id, search, status } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [];
    id && whereCond.push({ id });

    if (search) {
      const searchArr = [{ title: { [Op.like]: `%${search}%` } }];
      whereCond.push({ [Op.or]: searchArr });
    }

    que_type_id && whereCond.push({ "$question_type.id$": que_type_id });
    if (status !== undefined) {
      whereCond.push({ is_active: status });
    }

    const { count: totalCount, rows: questionsData } =
      await db.Questions.findAndCountAll({
        limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.QUESTIONS,
        distinct: true,
        order: [["id", "DESC"]],
        include: [
          {
            model: db.QuestionTypes,
            as: "question_type",
            required: true,
            attributes: ATTRIBUTES.QUESTION_TYPES,
          },
          {
            model: db.QuestionOptions,
            as: "question_options",
            required: false,
            attributes: ATTRIBUTES.QUESTION_OPTIONS,
            where: {
              is_active: 1,
              is_deleted: 0,
            },
          },
          {
            model: db.ReportTags,
            as: "report_tag",
            required: false,
            attributes: ATTRIBUTES.REPORT_TAGS,
            where: { is_active: 1 },
          },
          {
            model: db.QuestionDependencies,
            as: "child_dependencies",
            required: false,
            where: { is_active: 1 },
            include: [
              {
                model: db.Questions,
                as: "child_question", // this must match the `as` in belongsTo
                attributes: ATTRIBUTES.QUESTIONS,
                include: [
                  {
                    model: db.QuestionTypes,
                    as: "question_type",
                    attributes: ATTRIBUTES.QUESTION_TYPES,
                  },
                  {
                    model: db.QuestionOptions,
                    as: "question_options",
                    attributes: ATTRIBUTES.QUESTION_OPTIONS,
                    required: false,
                    where: {
                      is_active: 1,
                      is_deleted: 0,
                    },
                  },
                ],
              },
            ],
          },
        ],
      });
    // Format the response to make dependencies more readable
    const formattedQuestions = questionsData.map((question) => {
      const plainQuestion = question.get({ plain: true });

      return {
        ...plainQuestion,
        dependencies: {
          // Questions that depend on this question
          childQuestions:
            plainQuestion.child_dependencies?.map((dep) => ({
              question: dep.child_question,
              triggerConditions: dep.trigger_conditions,
              logicalOperator: dep.logical_operator,
              id: dep.id,
            })) || [],

          // Questions that this question depends on
          parentQuestions:
            plainQuestion.parent_dependencies?.map((dep) => ({
              question: dep.parent_question,
              triggerConditions: dep.trigger_conditions,
              logicalOperator: dep.logical_operator,
            })) || [],
        },
        // Remove the raw dependencies from the response
        child_dependencies: undefined,
        parent_dependencies: undefined,
      };
    });

    if (!questionsData) {
      return response.handler.badRequest(MESSAGE.QUESTION.FOUND.ERROR);
    } else if (totalCount === 0 || questionsData.length === 0) {
      return response.handler.notFound(MESSAGE.QUESTION.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.QUESTION.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: formattedQuestions.length,
        questions: formattedQuestions,
      });
    }
  } catch (error) {
    console.error("Error in get questions:", error);
    return response.handler.serverError(error);
  }
};

// Helper function to check if a question should be shown based on answers
const shouldShowQuestion = async (question, answers) => {
  if (!question.dependencies?.parentQuestions?.length) {
    return true; // No dependencies, always show
  }

  for (const dep of question.dependencies.parentQuestions) {
    const parentAnswer = answers[dep.question.id];
    if (!parentAnswer) continue;

    const dependencyMet = await evaluateQuestionVisibility(dep.question.id, {
      [dep.question.id]: parentAnswer,
    });

    if (
      !dependencyMet.some(
        (d) => d.childQuestionId === question.id && d.shouldShow
      )
    ) {
      return false;
    }
  }

  return true;
};

const update = async (request, response) => {
  try {
    const {
      id,
      que_type_id,
      title,
      options,
      attachment_required,
      report_tag_id,
      video_required,
      is_attachment_required,
      is_numeric,
      is_video_required,
      dependencies,
      comment_required,
    } = request.body;

    // Check if question exists and is active
    const queExist = await common.getQuestionById(id);
    if (!queExist) {
      return response.handler.badRequest(MESSAGE.QUESTION.NOT_EXIST);
    }

    // If question type is being updated, validate it exists
    if (que_type_id && que_type_id !== queExist.question_type_id) {
      const queType = await common.getQuestionTypeById(que_type_id);
      if (!queType) {
        return response.handler.badRequest(MESSAGE.QUESTION_TYPE.NOT_EXIST);
      }
    }

    // Check if report tag exists if provided
    if (report_tag_id) {
      const reportTagExist = await common.getReportTagById(report_tag_id);
      if (!reportTagExist) {
        return response.handler.badRequest(MESSAGE.REPORT_TAG.NOT_EXIST);
      }
    }

    // Check for duplicate title excluding current question
    if (title && title !== queExist.title) {
      const questionExist = await common.getQuestionByConditions([
        { title: { [Op.like]: title } },
        { is_active: 1 },
        { is_deleted: 0 },
        { id: { [Op.ne]: id } },
      ]);
      if (questionExist) {
        return response.handler.conflict(MESSAGE.QUESTION.EXIST);
      }
    }

    // Start transaction for atomicity
    const transaction = await db.sequelize.transaction();

    try {
      // Update question
      const updateQuestion = await db.Questions.update(
        {
          question_type_id: que_type_id || queExist.question_type_id,
          title: title || queExist.title,
          report_tag_id: report_tag_id,
          attachment_required: attachment_required,
          video_required: video_required,
          is_attachment_required: is_attachment_required,
          is_numeric:
            is_numeric !== undefined ? is_numeric : queExist.is_numeric,
          is_video_required:
            is_video_required !== undefined
              ? is_video_required
              : queExist.is_video_required,
          comment_required: comment_required || queExist.comment_required,
          updated_at: new Date(),
        },
        {
          where: { id },
          transaction,
        }
      );

      if (!updateQuestion[0]) {
        await transaction.rollback();
        return response.handler.badRequest(MESSAGE.QUESTION.UPDATE.ERROR);
      }

      // Handle options update if provided
      const queType = await common.getQuestionTypeById(
        que_type_id || queExist.question_type_id
      );
      if (options && queType?.title !== CONSTANTS.QUESTION_TYPES.DESCRIPTIVE) {
        // First deactivate all existing options
        await db.QuestionOptions.update(
          {
            is_active: 0,
            is_deleted: 1,
            updated_at: new Date(),
          },
          {
            where: {
              question_id: id,
              is_active: 1,
              is_deleted: 0,
            },
            transaction,
          }
        );

        // Create new options
        const updateQueOptions = options.map((option) => ({
          question_id: id,
          title: option,
          is_active: 1,
          is_deleted: 0,
          created_at: new Date(),
          updated_at: new Date(),
        }));

        await db.QuestionOptions.bulkCreate(updateQueOptions, { transaction });
      }

      // Handle dependencies
      //changes by pawan
      const dependencyResult = await handleQuestionDependencies(
        request,
        response,
        id,
        transaction
      );
      if (dependencyResult !== true) {
        await transaction.rollback();
        return dependencyResult;
      }

      // Commit transaction if everything succeeded
      await transaction.commit();
      return response.handler.success(MESSAGE.QUESTION.UPDATE.SUCCESS);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("Error updating question:", error);
    return response.handler.serverError(error);
  }
};

const changeStatus = async (request, response) => {
  try {
    const { id, status } = request.body;

    // check if question is exist with requested id or not.
    const questionExist = await common.getQuestionById(id);

    if (!questionExist) {
      return response.handler.badRequest(MESSAGE.QUESTION.NOT_EXIST);
    }

    if (status) {
      // check if same title with question already exist and active or not. If available and active, then we will restrict to change status.
      const check = await common.getQuestionByConditions([
        { title: questionExist.title },
        { is_active: 1 },
        { is_deleted: 0 },
      ]);

      if (!isEmpty(check)) {
        return response.handler.badRequest(
          MESSAGE.QUESTION.STATUS_CHANGE.FAILED_DUE_TO_SAME_EXIST
        );
      }
    }

    if (!status) {
      const queMappingExist = await db.QuestionMappingSet.findAll({
        where: { question_id: id },
        attributes: ATTRIBUTES.QUESTION_MAPPING_SET,
      });

      if (queMappingExist?.length) {
        return response.handler.badRequest(
          MESSAGE.QUESTION.STATUS_CHANGE.FAILED_DUE_TO_QUESTION_MAPPING_EXIST
        );
      }
    }
    const changeQuestionStatus = await db.Questions.update(
      { is_active: status },
      { where: { id } }
    );

    if (changeQuestionStatus) {
      return response.handler.success(MESSAGE.QUESTION.STATUS_CHANGE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.QUESTION.STATUS_CHANGE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  create,
  get,
  update,
  changeStatus,
  shouldShowQuestion,
};
