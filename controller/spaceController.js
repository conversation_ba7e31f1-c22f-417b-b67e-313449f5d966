const { Op } = require("sequelize");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");

const create = async (request, response) => {
  try {
    const { title, source } = request.body;

    const spaceExist = await common.getSpaceByTitle(title);

    if (spaceExist) {
      return response.handler.conflict(MESSAGE.SPACE.EXIST);
    }

    const addSpace = await db.Spaces.create({
      title: title,
      source: source || CONSTANTS.DATA_SOURCE.CMS,
    });

    if (addSpace) {
      const addedSpaceId = addSpace?.id;

      let space = {};
      if (addedSpaceId) {
        space = await db.Spaces.findOne({
          where: { id: addedSpaceId },
          attributes: ATTRIBUTES.SPACES,
        });
      }

      return response.handler.success(MESSAGE.SPACE.ADD.SUCCESS, { space });
    } else {
      return response.handler.badRequest(MESSAGE.SPACE.ADD.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const update = async (request, response) => {
  try {
    const { id, title } = request.body;

    // check if space is exist with requested id or not.
    const spaceExist = await common.getSpaceById(id);

    if (!spaceExist) {
      return response.handler.badRequest(MESSAGE.SPACE.NOT_EXIST);
    }

    // check if requested title is unique or not.
    const checkSpaceTitle = await common.getSpaceByConditions([
      { id: { [Op.ne]: id } },
      { title },
    ]);

    if (checkSpaceTitle) {
      return response.handler.conflict(MESSAGE.SPACE.EXIST);
    }

    const updateSpace = await db.Spaces.update(
      { title: title },
      { where: { id } }
    );

    if (updateSpace) {
      return response.handler.success(MESSAGE.SPACE.UPDATE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.SPACE.UPDATE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const { id, search, status, order_id } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [];
    id && whereCond.push({ id });
    status && whereCond.push({ is_active: status });

    if (search) {
      const searchArr = [{ title: { [Op.like]: `%${search}%` } }];

      whereCond.push({ [Op.or]: searchArr });
    }

    if (order_id) {
      const workOrderData = await db.WorkOrders.findAll({
        where: { order_id, is_active: 1 },
        include: [
          {
            model: db.WorkOrderSpaces,
            as: "work_order_spaces",
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
          },
        ],
      });

      let spaceIds = workOrderData?.flatMap((item) =>
        item?.work_order_spaces?.flatMap((space) => space?.space_id)
      );

      spaceIds = [...new Set(spaceIds)];
      whereCond.push({ id: { [Op.notIn]: spaceIds } });
    }

    const { count: totalCount, rows: spaceData } =
      await db.Spaces.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.SPACES,
        order: [["id", "DESC"]],
      });

    if (!spaceData) {
      return response.handler.badRequest(MESSAGE.SPACE.FOUND.ERROR);
    } else if (totalCount === 0 || spaceData.length === 0) {
      return response.handler.notFound(MESSAGE.SPACE.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.SPACE.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: spaceData.length,
        spaces: spaceData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const changeStatus = async (request, response) => {
  try {
    const { id, status } = request.body;

    // check if space is exist with requested id or not.
    const spaceExist = await common.getSpaceById(id);

    if (!spaceExist) {
      return response.handler.badRequest(MESSAGE.SPACE.NOT_EXIST);
    }

    // if current status of space is active and user request to inactive, then we will check if that space is mapped with any work order or not. if mapped then we will restrict to change the status.
    if (!status) {
      const spaceWo = await db.WorkOrderSpaces.count({
        where: { space_id: id },
      });

      if (spaceWo) {
        return response.handler.badRequest(
          MESSAGE.SPACE.STATUS_CHANGE.FAILED_DUE_TO_LINKED_WO
        );
      }
    }

    const changeSpaceStatus = await db.Spaces.update(
      { is_active: status },
      { where: { id } }
    );

    if (changeSpaceStatus) {
      return response.handler.success(MESSAGE.SPACE.STATUS_CHANGE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.SPACE.STATUS_CHANGE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  create,
  update,
  get,
  changeStatus,
};
