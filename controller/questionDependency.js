const db = require("../database/models");
const common = require("./commonFunctions.js");
const { CONSTANTS } = require("../helpers/constants.js");

async function mapQuestionDependency(request, response) {
  const {
    parentQuestionId,
    childQuestionId,
    triggerConditions,
    logicalOperator = "AND",
  } = request.body;
  try {
    // Validate that questions exist
    const [parentQuestion, childQuestion] = await Promise.all([
      db.Questions.findByPk(parentQuestionId),
      db.Questions.findByPk(childQuestionId),
    ]);

    if (!parentQuestion || !childQuestion) {
      return response.handler.badRequest("Parent or child question not found");
    }

    // Validate trigger conditions
    for (const condition of triggerConditions) {
      if (condition.option_ids?.length) {
        // Verify all option IDs exist for the parent question
        const validOptions = await db.QuestionOptions.count({
          where: {
            id: condition.option_ids,
            question_id: parentQuestionId,
          },
        });

        if (validOptions !== condition.option_ids.length) {
          return response.handler.badRequest("Invalid option IDs provided");
        }
      }
    }

    // Create dependency
    await db.QuestionDependencies.create({
      parent_question_id: parentQuestionId,
      child_question_id: childQuestionId,
      trigger_conditions: triggerConditions,
      logical_operator: logicalOperator,
    });
    return response.handler.success("Question mapped successfully");
  } catch (error) {
    console.error("Error mapping question dependency:", error);
    return response.handler.serverError(error);
  }
}

async function deactivateDependency(request, response) {
  const { dependencyId } = request;
  try {
    // Find the dependency
    const dependency = await db.QuestionDependencies.findByPk(dependencyId);
    if (!dependency) {
      return response.handler.badRequest("Dependency not found");
    }

    // Soft delete by setting is_active to false
    await dependency.update({
      is_active: false,
      updated_at: new Date(),
    });

    return response.handler.success("Dependency deactivated successfully");
  } catch (error) {
    console.error("Error deactivating dependency:", error);
    return response.handler.serverError(error);
  }
}

// Function to edit dependency conditions
async function updateDependency(request, response) {
  const { dependencyId, triggerConditions, logicalOperator } = request.body;
  try {
    // Find the dependency
    const dependency = await db.QuestionDependencies.findByPk(dependencyId);
    if (!dependency) {
      return response.handler.badRequest("Dependency not found");
    }

    // If the dependency is inactive, don't allow updates
    if (!dependency.is_active) {
      return response.handler.badRequest(
        "Cannot update an inactive dependency"
      );
    }

    // Validate that all option_ids exist for the parent question
    for (const condition of triggerConditions) {
      if (condition.option_ids?.length) {
        const validOptions = await db.QuestionOptions.count({
          where: {
            id: condition.option_ids,
            question_id: dependency.parent_question_id,
          },
        });

        if (validOptions !== condition.option_ids.length) {
          return response.handler.badRequest("Invalid option IDs provided");
        }
      }
    }

    // Update the dependency
    await dependency.update({
      trigger_conditions: triggerConditions,
      logical_operator: logicalOperator,
      updated_at: new Date(),
    });
    return response.handler.success("Dependency updated successfully");
  } catch (error) {
    console.error("Error updating dependency:", error);
    return response.handler.serverError(error);
  }
}

// Function to reactivate a dependency
async function reactivateDependency(request, response) {
  const { dependencyId } = request.body;
  try {
    // Find the dependency
    const dependency = await db.QuestionDependencies.findByPk(dependencyId);
    if (!dependency) {
      return response.handler.badRequest("Dependency not found");
    }

    // Reactivate by setting is_active to true
    await dependency.update({
      is_active: true,
      updated_at: new Date(),
    });

    return response.handler.success("Dependency reactivated successfully");
  } catch (error) {
    console.error("Error reactivating dependency:", error);
    return response.handler.serverError(error);
  }
}

const mapQuestionDependenciesInBulk = async (dependencies) => {
  try {
    const bulkDependencies = [];

    for (const dependency of dependencies) {
      const {
        parentQuestionId,
        childQuestionId,
        triggerConditions,
        logicalOperator = "AND",
      } = dependency;

      // Validate that questions exist
      const [parentQuestion, childQuestion] = await Promise.all([
        db.Questions.findByPk(parentQuestionId),
        db.Questions.findByPk(childQuestionId),
      ]);

      if (!parentQuestion || !childQuestion) {
        throw new Error("Parent or child question not found for a dependency");
      }

      //if child question has attachment required then thow a error it can not be added as dependency
      if (childQuestion?.attachment_required) {
        throw new Error(
          `Child question has attachment required. Please provide a valid dependency without an attachment`
        );
      }

      //if child question type is mutiple then in condition you can not add less than or greater than
      const multipleTypeId = await db.QuestionTypes.findOne({
        where: { title: CONSTANTS.QUESTION_TYPES.MULTIPLE },
      });

      if (
        multipleTypeId?.id === childQuestion?.question_type_id &&
        triggerConditions?.findIndex(
          (item) =>
            item?.operator === CONSTANTS.QUESTION_CONDITION.LESS_THAN ||
            item?.operator === CONSTANTS.QUESTION_CONDITION.GREATER_THAN
        ) !== -1
      ) {
        throw new Error(
          `Child question can not have a less than or greater than condition`
        );
      }

      //check if child question has a child question or not
      const childQuestionHasChild = await db.QuestionDependencies.findOne({
        where: {
          parent_question_id: childQuestionId,
        },
      });

      if (childQuestionHasChild) {
        throw new Error(
          `Child question has a child question.Please provide a valid dependency without a child question`
        );
      }

      // Validate trigger conditions
      for (const condition of triggerConditions) {
        if (condition.option_ids?.length) {
          // Verify all option IDs exist for the parent question
          const validOptions = await db.QuestionOptions.count({
            where: {
              id: condition.option_ids,
              question_id: parentQuestionId,
            },
          });

          if (validOptions !== condition.option_ids.length) {
            throw new Error("Invalid option IDs provided for a dependency");
          }
        }
      }

      // Prepare dependency for bulk insert
      bulkDependencies.push({
        parent_question_id: parentQuestionId,
        child_question_id: childQuestionId,
        trigger_conditions: triggerConditions,
        logical_operator: logicalOperator,
      });
    }

    // Bulk create dependencies
    await db.QuestionDependencies.bulkCreate(bulkDependencies);
  } catch (error) {
    console.error("Error mapping question dependencies in bulk:", error);
    throw error;
  }
};

const handleQuestionDependencies = async (
  request,
  response,
  questionId,
  transaction
) => {
  try {
    // Get all existing active dependencies for the question
    const existingDependencies = await db.QuestionDependencies.findAll({
      where: {
        parent_question_id: questionId,
        is_active: 1,
      },
      transaction,
    });

    // Create a set of dependency IDs from the request
    const newDependencyIds = new Set(
      (request.body.dependencies || [])
        .filter((dep) => dep.id)
        .map((dep) => dep.id)
    );

    // Deactivate dependencies that aren't in the new list
    for (const existingDep of existingDependencies) {
      if (!newDependencyIds.has(existingDep.id)) {
        await existingDep.update(
          {
            is_active: 0,
            updated_at: new Date(),
          },
          { transaction }
        );
      }
    }

    const { dependencies } = request.body;
    if (!dependencies?.length) return true;

    for (const dependency of dependencies) {
      if (dependency.id) {
        // Update existing dependency
        const existingDependency = await db.QuestionDependencies.findOne({
          where: {
            id: dependency.id,
            is_active: 1,
          },
          transaction,
        });

        if (!existingDependency) {
          return response.handler.badRequest(
            "Dependency not found or inactive"
          );
        }

        // Validate options for parent question
        if (dependency.triggerConditions) {
          for (const condition of dependency.triggerConditions) {
            if (condition.option_ids?.length) {
              const validOptions = await db.QuestionOptions.count({
                where: {
                  id: condition.option_ids,
                  question_id: existingDependency.parent_question_id,
                  is_active: 1,
                  is_deleted: 0,
                },
                transaction,
              });

              if (validOptions !== condition.option_ids.length) {
                return response.handler.badRequest(
                  `Invalid option IDs provided for dependency ${dependency.id}`
                );
              }
            }
          }
        }

        // Update the dependency
        await existingDependency.update(
          {
            trigger_conditions: dependency.triggerConditions,
            logical_operator: dependency.logicalOperator,
            updated_at: new Date(),
          },
          { transaction }
        );
      } else {
        // Create new dependency
        // First validate the child question exists
        const childQuestion = await common.getQuestionById(
          dependency.childQuestionId
        );
        if (!childQuestion) {
          return response.handler.badRequest(
            `Child question ${dependency.childQuestionId} does not exist`
          );
        }

        // Validate options for the new dependency
        if (dependency.triggerConditions) {
          for (const condition of dependency.triggerConditions) {
            if (condition.option_ids?.length) {
              const validOptions = await db.QuestionOptions.count({
                where: {
                  id: condition.option_ids,
                  question_id: questionId,
                  is_active: 1,
                  is_deleted: 0,
                },
                transaction,
              });

              if (validOptions !== condition.option_ids.length) {
                return response.handler.badRequest(
                  "Invalid option IDs provided for new dependency"
                );
              }
            }
          }
        }

        // Create the new dependency
        await db.QuestionDependencies.create(
          {
            parent_question_id: questionId,
            child_question_id: dependency.childQuestionId,
            trigger_conditions: dependency.triggerConditions,
            logical_operator: dependency.logicalOperator,
            is_active: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
          { transaction }
        );
      }
    }

    return true;
  } catch (error) {
    console.error("Error handling dependencies:", error);
    return response.handler.serverError(error);
  }
};

module.exports = {
  mapQuestionDependency,
  deactivateDependency,
  updateDependency,
  reactivateDependency,
  mapQuestionDependenciesInBulk,
  handleQuestionDependencies,
};
