const { Op } = require("sequelize");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");

const get = async (request, response) => {
    try {
        const { id, search } = request.query;
        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        let whereCond = [
            { is_active: 1 },
            { is_deleted: 0 },
        ];

        id && whereCond.push({ id });

        if (search) {
            whereCond.push({ title: { [Op.like]: `%${search}%` } });
        }

        const { count: totalCount, rows: queTypeData } = await db.QuestionTypes.findAndCountAll({
            limit: limit,
            offset: start,
            where: whereCond,
            attributes: ATTRIBUTES.QUESTION_TYPES,
            order: [["id", "DESC"]],
        });

        if (!queTypeData) {
            return response.handler.badRequest(MESSAGE.QUESTION_TYPE.FOUND.ERROR);
        }
        else if (totalCount === 0 || queTypeData.length === 0) {
            return response.handler.notFound(MESSAGE.QUESTION_TYPE.FOUND.FAILED);
        }
        else {
            return response.handler.success(
                MESSAGE.QUESTION_TYPE.FOUND.SUCCESS,
                { totalCount: totalCount, count: queTypeData.length, question_types: queTypeData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    get,
}
