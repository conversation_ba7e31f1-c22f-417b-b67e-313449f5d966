const { Op } = require("sequelize");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");
const { getPadString } = require("../helpers/functions.js");

const create = async (request, response) => {
  try {
    const { name, is_mandatory = false, number = 0 } = request.body;
    const reportTagExist = await common.getReportTagByName(name);

    if (reportTagExist) {
      return response.handler.conflict(MESSAGE.REPORT_TAG.EXIST);
    }

    const createReportTag = await db.ReportTags.create({
      name,
      is_mandatory,
      number,
    });

    if (createReportTag) {
      const custom_id = await getPadString(createReportTag.id, "RT");
      await db.Orders.update(
        { custom_id },
        { where: { id: createReportTag.id } }
      );

      return response.handler.success(MESSAGE.REPORT_TAG.CREATE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.REPORT_TAG.CREATE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const { id, search, is_active } = request.query;

    let whereCond = [];
    id && whereCond.push({ id });

    if (search) {
      const searchArr = [{ name: { [Op.like]: `%${search}%` } }];
      whereCond.push({ [Op.or]: searchArr });
    }

    if (typeof is_active === "string") {
      whereCond.push({ is_active: is_active });
    }

    let query = {
      where: whereCond,
      order: [["id", "DESC"]],
      attributes: ATTRIBUTES.REPORT_TAGS,
    };

    if (request?.query?.page && request?.query?.limit) {
      const page = parseInt(request?.query?.page);
      const limit = parseInt(request?.query?.limit);
      const start = page > 1 ? (page - 1) * limit : 0;

      query.limit = limit;
      query.offset = start;
    }

    const { count: totalCount, rows: reportTagsData } =
      await db.ReportTags.findAndCountAll(query);

    if (!reportTagsData) {
      return response.handler.badRequest(MESSAGE.REPORT_TAG.FOUND.ERROR);
    } else if (totalCount === 0 || reportTagsData.length === 0) {
      return response.handler.notFound(MESSAGE.REPORT_TAG.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.REPORT_TAG.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: reportTagsData.length,
        report_tags: reportTagsData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const edit = async (request, response) => {
  try {
    const { name, is_mandatory, number, id, is_active } = request.body;

    const reportTagExist = await db.ReportTags.findOne({
      where: { id, is_active: is_active ? !is_active : true },
    });

    if (!reportTagExist) {
      return response.handler.conflict(MESSAGE.REPORT_TAG.NOT_EXIST);
    }

    const reportTagExistByName = await common.getReportTagByName(name);

    if (reportTagExistByName && reportTagExistByName?.id !== id) {
      return response.handler.conflict(MESSAGE.REPORT_TAG.EXIST);
    }

    const updateReportTag = await db.ReportTags.update(
      { name, is_mandatory, number, is_active },
      { where: { id } }
    );
    if (updateReportTag)
      return response.handler.success(MESSAGE.REPORT_TAG.EDIT.SUCCESS);
    return response.handler.success(MESSAGE.REPORT_TAG.EDIT.ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};
module.exports = {
  create,
  get,
  edit,
};
