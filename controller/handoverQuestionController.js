const { Op } = require("sequelize");
const { isEmpty } = require("lodash");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");

const create = async (request, response) => {
  try {
    const {
      que_type,
      order,
      title,
      min_number,
      attachment_required,
      max_number,
      options,
      comment_required = null,
    } = request.body;

    // check if question with same title exist or not.
    const questionExist = await db.HandoverQuestions.findOne({
      where: {
        title: { [Op.like]: title },
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (questionExist) {
      return response.handler.conflict(MESSAGE.QUESTION.EXIST);
    }

    // check if question with same order exist or not.
    const questionOrderExist = await db.HandoverQuestions.findOne({
      where: {
        order,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (questionOrderExist) {
      return response.handler.conflict(MESSAGE.QUESTION.ORDER_EXIST);
    }

    if (
      que_type !== CONSTANTS.HANDOVER_QUESTION_TYPES.DESCRIPTIVE &&
      que_type !== CONSTANTS.HANDOVER_QUESTION_TYPES.RATING &&
      que_type !== CONSTANTS.HANDOVER_QUESTION_TYPES.BOOLEAN &&
      !options?.length
    ) {
      return response.handler.badRequest(MESSAGE.QUESTION.OPTIONS.REQUIRED);
    }

    const addQuestion = await db.HandoverQuestions.create({
      order,
      question_type: que_type,
      title: title,
      attachment_required: attachment_required,
      min_number: min_number || null,
      max_number: max_number || null,
      comment_required,
    });

    if (addQuestion) {
      if (
        que_type !== CONSTANTS.HANDOVER_QUESTION_TYPES.DESCRIPTIVE &&
        que_type !== CONSTANTS.HANDOVER_QUESTION_TYPES.RATING &&
        options?.length
      ) {
        let addQueOptions = [];
        for (let i = 0; i < options.length; i++) {
          addQueOptions.push({
            question_id: addQuestion?.id,
            title: options[i],
          });
        }

        await db.HandoverQuestionOptions.bulkCreate(addQueOptions);
      }
      return response.handler.success(MESSAGE.QUESTION.CREATE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.QUESTION.CREATE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const { id, search, status, que_type } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [];
    id && whereCond.push({ id });

    if (search) {
      const searchArr = [{ title: { [Op.like]: `%${search}%` } }];
      whereCond.push({ [Op.or]: searchArr });
    }

    if (status !== undefined) {
      whereCond.push({ is_active: status });
    }

    if (que_type) {
      whereCond.push({ question_type: que_type });
    }

    const { count: totalCount, rows: questionsData } =
      await db.HandoverQuestions.findAndCountAll({
        limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.HANDOVER_QUESTIONS,
        distinct: true,
        order: [["id", "DESC"]],
        include: [
          {
            model: db.HandoverQuestionOptions,
            attributes: ATTRIBUTES.HANDOVER_QUESTION_OPTIONS,
            as: "handover_question_options",
            required: false,
            where: {
              is_active: 1,
            },
          },
        ],
      });

    if (!questionsData) {
      return response.handler.badRequest(MESSAGE.QUESTION.FOUND.ERROR);
    } else if (totalCount === 0 || questionsData.length === 0) {
      return response.handler.notFound(MESSAGE.QUESTION.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.QUESTION.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: questionsData.length,
        questions: questionsData,
      });
    }
  } catch (error) {
    console.error("Error in get questions:", error);
    return response.handler.serverError(error);
  }
};

const updateStatus = async (request, response) => {
  try {
    const { id, status } = request.body;

    const questionData = await db.HandoverQuestions.findOne({
      where: { id, is_active: !status },
    });

    if (!questionData) {
      return response.handler.conflict(MESSAGE.QUESTION.NOT_EXIST);
    }

    await db.HandoverQuestions.update(
      {
        is_active: status,
      },
      {
        where: { id },
      }
    );
    return response.handler.success(MESSAGE.QUESTION.STATUS_CHANGE.SUCCESS);
  } catch (error) {
    console.error("Error in get questions:", error);
    return response.handler.serverError(error);
  }
};

module.exports = {
  create,
  get,
  updateStatus,
};
