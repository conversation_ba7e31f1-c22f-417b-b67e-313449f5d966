const { Op } = require("sequelize");
const { isEmpty } = require("lodash");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { MESSAGE } = require("../helpers/messages");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const common = require("./commonFunctions.js");

const create = async (request, response) => {
    try {
        const { title, skill_ids } = request.body;

        const loggedInUser = await request?.login_user?.data;

        // check if requested work order type is exist or not. if exist then we will fetch that id.
        let work_order_type_id;
        const workOrderType = await db.WorkOrderTypes.findOne({
            where: {
                title,
                is_active: 1,
                is_deleted: 0
            },
        });

        if (!isEmpty(workOrderType)) {
            work_order_type_id = await workOrderType?.id;
            return response.handler.conflict(MESSAGE.WORK_ORDER_TYPE.EXIST);
        }
        else {
            const addWorkOrderType = await db.WorkOrderTypes.create({
                title: title
            });

            if (addWorkOrderType) {
                work_order_type_id = await addWorkOrderType?.id;
            }
            else {
                return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.ADD.ERROR);
            }
        }

        // while adding new work order type we need to manage points against to that work order type. so if point not exist against wo type then we need to add.
        const checkPointExist = await db.WorkOrderTypePoints.count({
            where: { work_order_type_id }
        });

        if (!checkPointExist) {
            await db.WorkOrderTypePoints.create({
                work_order_type_id: work_order_type_id,
                points_to_assign: 0,
                transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT,
                added_by: loggedInUser.id
            });
        }

        let addData = [];
        // if work order type & skill already mapped, then we will skip that record.
        let mappedSkillsId = [];
        const workOrderTypeSkillExist = await db.WorkOrderTypeSkills.findAll({
            where: { work_order_type_id: work_order_type_id }
        });

        if (workOrderTypeSkillExist.length) {
            mappedSkillsId = await workOrderTypeSkillExist.map((data) => data.skill_id)
        }

        for (let i = 0; i < skill_ids.length; i++) {
            if (!mappedSkillsId.includes(skill_ids[i])) {
                addData.push({
                    work_order_type_id: work_order_type_id,
                    skill_id: skill_ids[i]
                });
            }
        }

        const addWorkOrderTypeSkill = await db.WorkOrderTypeSkills.bulkCreate(addData);

        if (addWorkOrderTypeSkill) {
            return response.handler.success(MESSAGE.WORK_ORDER_TYPE_SKILL.ADD.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE_SKILL.ADD.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const update = async (request, response) => {
    try {
        const { id, title } = request.body;
        // check if work order type is exist with requested id or not.
        const woTypeExist = await common.getWorkOrderTypeById(id);
        if (!woTypeExist) {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
        }

        // check if requested title is unique or not.
        const checkWoTypeTitle = await common.getWoTypeByConditions([{ id: { [Op.ne]: id } }, { title }]);
        if (checkWoTypeTitle) {
            return response.handler.conflict(MESSAGE.WORK_ORDER_TYPE.EXIST);
        }

        const updateWoType = await db.WorkOrderTypes.update(
            { title: title },
            { where: { id } }
        );

        if (updateWoType) {
            return response.handler.success(MESSAGE.WORK_ORDER_TYPE.UPDATE.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.UPDATE.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const get = async (request, response) => {
    try {
        const { id, search, status } = request.query;

        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        // get skills from user authentication service.
        let skillsData = [];
        const skillAPI = await userService.getAllSkills({ headers: request?.headers });

        if (skillAPI) {
            skillsData = skillAPI
        }

        let whereCond = [];
        id && whereCond.push({ id });
        if (status !== undefined) {
            whereCond.push({ is_active: status });
        }

        if (search) {
            const searchArr = [
                { title: { [Op.like]: `%${search}%` } },
            ];

            whereCond.push({ [Op.or]: searchArr });
        }

        const { count: totalCount, rows: wo_type_data } = await db.WorkOrderTypes.findAndCountAll({
            limit: limit,
            offset: start,
            where: whereCond,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
            order: [["id", "DESC"]],
            distinct: true,
            include: [
                {
                    model: db.WorkOrderTypeSkills,
                    as: "wo_type_skills",
                    required: false,
                    attributes: ATTRIBUTES.WORK_ORDER_TYPE_SKILLS
                }
            ]
        });

        if (!wo_type_data) {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.FOUND.ERROR);
        }
        else if (totalCount === 0 || wo_type_data.length === 0) {
            return response.handler.notFound(MESSAGE.WORK_ORDER_TYPE.FOUND.FAILED);
        }
        else {
            let respData = [];
            await wo_type_data.map(async (ele) => {
                let skData = [];
                if (ele?.wo_type_skills?.length) {
                    await ele.wo_type_skills.map(async (sData) => {
                        const skill = await skillsData.find((sd) => sd.id === sData.skill_id);
                        skData.push(skill || {})
                    });
                }

                respData.push({
                    id: ele.id,
                    title: ele.title,
                    is_active: ele.is_active,
                    is_deleted: ele.is_deleted,
                    skills: skData,
                });
            });

            return response.handler.success(
                MESSAGE.WORK_ORDER_TYPE.FOUND.SUCCESS,//respData //wo_type_data
                { totalCount: totalCount, count: wo_type_data.length, work_order_types: respData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const changeStatus = async (request, response) => {
    try {
        const { id, status } = request.body;

        // check if work order type is exist with requested id or not.
        const woTypeExist = await common.getWorkOrderTypeById(id);
        if (!woTypeExist) {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
        }

        // if current status of work order type is active and user request to inactive, then we will check if that work order type is mapped with any work order or que set or not. if mapped then we will restrict to change the status.
        if (!status) {
            const woTypeQueSet = await db.QuestionMapping.count({
                where: { work_order_type_id: id }
            });

            const woQueSet = await db.WorkOrders.count({
                where: { work_order_type_id: id }
            });

            if (woTypeQueSet || woQueSet) {
                return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.STATUS_CHANGE.FAILED_DUE_TO_LINKED_TO_WO_OR_QUESET);
            }
        }

        if (status) {
            // check if same work order type name is exist with active status then we will restrict to change the status.
            const isActiveWOTypeExist = await common.getWoTypeByConditions([{ title: woTypeExist.title }, { is_active: 1 }, { is_deleted: 0 }]);

            if (!isEmpty(isActiveWOTypeExist)) {
                return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.STATUS_CHANGE.FAILED_DUE_TO_SAME_EXIST);
            }
        }

        // updadte status of work order types
        const changeWoTypeStatus = await db.WorkOrderTypes.update(
            { is_active: status },
            { where: { id } }
        );

        if (changeWoTypeStatus) {
            return response.handler.success(MESSAGE.WORK_ORDER_TYPE.STATUS_CHANGE.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.STATUS_CHANGE.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    create,
    update,
    get,
    changeStatus,
}
