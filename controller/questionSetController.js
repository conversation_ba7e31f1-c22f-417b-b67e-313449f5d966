const { Op } = require("sequelize");
const { isEmpty } = require("lodash");

const db = require("../database/models");
const aws = require("../helpers/awsS3.js");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");

const create = async (request, response) => {
  try {
    const { work_order_type_id, scope_id, mapping_data } = request.body;

    // check if requested work_order_type is exist or not.
    const woTypeExist = await common.getWorkOrderTypeById(work_order_type_id);
    if (!woTypeExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
    }

    // check if requested scope is exist or not.
    const scopeExist = await common.getScopeById(scope_id);
    if (!scopeExist) {
      return response.handler.badRequest(MESSAGE.SCOPE.NOT_EXIST);
    }

    /*
            - check if question set exist or not with requested work_order_type_id & scope_id
            - if exist and active, then we will restrict to create new question set.
        */

    const questionSetExist = await db.QuestionMapping.count({
      where: [
        { work_order_type_id: work_order_type_id },
        { scope_id: scope_id },
        { is_active: 1 },
        { is_deleted: 0 },
      ],
    });

    if (questionSetExist) {
      return response.handler.conflict(MESSAGE.QUESTION_SET.EXIST);
    }

    // create mapping for wo_type & scope
    const createQuestionMapping = await db.QuestionMapping.create({
      work_order_type_id: work_order_type_id,
      scope_id: scope_id,
      // touch_point: touch_point
    });

    if (createQuestionMapping) {
      for (let i = 0; i < mapping_data.length; i++) {
        const touch_point = mapping_data[i].touch_point;
        const question_ids = mapping_data[i].question_ids;

        const question_mapping_id = await createQuestionMapping?.id;

        // create mapping for wo_type & scope & touch point
        const createQueMappingTp = await db.QuestionMappingTouchPoints.create({
          question_mapping_id: question_mapping_id,
          touch_point: touch_point,
        });

        if (createQueMappingTp) {
          const question_mapping_touch_point_id = await createQueMappingTp?.id;

          // create mapping for tp with questions
          let data = [];
          for (let i = 0; i < question_ids.length; i++) {
            data.push({
              question_mapping_touch_point_id,
              question_id: question_ids[i],
            });
          }

          const createQueSet = await db.QuestionMappingSet.bulkCreate(data);
        }
      }
    }

    return response.handler.success(MESSAGE.QUESTION_SET.CREATE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const changeStatus = async (request, response) => {
  try {
    const { id, status } = request.body;

    // check if question set available with requested id or not.
    const queSetExist = await common.getQuestionMappingById(id);

    if (!queSetExist) {
      return response.handler.badRequest(MESSAGE.QUESTION_SET.NOT_EXIST);
    }

    // if current status of question set is active and user request to inactive, then we will check if that que set is mapped with any task or not. if mapped then we will restrict to change the status.
    if (!status) {
      const queSetTask = await db.Tasks.count({
        where: { question_mapping_id: id },
      });

      if (queSetTask) {
        return response.handler.badRequest(
          MESSAGE.QUESTION_SET.STATUS_CHANGE.FAILED_DUE_TO_LINKED_TASK
        );
      }
    }

    if (status) {
      // check if question set is exist with work_order_type_id & scope_id and active or not. if availabel then we will restrict to change status.
      const check = await db.QuestionMapping.findOne({
        where: {
          work_order_type_id: queSetExist.work_order_type_id,
          scope_id: queSetExist.scope_id,
          is_active: 1,
          is_deleted: 0,
        },
      });

      if (!isEmpty(check)) {
        return response.handler.badRequest(
          MESSAGE.QUESTION_SET.STATUS_CHANGE.FAILED_DUE_TO_SAME_EXIST
        );
      }
    }

    const changeQuestionSetStatus = await db.QuestionMapping.update(
      { is_active: status },
      { where: { id } }
    );

    if (changeQuestionSetStatus) {
      return response.handler.success(
        MESSAGE.QUESTION_SET.STATUS_CHANGE.SUCCESS
      );
    } else {
      return response.handler.badRequest(
        MESSAGE.QUESTION_SET.STATUS_CHANGE.ERROR
      );
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const { search } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [];
    if (search) {
      const searchArr = [
        { "$work_order_type_data.title$": { [Op.like]: `%${search}%` } },
        { "$scope_data.model$": { [Op.like]: `%${search}%` } },
        { "$scope_data.name$": { [Op.like]: `%${search}%` } },
      ];

      whereCond.push({ [Op.or]: searchArr });
    }
    const { count: totalCount, rows: questionSet } =
      await db.QuestionMapping.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        distinct: true,
        attributes: ATTRIBUTES.QUESTION_MAPPING,
        order: [["id", "DESC"]],
        include: [
          {
            model: db.WorkOrderTypes,
            as: "work_order_type_data",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
          {
            model: db.Scopes,
            as: "scope_data",
            required: true,
            attributes: [
              ...ATTRIBUTES.SCOPES,
              [
                db.sequelize.fn(
                  "CONCAT",
                  db.sequelize.col("height"),
                  " ⨯ ",
                  db.sequelize.col("width")
                ),
                "dimension",
              ],
            ],
            include: [
              {
                model: db.ScopeImages,
                as: "scope_image",
                required: true,
                attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                  (value) => value !== "base64"
                ),
                include: [
                  {
                    model: db.ScopeImageTouchPoints,
                    as: "touch_points",
                    required: true,
                    attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                  },
                ],
              },
            ],
          },
          {
            model: db.QuestionMappingTouchPoints,
            as: "question_mapping_touch_points",
            required: false,
            attributes: ATTRIBUTES.QUESTION_MAPPING_TOUCH_POINTS,
            include: [
              {
                model: db.QuestionMappingSet,
                as: "question_set_data",
                required: false,
                attributes: ATTRIBUTES.QUESTION_MAPPING_SET,
                include: [
                  {
                    model: db.Questions,
                    as: "question_data",
                    required: false,
                    where: { is_active: 1 },
                    attributes: ATTRIBUTES.QUESTIONS,
                    include: [
                      {
                        model: db.QuestionTypes,
                        as: "question_type",
                        required: false,
                        attributes: ATTRIBUTES.QUESTION_TYPES,
                      },
                      {
                        model: db.QuestionOptions,
                        as: "question_options",
                        required: false,
                        attributes: ATTRIBUTES.QUESTION_OPTIONS,
                      },
                      {
                        model: db.ReportTags,
                        as: "report_tag",
                        required: false,
                        attributes: ATTRIBUTES.REPORT_TAGS,
                        where: { is_active: 1 },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      });

    if (!questionSet) {
      return response.handler.badRequest(MESSAGE.QUESTION_SET.FOUND.ERROR);
    } else if (totalCount === 0 || questionSet.length === 0) {
      return response.handler.notFound(MESSAGE.QUESTION_SET.FOUND.FAILED);
    } else {
      let respData = [];

      for (let i = 0; i < questionSet.length; i++) {
        const tp = questionSet[i].question_mapping_touch_points;
        let question_set = [];
        for (let j = 0; j < tp.length; j++) {
          const touch_point = await questionSet[
            i
          ]?.scope_data?.scope_image?.touch_points.find(
            (data) => data?.touch_point === tp[j]?.touch_point
          );

          question_set.push({
            touch_point: {
              ...JSON.parse(JSON.stringify(touch_point)),
              question_mapping_touch_point_id:
                tp[j]?.question_set_data[0].question_mapping_touch_point_id,
            },
            questions: tp[j].question_set_data?.filter(
              (item) => item?.question_data
            ),
          });
        }

        respData.push({
          id: questionSet[i].id,
          work_order_type_id: questionSet[i].work_order_type_id,
          scope_id: questionSet[i].scope_id,
          is_active: questionSet[i].is_active,
          work_order_type_data: questionSet[i].work_order_type_data,
          scope_data: questionSet[i].scope_data,
          question_set,
        });
      }

      return response.handler.success(MESSAGE.QUESTION_SET.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: questionSet.length,
        question_sets: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getByWoTypeScope = async (request, response) => {
  try {
    const { work_order_type_id, scope_id, task_id } = request.query;
    // check if work order type exist with requested id or not.
    const woTypeExist = await common.getWorkOrderTypeById(work_order_type_id);
    if (!woTypeExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
    }

    // check if scope is exist with requested id or not.
    const scopeExist = await common.getScopeById(scope_id);
    if (!scopeExist) {
      return response.handler.badRequest(MESSAGE.SCOPE.NOT_EXIST);
    }

    /*
            - check if question set exist with requested work_order_type_id & scope_id or not.
            - if question set not available then, take model of scope as Default and check question set with that scope.
        */
    let queSet = {};
    const findQuestionSet = async (work_order_type_id, scope_id) => {
      const data = await db.QuestionMapping.findOne({
        where: {
          work_order_type_id,
          scope_id,
          is_active: 1,
          is_deleted: 0,
        },
        attributes: ATTRIBUTES.QUESTION_MAPPING,
      });

      return !isEmpty(data) ? data : {};
    };

    const queSetExist = await findQuestionSet(work_order_type_id, scope_id);

    if (!isEmpty(queSetExist)) {
      queSet = queSetExist;
    } else {
      // check question set with default scope's model
      const scopeWithDefaultModel = await common.getScopeByModelAndName(
        CONSTANTS.SCOPE.DEFAULT_MODEL,
        scopeExist.name
      );

      if (!scopeWithDefaultModel) {
        return response.handler.badRequest(MESSAGE.QUESTION_SET.NOT_EXIST);
      }

      const queSetExist2 = await findQuestionSet(
        work_order_type_id,
        scopeWithDefaultModel.id
      );

      if (!isEmpty(queSetExist2)) {
        queSet = queSetExist2;
      } else {
        return response.handler.badRequest(MESSAGE.QUESTION_SET.NOT_EXIST);
      }
    }

    let whereCond = [{ question_mapping_id: queSet?.id }];

    if (task_id) {
      whereCond.push(
        db.sequelize.where(
          db.sequelize.col(
            "question_set_data->question_data->question_answer->task_touch_point.touch_point"
          ),
          db.sequelize.col("QuestionMappingTouchPoints.touch_point")
        )
      );
    }

    let taskQuestionInclude = [
      {
        model: db.TaskTouchPoints,
        as: "task_touch_point",
        required: false,
        where: task_id && { task_id },
        attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
      },
    ];

    if (task_id) {
      taskQuestionInclude.push(
        {
          model: db.TaskAnswers,
          as: "answer_data",
          required: false,
          where: {
            [Op.or]: [
              { is_active: 1, is_deleted: 0 },
              { is_active: null },
              { is_deleted: null },
            ],
          },
          attributes: ATTRIBUTES.TASK_ANSWERS,
        },
        {
          model: db.TaskImageAnswers,
          as: "image_answer_data",
          required: false,
          where: {
            [Op.or]: [
              { is_active: 1, is_deleted: 0 },
              { is_active: null },
              { is_deleted: null },
            ],
          },
          attributes: ATTRIBUTES.TASK_IMAGE_ANSWERS.filter(
            (value) => value !== "base64"
          ),
        },
        {
          model: db.TaskVideoAnswers,
          as: "video_answer_data",
          required: false,
          where: {
            is_video_uploaded: 1,
            is_active: 1,
            is_deleted: 0,
          },
          attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
        }
      );
    }

    const questionSet = await db.QuestionMappingTouchPoints.findAll({
      where: whereCond,
      attributes: ATTRIBUTES.QUESTION_MAPPING_TOUCH_POINTS,
      distinct: true,
      order: [["id", "DESC"]],
      include: [
        {
          model: db.QuestionMappingSet,
          as: "question_set_data",
          required: false,
          attributes: ATTRIBUTES.QUESTION_MAPPING_SET,
          include: [
            {
              model: db.Questions,
              as: "question_data",
              required: false,
              attributes: ATTRIBUTES.QUESTIONS,
              include: [
                {
                  model: db.QuestionTypes,
                  as: "question_type",
                  required: false,
                  attributes: ATTRIBUTES.QUESTION_TYPES,
                },
                {
                  model: db.QuestionOptions,
                  as: "question_options",
                  required: false,
                  attributes: ATTRIBUTES.QUESTION_OPTIONS,
                },
                {
                  model: db.ReportTags,
                  as: "report_tag",
                  required: false,
                  attributes: ATTRIBUTES.REPORT_TAGS,
                  where: { is_active: 1 },
                },
                {
                  model: db.TaskQuestions,
                  as: "question_answer",
                  required: false,
                  attributes: ATTRIBUTES.TASK_QUESTIONS,
                  include: taskQuestionInclude,
                },
              ],
            },
          ],
        },
      ],
    });

    if (!questionSet) {
      return response.handler.badRequest(MESSAGE.QUESTION_SET.FOUND.ERROR);
    } else if (questionSet.length === 0) {
      return response.handler.notFound(MESSAGE.QUESTION_SET.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.QUESTION_SET.FOUND.SUCCESS, {
        count: questionSet.length,
        question_sets: questionSet,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const editQuestionSet = async (request, response) => {
  try {
    const { question_set } = request.body;
    if (question_set?.length) {
      //check if all the touch point exist in question_mapping_touch__points
      const touchpoint_ids = question_set?.map((item) => item?.touchpoint_id);

      const queMappingTouchPoints = await db.QuestionMappingTouchPoints.findAll(
        {
          where: { id: { [Op.in]: touchpoint_ids } },
        }
      );

      if (touchpoint_ids?.length !== queMappingTouchPoints?.length) {
        const id = touchpoint_ids?.filter(
          (val) =>
            queMappingTouchPoints?.findIndex((item) => item?.id == val) === -1
        );

        return response.handler.badRequest(
          MESSAGE.QUESTION_SET.EDIT.TOUCH_POINT_NOT_EXIST_IN_SCOPE(
            id.join(", ")
          )
        );
      }

      const questionMappingObj = queMappingTouchPoints?.map((item) => ({
        id: item?.id,
        queMappingId: item?.question_mapping_id,
        touchpoint: item?.touch_point,
      }));

      //chekch if all the edit question and delete question exists or not
      const questionIds = [
        ...new Set(
          question_set
            ?.map((item) => item?.new_question_ids)
            ?.flatMap((id) => id)
            .concat(
              question_set
                ?.map((item) => item?.delete_question_ids)
                ?.flatMap((id) => id)
            )
        ),
      ];

      const isQuestionExist = await db.Questions.findAll({
        where: {
          id: { [Op.in]: questionIds },
          is_active: 1,
        },
      });

      if (isQuestionExist?.length !== questionIds?.length) {
        const id = questionIds?.filter(
          (val) => isQuestionExist?.findIndex((item) => item?.id == val) === -1
        );
        return response.handler.badRequest(
          MESSAGE.QUESTION_SET.EDIT.QUESTION_NOT_AVAILABLE(id.join(", "))
        );
      }

      //get existing touchpoint with their question_ids
      const getExistTouchpointQuestion = await db.QuestionMappingSet.findAll({
        where: {
          question_mapping_touch_point_id: { [Op.in]: touchpoint_ids },
        },
        attributes: ATTRIBUTES.QUESTION_MAPPING_SET,
      });

      const queObj = {};
      if (getExistTouchpointQuestion?.length) {
        getExistTouchpointQuestion?.forEach((item) => {
          const tpId = item?.question_mapping_touch_point_id;
          queObj[tpId] = queObj?.[tpId]?.length
            ? [...queObj?.[tpId], item?.question_id]
            : [item?.question_id];
        });
      }

      //Add new question to question mapping
      //add new question with touchpoint in question_mappings
      let addData = [];
      const deleteQueMapObj = {};
      for (let i = 0; i < question_set.length; i++) {
        const { touchpoint_id, new_question_ids, delete_question_ids } =
          question_set[i];

        if (queObj?.[touchpoint_id]?.length) {
          //check if new question with specific touch points exists or not
          //If exists then throw error
          for (let j of queObj[touchpoint_id]) {
            const index = new_question_ids.indexOf(j);
            if (index !== -1) {
              return response.handler.badRequest(
                MESSAGE.QUESTION_SET.EDIT.QUESTION_EXIST_FOR_TOUCH_POINTS
              );
            }
          }

          //check if delete question with specific touch points exists or not
          //If not exists then throw error
          for (let j of delete_question_ids) {
            const index = queObj?.[touchpoint_id].indexOf(j);
            if (index === -1) {
              return response.handler.badRequest(
                MESSAGE.QUESTION_SET.EDIT.DELETE_QUESTION_NOT_EXIST
              );
            }
          }
        }

        if (new_question_ids?.length) {
          new_question_ids?.forEach((item) => {
            addData.push({
              question_mapping_touch_point_id: touchpoint_id,
              question_id: item,
            });
          });
        }

        //create object for delete question ids with touchpoint as a key and id as a value
        if (delete_question_ids?.length) {
          const index = questionMappingObj?.findIndex(
            (item) => item?.id === touchpoint_id
          );
          if (index !== -1)
            deleteQueMapObj[questionMappingObj?.[index]?.touchpoint] =
              delete_question_ids;
        }
      }

      //if delete object contains any data then
      if (Object.keys(deleteQueMapObj)?.length) {
        //get task id based on question mapping id
        const queMappingId = questionMappingObj?.[0]?.queMappingId;
        const taskData = await db.Tasks.findAll({
          where: { question_mapping_id: queMappingId },
        });

        if (taskData?.length) {
          const taskIds = await taskData?.map((item) => item?.id);

          //get task touch points based on task id
          const taskTouchPoints = await db.TaskTouchPoints.findAll({
            where: { task_id: { [Op.in]: taskIds } },
            include: [
              {
                model: db.TaskQuestions,
                as: "task_questions",
              },
            ],
          });

          const deleteTaskQueIds = [];
          for (let i = 0; i < taskTouchPoints?.length; i++) {
            const taskTouchPointsData = taskTouchPoints[i];
            const queId = deleteQueMapObj?.[taskTouchPointsData?.touch_point];

            if (queId?.length) {
              queId?.forEach((que) => {
                const data = taskTouchPointsData?.task_questions?.filter(
                  (taskTp) => taskTp?.question_id === que
                );
                if (data?.length) {
                  deleteTaskQueIds.push(...data?.map((d) => d?.id));
                }
              });
            }
          }

          if (deleteTaskQueIds?.length > 0) {
            const taskVideoAnswersData = await db.TaskVideoAnswers.findAll({
              where: { task_question_id: { [Op.in]: deleteTaskQueIds } },
              attributes: ["id", "video_key"],
            });
            const taskImageAnswersData = await db.TaskImageAnswers.findAll({
              where: { task_question_id: { [Op.in]: deleteTaskQueIds } },
              attributes: ["id", "image_key"],
            });
            if (taskImageAnswersData.length) {
              for (const data of taskImageAnswersData) {
                await aws.deleteFileS3(data.image_key);
              }
              const image_answer_ids = taskImageAnswersData.map(
                (data) => data.id
              );
              await db.TaskImageAnswers.destroy({
                where: { id: { [Op.in]: image_answer_ids } },
              });
            }
            if (taskVideoAnswersData.length) {
              for (const data of taskVideoAnswersData) {
                await aws.deleteFileS3(data.video_key);
              }
              const video_answer_ids = taskVideoAnswersData.map(
                (data) => data.id
              );
              await db.TaskVideoAnswers.destroy({
                where: { id: { [Op.in]: video_answer_ids } },
              });
            }
            await db.TaskAnswers.destroy({
              where: { task_question_id: { [Op.in]: deleteTaskQueIds } },
            });
            await db.TaskQuestions.destroy({
              where: { id: { [Op.in]: deleteTaskQueIds } },
            });
          }
        }

        for (let i = 0; i < question_set.length; i++) {
          const { touchpoint_id, delete_question_ids } = question_set[i];

          if (delete_question_ids?.length) {
            await db.QuestionMappingSet.destroy({
              where: {
                question_mapping_touch_point_id: touchpoint_id,
                question_id: { [Op.in]: delete_question_ids },
              },
            });
          }
        }
      }

      addData?.length && (await db.QuestionMappingSet.bulkCreate(addData));

      return response.handler.success(MESSAGE.QUESTION_SET.EDIT.SUCCESS);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  create,
  changeStatus,
  get,
  getByWoTypeScope,
  editQuestionSet,
};
