const db = require("../database/models");
const { MESSAGE } = require("../helpers/messages");

const getVersionData = async (request, response) => {
  try {
    const data = await db.ApkVersions.findOne({});
    if (data) {
      return response.handler.success("Data fetched successfully.", data);
    }
    return response.handler.badRequest(MESSAGE.GENERAL_ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  getVersionData,
};
