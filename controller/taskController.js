const { Op, Sequelize } = require("sequelize");
const fs = require("fs");

const db = require("../database/models");

const {
  getCurrentDateTimeUTC,
  generateRandomAlphaNumericString,
} = require("../helpers/functions");
const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const aws = require("../helpers/awsS3.js");
const common = require("./commonFunctions.js");
const userService = require("../helpers/userAuthenticationServiceApis.js");

const get = async (request, response) => {
  try {
    const { work_order_id, que_required, status } = request.query;

    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);
    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    let includeData = [
      {
        model: db.WorkOrders,
        as: "work_order_data",
        required: false,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        include: [
          {
            model: db.WorkOrderTypes,
            as: "work_order_type",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
        ],
      },
      {
        model: db.WorkOrderScopes,
        as: "work_order_task_scope",
        required: false,
        attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
        include: [
          {
            model: db.Scopes,
            as: "scope_data",
            required: false,
            attributes: [
              ...ATTRIBUTES.SCOPES,
              [
                db.sequelize.fn(
                  "CONCAT",
                  db.sequelize.col("work_order_task_scope.scope_data.height"),
                  " ⨯ ",
                  db.sequelize.col("work_order_task_scope.scope_data.width")
                ),
                "dimension",
              ],
            ],
            include: [
              {
                model: db.ScopeImages,
                as: "scope_image",
                required: false,
                attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                  (value) => value !== "base64"
                ),
                include: [
                  {
                    model: db.ScopeImageTouchPoints,
                    as: "touch_points",
                    required: false,
                    attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                  },
                ],
              },
            ],
          },
          {
            model: db.WorkOrderSpaces,
            as: "work_order_task_space",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
            ],
          },
        ],
      },
      {
        model: db.TaskTouchPoints,
        as: "task_touch_points",
        required: false,
        attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
      },
    ];
    let questionInclude = [];
    if (que_required) {
      ////////////////////////////////////////////////////////////
      // Create separate task query structures for different paths to avoid alias conflicts

      // Updated: Added parent_question_id field to where clause to distinguish between parent and child questions
      const mainTaskQuesQuery = [
        {
          model: db.TaskQuestions,
          as: "question_answer",
          required: false,
          where: {
            task_touch_point_id: {
              [Op.in]: db.sequelize.literal(
                `(SELECT id FROM task_touch_points WHERE task_id = Tasks.id)`
              ),
            },
            // Only include top-level questions (not subquestions)
            parent_question_id: null,
          },
          attributes: [
            ...ATTRIBUTES.TASK_QUESTIONS,
            "parent_question_id",
            "isSubquestion",
          ],
          include: [
            {
              model: db.TaskTouchPoints,
              as: "task_touch_point",
              required: false,
              attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
            },
            {
              model: db.TaskAnswers,
              as: "answer_data",
              required: false,
              where: {
                [Op.or]: [
                  { is_active: 1, is_deleted: 0 },
                  { is_active: null, is_deleted: null },
                ],
              },
              attributes: ATTRIBUTES.TASK_ANSWERS,
            },
            {
              model: db.TaskImageAnswers,
              as: "image_answer_data",
              required: false,
              where: {
                [Op.or]: [
                  { is_active: 1, is_deleted: 0 },
                  { is_active: null, is_deleted: null },
                ],
              },
              attributes: ATTRIBUTES.TASK_IMAGE_ANSWERS.filter(
                (value) => value !== "base64"
              ),
            },
            {
              model: db.TaskVideoAnswers,
              as: "video_answer_data",
              required: false,
              where: {
                is_video_uploaded: 1,
                is_active: 1,
                is_deleted: 0,
              },
              attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
            },
          ],
        },
      ];

      // Main query structure
      questionInclude.push({
        model: db.QuestionMapping,
        as: "question_mapping",
        required: false,
        attributes: ATTRIBUTES.QUESTION_MAPPING,
        include: [
          {
            model: db.QuestionMappingTouchPoints,
            as: "question_mapping_touch_points",
            required: false,
            attributes: ATTRIBUTES.QUESTION_MAPPING_TOUCH_POINTS,
            include: [
              {
                model: db.QuestionMappingSet,
                as: "question_set_data",
                required: false,
                attributes: ATTRIBUTES.QUESTION_MAPPING_SET,
                include: [
                  {
                    model: db.Questions,
                    as: "question_data",
                    where: {
                      [Op.or]: [
                        {
                          is_active: { [Op.in]: [0, 1] },
                          "$Tasks.status$": CONSTANTS.TASK_STATUS.COMPLETED,
                        },
                        {
                          is_active: 1,
                          "$Tasks.status$": {
                            [Op.notIn]: [CONSTANTS.TASK_STATUS.COMPLETED],
                          },
                        },
                      ],
                    },
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.QUESTIONS,
                      [
                        Sequelize.literal(`
                    \`Tasks\`.\`updatedAt\` < \`question_mapping->question_mapping_touch_points->question_set_data\`.\`createdAt\` 
                  `),
                        "isNewAdded",
                      ],
                    ],
                    include: [
                      {
                        model: db.QuestionTypes,
                        as: "question_type",
                        required: false,
                        attributes: ATTRIBUTES.QUESTION_TYPES,
                      },
                      {
                        model: db.QuestionOptions,
                        as: "question_options",
                        required: false,
                        where: { is_active: 1, is_deleted: 0 },
                        attributes: ATTRIBUTES.QUESTION_OPTIONS,
                      },
                      {
                        model: db.ReportTags,
                        as: "report_tag",
                        required: false,
                        attributes: ATTRIBUTES.REPORT_TAGS,
                        where: { is_active: 1 },
                      },
                      {
                        model: db.QuestionDependencies,
                        as: "child_dependencies",
                        required: false,
                        where: {
                          is_active: true,
                        },
                        include: [
                          {
                            model: db.Questions,
                            as: "child_question",
                            attributes: ATTRIBUTES.QUESTIONS,
                            include: [
                              {
                                model: db.QuestionTypes,
                                as: "question_type",
                                attributes: ATTRIBUTES.QUESTION_TYPES,
                              },
                              {
                                model: db.QuestionOptions,
                                as: "question_options",
                                where: { is_active: 1, is_deleted: 0 },
                                attributes: ["id", "question_id", "title"],
                              },
                            ],
                          },
                        ],
                      },
                      ...mainTaskQuesQuery,
                    ],
                  },
                ],
              },
            ],
          },
        ],
      });
    } else {
      includeData?.push({
        model: db.QuestionMapping,
        as: "question_mapping",
        required: false,
        attributes: ATTRIBUTES.QUESTION_MAPPING,
      });
    }

    let whereCond = [{ work_order_id: work_order_id }];
    status &&
      JSON.parse(status)?.length &&
      whereCond.push({ status: { [Op.in]: JSON.parse(status) } });

    let tasks = await db.Tasks.findAll({
      where: whereCond,
      attributes: ATTRIBUTES.TASKS,
      include: includeData,
    });

    const taskQuestions = await db.Tasks.findAll({
      where: whereCond,
      include: questionInclude,
    });

    if (!tasks) {
      return response.handler.badRequest(MESSAGE.TASK.FOUND.ERROR);
    } else if (tasks.length === 0) {
      return response.handler.notFound(MESSAGE.TASK.FOUND.FAILED);
    } else {
      let respData = [];

      for (let i = 0; i < tasks.length; i++) {
        const queDataIndex = taskQuestions?.findIndex(
          (item) => item?.id === tasks?.[i]?.id
        );
        const task = {
          ...JSON.parse(JSON.stringify(tasks[i])),
          question_mapping:
            taskQuestions?.[queDataIndex]?.question_mapping ||
            tasks[i]?.question_mapping,
        };

        let question_set = [];
        const que_data = task?.question_mapping?.question_mapping_touch_points;

        for (let j = 0; j < que_data?.length; j++) {
          const data = que_data[j];

          const tpData = task.task_touch_points?.filter(
            (val) => val?.touch_point === data?.touch_point
          )?.[0];

          let questionSetData = [];
          if (task?.status === CONSTANTS.TASK_STATUS.COMPLETED) {
            // Only include questions with answers for completed tasks
            questionSetData = que_data?.[j]?.question_set_data?.filter(
              (item) =>
                item?.question_data &&
                item?.question_data?.question_answer?.length &&
                item?.question_data?.question_answer?.filter(
                  (que_answer) =>
                    que_answer?.answer_data?.length ||
                    que_answer?.image_answer_data?.length ||
                    que_answer?.video_answer_data
                )?.length
            );
          } else {
            // Include all questions for tasks not completed
            questionSetData = que_data?.[j]?.question_set_data?.filter(
              (item) => item?.question_data
            );
          }

          // Modified: This is where we process the question data to properly handle parent-child relationships
          questionSetData = questionSetData?.map((item) => {
            let queObj = { ...JSON.parse(JSON.stringify(item)) };

            // Get only top-level questions for this touch point (not subquestions)
            const taskQuestionData =
              queObj?.question_data?.question_answer?.filter((val) => {
                return (
                  val?.task_touch_point?.touch_point === data?.touch_point &&
                  val?.parent_question_id === null
                ); // Only parent questions, not subquestions
              });

            //get an answer for child question (For query optimisation)
            const childDependenciesArray = [];
            if (queObj?.question_data?.child_dependencies?.length) {
              queObj?.question_data?.child_dependencies?.forEach(
                async (item) => {
                  const tmpObj = { ...JSON.parse(JSON.stringify(item)) };
                  const answerData = await db.TaskQuestions.findAll({
                    where: {
                      question_id: item?.child_question_id,
                      isSubquestion: true,
                      parent_question_id: item?.parent_question_id,
                      task_touch_point_id: tpData?.id || null,
                    },
                    include: [
                      {
                        model: db.TaskTouchPoints,
                        as: "task_touch_point",
                        required: false,
                        attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
                      },
                      {
                        model: db.TaskAnswers,
                        as: "answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null, is_deleted: null },
                          ],
                        },
                        attributes: ATTRIBUTES.TASK_ANSWERS,
                      },
                      {
                        model: db.TaskImageAnswers,
                        as: "image_answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null, is_deleted: null },
                          ],
                        },
                        attributes: ATTRIBUTES.TASK_IMAGE_ANSWERS.filter(
                          (value) => value !== "base64"
                        ),
                      },
                      {
                        model: db.TaskVideoAnswers,
                        as: "video_answer_data",
                        required: false,
                        where: {
                          is_video_uploaded: 1,
                          is_active: 1,
                          is_deleted: 0,
                        },
                        attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
                      },
                    ],
                    attributes: [
                      ...ATTRIBUTES.TASK_QUESTIONS,
                      "parent_question_id",
                      "isSubquestion",
                    ],
                  });

                  childDependenciesArray.push({
                    ...tmpObj,
                    child_question: {
                      ...tmpObj?.child_question,
                      question_answer: answerData,
                    },
                  });
                }
              );
            }

            queObj = {
              ...queObj,
              task_question_id: taskQuestionData?.[0]?.id,
              task_question_status: taskQuestionData?.[0]?.status,
              task_question_reason: taskQuestionData?.[0]?.reason,
              question_data: {
                ...queObj?.question_data,
                question_answer: taskQuestionData,
                child_dependencies: childDependenciesArray,
              },
            };
            return queObj;
          });

          question_set.push({
            ...JSON.parse(JSON.stringify(data)),
            reason: tpData?.reason,
            status: tpData?.status,
            tpId: tpData?.id,
            question_set_data: questionSetData,
          });
        }

        let report_tag_data = [];
        if (que_required) {
          const tagIds = [
            ...new Set(
              task?.question_mapping?.question_mapping_touch_points
                ?.flatMap((item) => item?.question_set_data)
                ?.map((data) => data?.question_data?.report_tag_id)
                ?.filter((val) => val)
            ),
          ];

          report_tag_data = await db.ReportTags.findAll({
            where: { id: { [Op.in]: tagIds }, is_active: 1 },
            include: [
              {
                model: db.TaskImageAnswers,
                as: "report_task_image_answers",
                required: false,
                where: { task_id: task?.id, is_active: 1, is_deleted: 0 },
              },
            ],
          });
        }

        const internal_team_user = await common.getInternalTeamData(
          task?.internal_team_id,
          request?.headers
        );

        let scopeData;
        if (
          task.work_order_task_scope.scope_data.id !==
          task?.question_mapping?.scope_id
        ) {
          scopeData = await db.Scopes.findOne({
            where: { id: task?.question_mapping?.scope_id },
            attributes: ATTRIBUTES.SCOPES,
            include: [
              {
                model: db.ScopeImages,
                as: "scope_image",
                required: false,
                attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                  (value) => value !== "base64"
                ),
                include: [
                  {
                    model: db.ScopeImageTouchPoints,
                    as: "touch_points",
                    required: false,
                    attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                  },
                ],
              },
            ],
          });
        }
        respData.push({
          id: task.id,
          internal_team_id: task.internal_team_id,
          internal_team_user,
          report_tag_data,
          work_order_id: task.work_order_id,
          actual_task_id: task.actual_task_id,
          status: task.status,
          updatedAt: task.updatedAt,
          question_mapping_id: task.question_mapping_id,
          is_submitted: task.is_submitted,
          additional_info: task.additional_info,
          work_order_data: task.work_order_data,
          reason: task.reason,
          scope_data: task.work_order_task_scope.scope_data,
          scope_data_default:
            task.work_order_task_scope.scope_data.id !==
            task?.question_mapping?.scope_id
              ? scopeData
              : null,
          space_data:
            task.work_order_task_scope.work_order_task_space.space_data,
          question_sets: question_set,
        });
      }

      return response.handler.success(MESSAGE.TASK.FOUND.SUCCESS, {
        count: tasks.length,
        tasks: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const submitAnswerBKP = async (request, response) => {
  try {
    const { task_id, additional_info, task_data } = request.body;
    // check if task is exist with requested id or not
    const taskExist = await common.getTaskById(task_id);
    if (!taskExist) {
      return response.handler.badRequest(MESSAGE.TASK.NOT_EXIST);
    }

    /*
			- if user tries to resubmit the task we need to verify if task is survey task or any other and check the task status if it is pending or not.
			- only survey task can be resubmitted/updated
		*/
    const workOrder = await common.getWorkOrderById(taskExist.work_order_id);
    // if (taskExist.status === CONSTANTS.TASK_STATUS.COMPLETED && workOrder.work_order_type.title !== CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
    //     return response.handler.badRequest(MESSAGE.TASK.SUBMIT.ALREADY_SUBMITTED);
    // }

    /*
			identify if task answers are resubmitted
			1. check if task is survey or not
			2. check task status if completed
			- if both above condition true, then delete existing answers and create new answers.
		*/
    let isUpdate = true;
    // let isUpdate = false;
    // if (taskExist.status === CONSTANTS.TASK_STATUS.COMPLETED && workOrder.work_order_type.title === CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
    //     isUpdate = true;
    // }

    if (isUpdate) {
      // find task_question_ids of the task
      const taskQueIds = await db.Tasks.findAll({
        where: { id: task_id },
        include: [
          {
            model: db.TaskTouchPoints,
            as: "task_touch_points",
            required: true,
            attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
            include: [
              {
                model: db.TaskQuestions,
                as: "task_questions",
                required: true,
                attributes: ATTRIBUTES.TASK_QUESTIONS,
              },
            ],
          },
        ],
      });

      const taskQueIdsArr = await taskQueIds.flatMap((task) =>
        task.task_touch_points.flatMap((tp) =>
          tp.task_questions.map((tq) => tq.id)
        )
      );

      // deleted all existing answers.
      await db.TaskAnswers.update(
        { is_active: 0, is_deleted: 1 },
        { where: { task_question_id: { [Op.in]: taskQueIdsArr } } }
      );

      const imageAnsIds = task_data
        .flatMap((task) => task.question_answer)
        .flatMap((questionAnswer) => questionAnswer.image_answers_ids || [])
        .filter((id) => id !== undefined);

      await db.TaskImageAnswers.update(
        { is_active: 0, is_deleted: 1 },
        {
          where: {
            task_question_id: { [Op.in]: taskQueIdsArr },
            id: { [Op.notIn]: imageAnsIds },
          },
        }
      );

      const videoAnsIds = task_data
        .flatMap((task) => task.question_answer)
        .flatMap((questionAnswer) => questionAnswer.video_answer_id || [])
        .filter((id) => id !== undefined);

      await db.TaskVideoAnswers.update(
        { is_active: 0, is_deleted: 1 },
        {
          where: {
            task_question_id: { [Op.in]: taskQueIdsArr },
            id: { [Op.notIn]: videoAnsIds },
          },
        }
      );
    }

    for (let i = 0; i < task_data.length; i++) {
      // check if task_id & touch_point mapping is there or not. if not then we will create mapping first.
      let task_touch_point_id;
      const taskTPMapping = await db.TaskTouchPoints.findOne({
        where: [
          { task_id: task_id },
          { touch_point: task_data[i].touch_point },
        ],
      });

      if (taskTPMapping?.id) {
        task_touch_point_id = await taskTPMapping?.id;
      } else {
        // create task & touch points mapping
        const createTaskTP = await db.TaskTouchPoints.create({
          task_id: task_id,
          touch_point: task_data[i].touch_point,
        });

        if (createTaskTP?.id) {
          task_touch_point_id = await createTaskTP?.id;
        }
      }

      if (task_touch_point_id) {
        const que_ans = await task_data[i].question_answer;
        for (let j = 0; j < que_ans.length; j++) {
          // check if task_touch_point_id & question_id mapping is there or not. if not then we will create mapping first.
          let task_question_id;
          const taskTPQueMapping = await db.TaskQuestions.findOne({
            where: [
              { task_touch_point_id: task_touch_point_id },
              { question_id: que_ans[j].question_id },
            ],
          });

          if (taskTPQueMapping?.id) {
            task_question_id = await taskTPQueMapping?.id;
          } else {
            const createQuestion = await db.TaskQuestions.create({
              task_touch_point_id: task_touch_point_id,
              question_id: que_ans[j].question_id,
            });

            if (createQuestion?.id) {
              task_question_id = await createQuestion?.id;
            }
          }

          if (task_question_id) {
            // create answer for the questions
            const ans = await que_ans[j].answers;
            let ansData = [];
            for (let k = 0; k < ans.length; k++) {
              await ansData.push({
                task_question_id: task_question_id,
                answer: ans[k],
              });
            }

            await db.TaskAnswers.bulkCreate(ansData);

            // keep active images only with requested ids.
            if (que_ans[j]?.image_answers_ids?.length) {
              await db.TaskImageAnswers.update(
                { is_active: 0, is_deleted: 1 },
                {
                  where: [
                    { task_question_id: task_question_id },
                    { id: { [Op.notIn]: que_ans[j].image_answers_ids } },
                  ],
                }
              );
            }

            // mark video upload success true for received video answer id.
            if (que_ans[j]?.video_answer_id) {
              const taskVideoAnswer = await common.getTaskVideoAnswerById(
                que_ans[j]?.video_answer_id
              );

              if (taskVideoAnswer) {
                const fileUrl = await aws.getS3FileUrl(
                  taskVideoAnswer.video_key
                );
                await db.TaskVideoAnswers.update(
                  {
                    is_video_uploaded: 1,
                    video_url: fileUrl,
                  },
                  { where: { id: que_ans[j]?.video_answer_id } }
                );
              }
            }
          }
        }
      }
    }

    // update additional info, is_submitted & status in tasks.
    await db.Tasks.update(
      {
        additional_info: additional_info || null,
        status: CONSTANTS.TASK_STATUS.COMPLETED,
        is_submitted: 1,
      },
      { where: { id: task_id } }
    );

    return response.handler.success(MESSAGE.TASK.SUBMIT.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const submitAnswer = async (request, response) => {
  try {
    const loggedInUser = await request?.login_user?.data;
    const { task_id, additional_info, task_data } = request.body;

    // check if task exist with requested id or not
    const taskExist = await common.isTaskExist(task_id);

    if (!taskExist) {
      return response.handler.badRequest(MESSAGE.TASK.NOT_EXIST);
    }

    // identify how many total touch points are there in the task.
    const taskDetail = await db.Tasks.findOne({
      where: { id: task_id },
      include: [
        {
          model: db.QuestionMapping,
          as: "question_mapping",
          required: true,
          attributes: ATTRIBUTES.QUESTION_MAPPING,
          include: [
            {
              model: db.Scopes,
              as: "scope_data",
              required: true,
              attributes: ATTRIBUTES.SCOPES,
              include: [
                {
                  model: db.ScopeImages,
                  as: "scope_image",
                  required: true,
                  attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                    (value) => value !== "base64"
                  ),
                  include: [
                    {
                      model: db.ScopeImageTouchPoints,
                      as: "touch_points",
                      required: true,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    });

    const task_total_touch_points =
      taskDetail?.question_mapping?.scope_data?.scope_image?.touch_points
        ?.length || 0;

    if (task_data?.length) {
      //get all the question id from payload
      const queIds = [
        ...new Set(
          task_data
            ?.flatMap((item) => item?.question_answer)
            ?.map((item) => item?.question_id)
        ),
      ];

      //get all que data with report_tag_id
      const queDetails = await db.Questions.findAll({
        where: { id: { [Op.in]: queIds } },
        attributes: ["id", "report_tag_id"],
      });

      //create an array of report tag ids
      const reportTagIds = [
        ...new Set(
          queDetails?.map((item) => item?.report_tag_id)?.filter((item) => item)
        ),
      ];

      //check if required number of images is there or not based on report tag id
      if (reportTagIds?.length) {
        //get report tag data
        const tagData = await db.ReportTags.findAll({
          where: { id: { [Op.in]: reportTagIds }, is_active: 1 },
        });

        let imgCountBasedOnReportId = {};
        //create an object with report tag as key and number of images as value
        tagData?.forEach((item) => {
          imgCountBasedOnReportId[item?.id] =
            (imgCountBasedOnReportId[item?.id] || 0) + item?.number;
        });

        const totalImageIdData =
          task_data?.[task_data?.length - 1]?.image_answers_ids;

        //check if required number of images are availbale with tag id or not
        if (totalImageIdData?.length) {
          for (let item of totalImageIdData) {
            if (
              imgCountBasedOnReportId?.[item?.report_tag_id] > item?.id?.length
            )
              return response.handler.badRequest(
                MESSAGE.TASK.SUBMIT.INVALID_IMAGE_COUNT
              );
          }
        }
      }

      // if task answer are re-submitting, then we will delete existing answers for requested touch points
      const requestedTouchPoints = task_data.map((td) => td.touch_point);
      /* Start Delete Existing Answers */
      // find task_question_ids of the task of requested touch points
      const taskQueIds = await db.Tasks.findOne({
        where: { id: task_id },
        include: [
          {
            model: db.TaskTouchPoints,
            as: "task_touch_points", // Ensure this matches the association in your model
            required: true,
            where: { touch_point: { [Op.in]: requestedTouchPoints } },
            attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
            include: [
              {
                model: db.TaskQuestions,
                as: "task_questions", // Ensure this matches the association in your model
                required: true,
                attributes: ATTRIBUTES.TASK_QUESTIONS,
                include: [
                  {
                    model: db.TaskImageAnswers,
                    where: { is_active: 1 },
                    required: false,
                    as: "image_answer_data",
                  },
                  {
                    model: db.TaskVideoAnswers,
                    where: { is_active: 1 },
                    required: false,
                    as: "video_answer_data",
                  },
                ],
              },
            ],
          },
        ],
      });
      if (taskQueIds) {
        const taskTouchPointIds = taskQueIds?.task_touch_points?.map(
          (tp) => tp?.id
        );
        const taskQueIdsArr = taskQueIds.task_touch_points.flatMap((tp) =>
          tp.task_questions.map((tq) => tq.id)
        );

        //update the status and reason for touchpoints to null
        await db.TaskTouchPoints.update(
          { reason: null, status: null },
          {
            where: { id: { [Op.in]: taskTouchPointIds } },
          }
        );

        //update the status and reason for task questions to null
        await db.TaskQuestions.update(
          { reason: null, status: null },
          {
            where: { id: { [Op.in]: taskQueIdsArr } },
          }
        );

        if (taskQueIdsArr.length) {
          // deleted all existing answers.
          // await db.TaskAnswers.update(
          // 	{ is_active: 0, is_deleted: 1 },
          // 	{ where: { task_question_id: { [Op.in]: taskQueIdsArr } } }
          // );
          await db.TaskAnswers.destroy({
            where: { task_question_id: { [Op.in]: taskQueIdsArr } },
          });

          const imageAnsIds = task_data
            ?.flatMap((task) => task?.question_answer)
            ?.flatMap(
              (questionAnswer) => questionAnswer?.image_answers_ids || []
            )
            ?.filter(Boolean);

          const existImageIds = taskQueIds?.task_touch_points
            ?.flatMap((task) => task?.task_questions)
            ?.flatMap((question) => question?.image_answer_data)
            ?.map((item) => item?.id);

          if (imageAnsIds?.length && existImageIds?.length) {
            const imgIdArr = existImageIds?.filter(
              (val) => !imageAnsIds.includes(val)
            );

            if (imgIdArr?.length) {
              await db.TaskImageAnswers.update(
                { is_active: 0, is_deleted: 1 },
                {
                  where: {
                    id: imgIdArr,
                  },
                }
              );
            }
          }
          const videoAnsIds = task_data
            ?.flatMap((task) => task?.question_answer)
            ?.map((questionAnswer) => questionAnswer?.video_answer_id || 0)
            ?.filter(Boolean);

          const existVideoIds = taskQueIds?.task_touch_points
            ?.flatMap((task) => task?.task_questions)
            ?.map((question) => question?.video_answer_data?.id || 0)
            ?.filter(Boolean);

          if (videoAnsIds?.length && existVideoIds?.length) {
            const videoIdArr = existVideoIds?.filter(
              (val) => !videoAnsIds.includes(val)
            );

            if (videoIdArr?.length) {
              await db.TaskVideoAnswers.update(
                { is_active: 0, is_deleted: 1 },
                {
                  where: {
                    id: videoIdArr,
                  },
                }
              );
            }
          }
        }
      }

      //find task_images based on report_tag id
      const taskImgAnsBasedOnReport = await db.TaskImageAnswers.findAll({
        where: {
          task_id,
          is_active: 1,
          is_deleted: 0,
        },
        attributes: ["id"],
      });

      //get image id from payload
      const reportBasedImgIds = task_data?.[
        task_data?.length - 1
      ]?.image_answers_ids
        ?.map((item) => item?.id)
        ?.flat();

      //get id from db
      const existImgIds = taskImgAnsBasedOnReport?.map((item) => item?.id);

      //filter out image id that not exist in payload from existing data
      const deleteImgIds = existImgIds?.filter(
        (id) => !reportBasedImgIds?.includes(id)
      );

      //if image id exist then delete it
      if (deleteImgIds?.length) {
        await db.TaskImageAnswers.update(
          { is_active: 0, is_deleted: 1 },
          {
            where: {
              id: deleteImgIds,
            },
          }
        );
      }
      /* End Delete Existing Answers */

      for (const task of task_data) {
        // check if task_id & touch_point mapping is there or not. if not then we will create mapping first.
        const taskTPMapping = await db.TaskTouchPoints.findOne({
          where: {
            task_id: task_id,
            touch_point: task.touch_point,
          },
        });

        let task_touch_point_id = taskTPMapping?.id;

        if (!task_touch_point_id) {
          // create task & touch points mapping
          const createTaskTP = await db.TaskTouchPoints.create({
            task_id: task_id,
            touch_point: task.touch_point,
          });

          task_touch_point_id = createTaskTP?.id;
        }

        if (task_touch_point_id) {
          const que_ans = task.question_answer;
          for (const qa of que_ans) {
            // IMPORTANT: The where clause already includes isSubquestion and parent_question_id
            // This is correctly identifying each question in its specific context
            const taskTPQueMapping = await db.TaskQuestions.findOne({
              where: {
                task_touch_point_id: task_touch_point_id,
                question_id: qa.question_id,
                isSubquestion: qa.isSubquestion || false,
                parent_question_id: qa.parent_question_id || null,
              },
            });

            let task_question_id = taskTPQueMapping?.id;
            if (!task_question_id) {
              // IMPORTANT: Creation of TaskQuestions correctly includes isSubquestion and parent_question_id
              // This ensures each question is properly tied to its parent if it's a subquestion
              const createQuestion = await db.TaskQuestions.create({
                task_touch_point_id: task_touch_point_id,
                question_id: qa.question_id,
                isSubquestion: qa.isSubquestion || false,
                parent_question_id: qa.parent_question_id || null,
              });

              task_question_id = createQuestion?.id;
            }

            if (task_question_id) {
              // create answer for the questions
              const ans = qa.answers;
              let ansData = [];
              for (let k = 0; k < ans.length; k++) {
                ansData.push({
                  task_question_id: task_question_id,
                  answer: ans[k],
                });
                if (qa?.comment) {
                  await db.TaskQuestions.update(
                    { comment: qa?.comment },
                    { where: { id: task_question_id } }
                  );
                }
              }

              await db.TaskAnswers.bulkCreate(ansData);

              // keep active images only with requested ids.
              if (qa?.image_answers_ids?.length) {
                await db.TaskImageAnswers.update(
                  { is_active: 0, is_deleted: 1 },
                  {
                    where: {
                      task_question_id: task_question_id,
                      id: { [Op.notIn]: qa.image_answers_ids },
                    },
                  }
                );
              }

              // mark video upload success true for received video answer id.
              if (qa?.video_answer_id) {
                const taskVideoAnswer = await common.getTaskVideoAnswerById(
                  qa.video_answer_id
                );

                if (taskVideoAnswer) {
                  const fileUrl = await aws.getS3FileUrl(
                    taskVideoAnswer.video_key
                  );

                  await db.TaskVideoAnswers.update(
                    {
                      is_video_uploaded: 1,
                      video_url: fileUrl,
                    },
                    { where: { id: qa?.video_answer_id } }
                  );
                }
              }
            }
          }
        }
      }
    }
    // update additional info
    if (additional_info) {
      await db.Tasks.update({ additional_info }, { where: { id: task_id } });
    }

    // update is_submitted & status to complete only, when all touchpoints answer are submitted.
    const task_answered_touch_points = await db.TaskTouchPoints.count({
      where: { task_id },
      distinct: true,
      col: "touch_point",
    });

    //get task touchpoint data
    const taskTouchPoint = await db.TaskTouchPoints.findAll({
      where: { task_id },
    });

    const taskTpIds = taskTouchPoint?.map((item) => item?.id);

    const rejectedQue = await db.TaskQuestions.findAll({
      where: {
        task_touch_point_id: { [Op.in]: taskTpIds },
        status: CONSTANTS.TASK_STATUS.REJECTED,
      },
    });
    if (
      task_total_touch_points === task_answered_touch_points &&
      task_data?.length &&
      rejectedQue?.length === 0
    ) {
      const woData = await db.WorkOrders.findOne({
        where: { id: taskDetail?.work_order_id },
      });
      await db.Tasks.update(
        {
          internal_team_id: woData?.internal_team_id,
          status: CONSTANTS.TASK_STATUS.COMPLETED,
          is_submitted: 1,
        },
        { where: { id: task_id } }
      );

      await db.TaskReview.create({
        task_id,
        assigned_to_user_id: loggedInUser?.id,
        status: CONSTANTS.TASK_STATUS.COMPLETED,
      });
    }

    return response.handler.success(MESSAGE.TASK.SUBMIT.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const uploadImageAnswer = async (request, response) => {
  try {
    let { task_id, touch_point, question_id, report_tag_id } = request.body;

    task_id = parseInt(task_id);
    touch_point = parseInt(touch_point);
    question_id = parseInt(question_id);
    report_tag_id = parseInt(report_tag_id);

    // check if task is exist with requested id or not
    const taskExist = await common.getTaskById(task_id);
    if (!taskExist) {
      return response.handler.badRequest(MESSAGE.TASK.NOT_EXIST);
    }

    // check if question is exist with requested id or not
    if (question_id) {
      const queExist = await common.getQuestionById(question_id);
      if (!queExist) {
        return response.handler.badRequest(MESSAGE.QUESTION.NOT_EXIST);
      }
    }

    // check if task_id & touch_point mapping is there or not. if not then we will create mapping first.
    let task_touch_point_id;
    const taskTPMapping = await db.TaskTouchPoints.findOne({
      where: {
        task_id,
        touch_point,
      },
    });

    if (taskTPMapping?.id) {
      task_touch_point_id = await taskTPMapping?.id;
    } else {
      const createTaskTPMapping = await db.TaskTouchPoints.create({
        task_id: task_id,
        touch_point: touch_point,
      });

      if (createTaskTPMapping?.id) {
        task_touch_point_id = await createTaskTPMapping?.id;
      }
    }

    if (task_touch_point_id) {
      let task_question_id;
      if (question_id) {
        // check if task_touch_point_id & question_id mapping is there or not. if not then we will create mapping first.
        const taskTPQueMapping = await db.TaskQuestions.findOne({
          where: {
            task_touch_point_id,
            question_id,
          },
        });

        if (taskTPQueMapping?.id) {
          task_question_id = await taskTPQueMapping?.id;
        } else {
          const createTaskTPQueMapping = await db.TaskQuestions.create({
            task_touch_point_id: task_touch_point_id,
            question_id: question_id,
          });

          if (createTaskTPQueMapping?.id) {
            task_question_id = await createTaskTPQueMapping?.id;
          }
        }
      }

      // if (task_question_id) {
      // upload img to s3
      let Key, Location, metadata, mimetype;
      if (request.file) {
        mimetype = request.file?.mimetype;
        const path = request.file?.path;
        const blob = fs.readFileSync(path); //convert file to blob
        const fileName = request.file?.originalname;
        const uploadProfileImg = await aws.uploadFileS3(
          fileName,
          blob,
          CONSTANTS.S3_BUCKET.FOLDERS.TASK.BASE,
          metadata,
          mimetype
        );

        if (uploadProfileImg) {
          Key = uploadProfileImg.Key;
          Location = uploadProfileImg.Location;
        }
      }

      // const base64 = await aws.convertS3UrlToBase64(Key);

      if (Key && Location) {
        let payload = {
          task_question_id: task_question_id,
          image_key: Key,
          image_url: Location,
          // base64: base64
        };
        if (report_tag_id) {
          payload = { ...payload, report_tag_id, task_id };
        }
        const addImage = await db.TaskImageAnswers.create(payload);

        if (addImage?.id) {
          const uploadedImg = await db.TaskImageAnswers.findOne({
            where: { id: addImage?.id },
            attributes: ["id", "image_key", "image_url"],
            raw: true,
          });

          return response.handler.success(MESSAGE.TASK.UPLOAD_IMAGE.SUCCESS, {
            image: {
              ...uploadedImg,
              task_id: task_id,
              touch_point: touch_point,
              question_id: question_id,
            },
          });
        }
      } else {
        return response.handler.badRequest(MESSAGE.TASK.UPLOAD_IMAGE.ERROR);
      }
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const generateTaskAnswerSignedUrl = async (request, response) => {
  try {
    let {
      task_id,
      touch_point,
      question_id,
      is_video_uploaded = true,
    } = request.body;
    task_id = parseInt(task_id);
    touch_point = parseInt(touch_point);
    question_id = parseInt(question_id);

    // check if task is exist with requested id or not
    const taskExist = await common.getTaskById(task_id);
    if (!taskExist) {
      return response.handler.badRequest(MESSAGE.TASK.NOT_EXIST);
    }

    // check if question is exist with requested id or not
    const queExist = await common.getQuestionById(question_id);
    if (!queExist) {
      return response.handler.badRequest(MESSAGE.QUESTION.NOT_EXIST);
    }

    // check if task_id & touch_point mapping is there or not. if not then we will create mapping first.
    let task_touch_point_id;
    const taskTPMapping = await db.TaskTouchPoints.findOne({
      where: {
        task_id,
        touch_point,
      },
    });

    if (taskTPMapping?.id) {
      task_touch_point_id = await taskTPMapping?.id;
    } else {
      const createTaskTPMapping = await db.TaskTouchPoints.create({
        task_id: task_id,
        touch_point: touch_point,
      });

      if (createTaskTPMapping?.id) {
        task_touch_point_id = await createTaskTPMapping?.id;
      }
    }

    if (task_touch_point_id) {
      // check if task_touch_point_id & question_id mapping is there or not. if not then we will create mapping first.
      let task_question_id;
      const taskTPQueMapping = await db.TaskQuestions.findOne({
        where: {
          task_touch_point_id,
          question_id,
        },
      });

      if (taskTPQueMapping?.id) {
        task_question_id = await taskTPQueMapping?.id;
      } else {
        const createTaskTPQueMapping = await db.TaskQuestions.create({
          task_touch_point_id: task_touch_point_id,
          question_id: question_id,
        });

        if (createTaskTPQueMapping?.id) {
          task_question_id = await createTaskTPQueMapping?.id;
        }
      }

      if (task_question_id) {
        const current_dateTime = await getCurrentDateTimeUTC(
          CONSTANTS.DATE_FORMATS.APPEND_TO_FILE
        );
        const fileName =
          (await generateRandomAlphaNumericString()) + "_" + current_dateTime;
        const s3FolderPath = `${CONSTANTS.S3_BUCKET.FOLDERS.TASK.BASE}/${CONSTANTS.S3_BUCKET.FOLDERS.TASK.VIDEOS}`;
        const fileKey = `${s3FolderPath}/${fileName}`;
        // const fileUrl = await aws.getS3FileUrl(fileKey);

        const signed_url = await aws.getS3PutObjectSignedUrl(
          s3FolderPath,
          fileName
        );

        if (signed_url) {
          const createTaskVideoAnswer = await db.TaskVideoAnswers.create({
            task_question_id,
            signed_url,
            signed_url_exipration_time:
              CONSTANTS.S3_BUCKET.SIGNED_URL.EXIPRATION_TIME,
            video_key: fileKey,
            is_video_uploaded,
            // video_url: fileUrl,
          });

          if (createTaskVideoAnswer?.id) {
            const taskVideoAnswer = await common.getTaskVideoAnswerById(
              createTaskVideoAnswer.id
            );

            if (taskVideoAnswer) {
              return response.handler.success(
                MESSAGE.TASK.SIGNED_URL.GENERATE.SUCCESS,
                { task_video_answer: taskVideoAnswer }
              );
            }
          }
        } else {
          return response.handler.badRequest(
            MESSAGE.TASK.SIGNED_URL.GENERATE.ERROR
          );
        }
      }
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const deleteTaskVideoAnswerSignedUrl = async (request, response) => {
  try {
    const { id } = request.query;
    if (!id) {
      return response.handler.badRequest(MESSAGE.TASK.SIGNED_URL.DELETE.ID);
    }
    const isVideoExist = await db.TaskVideoAnswers.findOne({ where: { id } });
    if (!isVideoExist) {
      return response.handler.badRequest(
        MESSAGE.TASK.SIGNED_URL.DELETE.NOT_EXIST
      );
    }
    await db.TaskVideoAnswers.update(
      {
        is_video_uploaded: 0,
      },
      { where: { id } }
    );
    return response.handler.success(MESSAGE.TASK.SIGNED_URL.DELETE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getTaskReview = async (request, response) => {
  try {
    const { task_id } = request?.query;

    // check if task exist with requested id or not
    const taskExist = await common.isTaskExist(task_id);

    if (!taskExist) {
      return response.handler.badRequest(MESSAGE.TASK.NOT_EXIST);
    }

    const taskReviewData = await db.TaskReview.findAll({ where: { task_id } });
    const taskTouchPointReviewData = await db.TaskTouchPointReview.findAll({
      where: { task_id },
    });
    const taskQuestionReviewData = await db.TaskQuestionReview.findAll({
      where: { task_id },
    });

    const resArr = [];

    taskReviewData?.forEach((item) => {
      resArr.push({ ...JSON.parse(JSON.stringify(item)), type: "task" });
    });
    taskTouchPointReviewData?.forEach((item) => {
      resArr.push({ ...JSON.parse(JSON.stringify(item)), type: "touch_point" });
    });
    taskQuestionReviewData?.forEach((item) => {
      resArr.push({ ...JSON.parse(JSON.stringify(item)), type: "question" });
    });

    const userIds = [
      ...new Set(
        [
          resArr?.map((item) => [
            item?.reviewer_user_id,
            item?.assigned_to_user_id,
          ]),
        ]?.flat(2)
      ),
    ]?.filter((item) => item);

    const userData = await userService.getUserDetailsByUserIds({
      user_ids: userIds,
      headers: request?.headers,
    });

    let reviewDataArr = [];
    for (let i = 0; i < resArr?.length; i++) {
      const assignedByUserData = await userData.find(
        (data) => data.id == resArr?.[i]?.assigned_to_user_id
      );

      const reviewerUserData = await userData.find(
        (data) => data.id == resArr?.[i]?.reviewer_user_id
      );

      reviewDataArr.push({
        ...JSON.parse(JSON.stringify(resArr?.[i])),
        assigned_user_data: assignedByUserData || {},
        reviewer_user_data: reviewerUserData || {},
      });
    }

    reviewDataArr = reviewDataArr.sort(
      (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
    );

    return response.handler.success("", reviewDataArr);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const updateVideoUrl = async (request, response) => {
  try {
    const { id } = request?.body;

    // check if task exist with requested id or not
    const taskVideoExist = await db.TaskVideoAnswers.findOne({ where: { id } });

    if (!taskVideoExist) {
      return response.handler.badRequest(MESSAGE.TASK.SIGNED_URL.NOT_EXIST);
    }

    const fileUrl = await aws.getS3FileUrl(taskVideoExist.video_key);

    const updatedData = await db.TaskVideoAnswers.update(
      {
        is_video_uploaded: 1,
        video_url: fileUrl,
      },
      { where: { id } }
    );

    if (updatedData?.[0]) {
      const result = await db.TaskVideoAnswers.findOne({ where: { id } });
      return response.handler.success(
        MESSAGE.TASK.SIGNED_URL.UPDATE.SUCCESS,
        result
      );
    }
    return response.handler.badRequest(MESSAGE.TASK.SIGNED_URL.UPDATE.ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  get,
  submitAnswer,
  uploadImageAnswer,
  generateTaskAnswerSignedUrl,
  deleteTaskVideoAnswerSignedUrl,
  getTaskReview,
  updateVideoUrl,
};
