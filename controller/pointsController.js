const { Op } = require("sequelize");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");
const userService = require("../helpers/userAuthenticationServiceApis.js");

const defineRatePerPoints = async (request, response) => {
    try {
        const { points, rate } = request.body;
        const loggedInUser = await request?.login_user?.data;

        const addRate = await db.PointRates.create({
            points,
            rate,
            currency: CONSTANTS.CURRENCY.INR,
            added_by: loggedInUser.id
        });

        if (addRate) {
            // mark all previous rate as inactive
            await db.PointRates.update(
                { is_active: 0 },
                { where: { id: { [Op.ne]: addRate.id } } }
            );

            return response.handler.success(MESSAGE.POINTS.RATE.DEFINE.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.POINTS.RATE.DEFINE.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const getRateOfPoints = async (request, response) => {
    try {
        const { status } = request.query;

        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        let whereCond = [];
        if (status !== undefined) {
            whereCond.push({ is_active: status });
        }

        const { count: totalCount, rows: rateOfPoints } = await db.PointRates.findAndCountAll({
            limit: limit,
            offset: start,
            where: whereCond,
            order: [["id", "DESC"]],
            attributes: ATTRIBUTES.POINT_RATES
        });

        if (!rateOfPoints) {
            return response.handler.badRequest(MESSAGE.POINTS.RATE.FOUND.SUCCESS);
        }
        else if (totalCount === 0 || rateOfPoints.length === 0) {
            return response.handler.notFound(MESSAGE.POINTS.RATE.FOUND.FAILED);
        }
        else {
            let added_by_user_ids = await rateOfPoints.map((data) => data?.added_by);

            // get user from user authentication service.
            let userData = [];
            if (added_by_user_ids.length) {
                const userAPI = await userService.getUserDetailsByUserIds({
                    user_ids: added_by_user_ids,
                    headers: request?.headers
                });

                if (userAPI) {
                    userData = userAPI
                }
            }

            let respData = [];
            for (let i = 0; i < rateOfPoints.length; i++) {
                let added_by_data = {}

                if (userData.length) {
                    added_by_data = await userData.find((ele) => ele.id === rateOfPoints[i]?.added_by);
                }

                const { id, first_name, last_name, email, country_code, mobile } = added_by_data || {};

                respData.push({
                    ...JSON.parse(JSON.stringify(rateOfPoints[i])),
                    added_by_data: {
                        id, first_name, last_name, email, country_code, mobile
                    }
                });
            }

            return response.handler.success(
                MESSAGE.POINTS.RATE.FOUND.SUCCESS,
                {
                    totalCount: totalCount,
                    count: rateOfPoints.length,
                    active_rate: await common.getPointRatesByCondition([{ is_active: 1 }]),
                    rate_history: respData
                }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const definePointsPerWoType = async (request, response) => {
    try {
        const { work_order_type_id, points_to_assign, transaction_type } = request.body;

        const loggedInUser = await request?.login_user?.data;

        // check if requested work_order_type is exist or not.
        const woTypeExist = await common.getWorkOrderTypeById(work_order_type_id);
        if (!woTypeExist) {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
        }

        const addPointsPerWO = await db.WorkOrderTypePoints.create({
            work_order_type_id,
            points_to_assign,
            transaction_type: transaction_type || CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT,
            added_by: loggedInUser.id
        });

        if (addPointsPerWO) {
            // mark all previous rate as inactive
            await db.WorkOrderTypePoints.update(
                { is_active: 0 },
                {
                    where: {
                        id: { [Op.ne]: addPointsPerWO.id },
                        work_order_type_id,
                        is_active: 1
                    }
                }
            );

            return response.handler.success(MESSAGE.POINTS.DEFINE_WITH_WO_TYPE.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.POINTS.DEFINE_WITH_WO_TYPE.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const getPoints = async (request, response) => {
    try {
        const { id, search } = request.query;
        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        let whereCond = [];
        id && whereCond.push({ id });

        const { count: totalCount, rows: pointsData } = await db.WorkOrderTypes.findAndCountAll({
            limit: limit,
            offset: start,
            // where: whereCond,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
            distinct: true,
            order: [["id", "DESC"]],
            include: [
                {
                    model: db.WorkOrderTypePoints,
                    as: "points_data",
                    required: false,
                    where: { is_active: 1 },
                    attributes: ATTRIBUTES.WORK_ORDER_TYPE_POINTS
                }
            ]
        });

        if (!pointsData) {
            return response.handler.badRequest(MESSAGE.POINTS.FOUND.ERROR);
        }
        else if (totalCount === 0 || pointsData.length === 0) {
            return response.handler.notFound(MESSAGE.POINTS.FOUND.FAILED);
        }
        else {
            let added_by_user_ids = await pointsData.map((data) => data.points_data?.[0]?.added_by);

            // get user from user authentication service.
            let userData = [];
            if (added_by_user_ids.length) {
                const userAPI = await userService.getUserDetailsByUserIds({
                    user_ids: added_by_user_ids,
                    headers: request?.headers
                });

                if (userAPI) {
                    userData = userAPI
                }
            }

            let respData = []
            for (let i = 0; i < pointsData.length; i++) {
                let added_by_data = {}

                if (userData.length) {
                    added_by_data = await userData.find((ele) => ele.id === pointsData[i].points_data[0]?.added_by);
                }

                const { id, first_name, last_name, email, country_code, mobile } = added_by_data || {};

                respData.push({
                    work_order_type_data: {
                        id: pointsData[i].id,
                        title: pointsData[i].title,
                    },
                    points_data: pointsData[i].points_data[0] || {},
                    added_by_data: {
                        id, first_name, last_name, email, country_code, mobile
                    }
                });
            }

            return response.handler.success(
                MESSAGE.POINTS.FOUND.SUCCESS,
                { totalCount: totalCount, count: pointsData.length, points: respData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const getPointsByWorkOrderType = async (request, response) => {
    try {
        const { wo_type_id } = request.query;
        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        // check if work order type is exist with requested id or not.
        const woTypeExist = await common.getWorkOrderTypeById(wo_type_id);
        if (!woTypeExist) {
            return response.handler.badRequest(MESSAGE.WORK_ORDER_TYPE.NOT_EXIST);
        }

        const { count: totalCount, rows: pointsData } = await db.WorkOrderTypePoints.findAndCountAll({
            limit: limit,
            offset: start,
            where: { work_order_type_id: wo_type_id },
            attributes: ATTRIBUTES.WORK_ORDER_TYPE_POINTS,
            distinct: true,
            order: [["id", "DESC"]],
            include: [
                {
                    model: db.WorkOrderTypes,
                    as: "work_order_type_data",
                    required: true,
                    attributes: ATTRIBUTES.WORK_ORDER_TYPES
                }
            ]
        });

        if (!pointsData) {
            return response.handler.badRequest(MESSAGE.POINTS.FOUND.ERROR);
        }
        else if (totalCount === 0 || pointsData.length === 0) {
            return response.handler.notFound(MESSAGE.POINTS.FOUND.FAILED);
        }
        else {
            let added_by_user_ids = await pointsData.map((data) => data?.added_by);

            // get user from user authentication service.
            let userData = [];
            if (added_by_user_ids.length) {
                const userAPI = await userService.getUserDetailsByUserIds({
                    user_ids: added_by_user_ids,
                    headers: request?.headers
                });

                if (userAPI) {
                    userData = userAPI
                }
            }

            let respData = [];
            for (let i = 0; i < pointsData.length; i++) {
                let added_by_data = {}

                if (userData.length) {
                    added_by_data = await userData.find((ele) => ele.id === pointsData[i].added_by);
                }

                const { id, first_name, last_name, email, country_code, mobile } = added_by_data || {};

                respData.push({
                    ...JSON.parse(JSON.stringify(pointsData[i])),
                    added_by_data: {
                        id, first_name, last_name, email, country_code, mobile
                    }
                });
            }

            return response.handler.success(
                MESSAGE.POINTS.FOUND.SUCCESS,
                { totalCount: totalCount, count: pointsData.length, points: respData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const getPointsHistoryOfUser = async (request, response) => {
    try {
        const { user_id } = request.query;
        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0;

        let whereCond = [
            { user_id }
        ];

        const { count: totalCount, rows: pointsHistoryData } = await db.PointHistory.findAndCountAll({
            limit: limit,
            offset: start,
            where: whereCond,
            attributes: ATTRIBUTES.POINT_HISTORY,
            order: [["id", "DESC"]],
            include: [
                {
                    model: db.WorkOrders,
                    as: "work_order_data",
                    required: false,
                    where: {
                        is_active: 1, is_deleted: 0
                    },
                    attributes: ["id", "actual_work_order_id", "order_id", "work_order_status_id", "work_order_type_id"],
                    include: [
                        {
                            model: db.WorkOrderTypes,
                            as: "work_order_type",
                            required: true,
                            attributes: ATTRIBUTES.WORK_ORDER_TYPES
                        }
                    ]
                },
                {
                    model: db.WorkOrderTypePoints,
                    as: "wo_points_data",
                    required: false,
                    attributes: ATTRIBUTES.WORK_ORDER_TYPE_POINTS
                },
                {
                    model: db.PointRates,
                    as: "rates_data",
                    required: false,
                    attributes: ATTRIBUTES.POINT_RATES
                },

            ]
        });

        if (!pointsHistoryData) {
            return response.handler.badRequest(MESSAGE.POINTS.HISTORY.FOUND.ERROR);
        }
        else if (totalCount === 0 || pointsHistoryData.length === 0) {
            return response.handler.notFound(MESSAGE.POINTS.HISTORY.FOUND.FAILED);
        }
        else {
            const userBalance = await db.PointBalance.findOne({
                where: { user_id },
                raw: true,
            });

            return response.handler.success(
                MESSAGE.POINTS.HISTORY.FOUND.SUCCESS,
                { totalCount: totalCount, count: pointsHistoryData.length, total_balance: userBalance, points_history: pointsHistoryData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const getBalanceOfUser = async (request, response) => {
    try {
        const { user_id } = request.query;

        const userBalance = await db.PointBalance.findOne({
            where: { user_id },
            raw: true,
        });

        if (userBalance) {
            return response.handler.success(
                MESSAGE.POINTS.BALANCE.FOUND.SUCCESS,
                { total_balance: userBalance }
            );
        }
        else {
            return response.handler.badRequest(MESSAGE.POINTS.BALANCE.FOUND.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const redeemEarning = async (request, response) => {
    try {
        const { user_id, amount, note } = request.body;

        // check if requested user has balance available for redeem or not
        const checkBalance = await common.getPointBalanceByUserId(user_id);

        if (!checkBalance) {
            return response.handler.badRequest(MESSAGE.POINTS.EARNINGS.REDEEM.EMPTY);
        }

        // check how much balance user has left.
        const remainedBalance = checkBalance.total_earnings - checkBalance.redeemed_earnings

        if (remainedBalance <= 0 || remainedBalance < amount) {
            return response.handler.badRequest(MESSAGE.POINTS.EARNINGS.REDEEM.INSUFFICIENT);
        }

        const currentPointRate = await common.getPointRatesByCondition([{ is_active: 1 }]);

        // add history of redeemed balance
        const redeemHistory = await db.RedeemedEarningHistory.create({
            user_id,
            redeem_amount: amount,
            note,
            point_rate_id: currentPointRate?.id || null
        });

        const redeemBalance = await db.PointBalance.update(
            {
                redeemed_points: checkBalance.redeemed_points + (amount * (currentPointRate.points / currentPointRate.rate)),
                redeemed_earnings: (parseFloat(checkBalance.redeemed_earnings) + amount)
            },
            { where: { user_id } }
        );

        if (redeemBalance) {
            await db.RedeemedEarningHistory.update(
                { is_read: 1 },
                { where: { id: redeemHistory.id } }
            );

            return response.handler.success(MESSAGE.POINTS.EARNINGS.REDEEM.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.POINTS.EARNINGS.REDEEM.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

const redeemedEarningHistory = async (request, response) => {
    try {
        const { user_id } = request.query;

        const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
        const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
        const start = page > 1 ? ((page - 1) * limit) : 0

        const { count: totalCount, rows: redeemedEarningHistoryData } = await db.RedeemedEarningHistory.findAndCountAll({
            limit: limit,
            offset: start,
            where: { user_id },
            attributes: ATTRIBUTES.REDEEMED_EARNING_HISTORY,
            order: [["id", "DESC"]],
        });

        if (!redeemedEarningHistoryData) {
            return response.handler.badRequest(MESSAGE.POINTS.EARNINGS.REDEEM.HISTORY.FOUND.ERROR);
        }
        else if (totalCount === 0 || redeemedEarningHistoryData.length === 0) {
            return response.handler.notFound(MESSAGE.POINTS.EARNINGS.REDEEM.HISTORY.FOUND.FAILED);
        }
        else {
            return response.handler.success(
                MESSAGE.POINTS.EARNINGS.REDEEM.HISTORY.FOUND.SUCCESS,
                { totalCount: totalCount, count: redeemedEarningHistoryData.length, redeemed_earnings_history: redeemedEarningHistoryData }
            );
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    defineRatePerPoints,
    getRateOfPoints,
    definePointsPerWoType,
    getPoints,
    getPointsByWorkOrderType,
    getPointsHistoryOfUser,
    getBalanceOfUser,
    redeemEarning,
    redeemedEarningHistory,
}
