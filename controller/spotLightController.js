const proScore = require("../helpers/bigSpring.js");

const getBSProScore = async (request, response) => {
    try {
        const loggedInUser = await request?.login_user?.data;
        const bsProfile = await proScore.getProfile(loggedInUser.email);

        if (bsProfile?.error) {
            return response.handler.badRequest(bsProfile.error);
        }

        if (!bsProfile?.data) {
            return response.handler.badRequest('No data found.');
        }

        const profileId = bsProfile.data.id;
        const bsProScore = await proScore.getProScore(profileId);

        if (bsProScore?.error) {
            return response.handler.badRequest(bsProScore.error);
        }

        return response.handler.success("Data fetched successfully.", bsProScore.data);
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    getBSProScore,
}
