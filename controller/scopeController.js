const { Op } = require("sequelize");
const { isEmpty } = require("lodash");
const fs = require("fs");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const aws = require("../helpers/awsS3.js");
const common = require("./commonFunctions.js");

const create = async (request, response) => {
  try {
    const { model, name, touch_points, scope_id } = request.body;

    let scopeId;
    if (scope_id) {
      // check of scope os exist with requested id or not
      const checkScope = await common.getScopeById(scope_id);
      if (checkScope) {
        if (checkScope.source === CONSTANTS.DATA_SOURCE.ZOHO) {
          scopeId = checkScope.id;
        } else {
          return response.handler.badRequest(MESSAGE.GENERAL_ERROR);
        }
      } else {
        return response.handler.badRequest(MESSAGE.SCOPE.NOT_EXIST);
      }
    } else {
      // check if scope with same model & name exist or not.
      const scopeExist = await common.getScopeByConditions([
        { model },
        { name },
        { is_active: 1 },
        { is_deleted: 0 },
      ]);

      if (scopeExist) {
        return response.handler.conflict(MESSAGE.SCOPE.EXIST);
      }

      const addScope = await db.Scopes.create({
        model: model,
        name: name,
        source: CONSTANTS.DATA_SOURCE.CMS,
      });

      if (addScope) {
        scopeId = addScope?.id;
      }
    }

    if (scopeId) {
      // upload img to s3
      let Key, Location, metadata, mimetype;
      if (request.file) {
        mimetype = request.file?.mimetype;
        const path = request.file?.path;
        const blob = fs.readFileSync(path); //convert file to blob
        const fileName = request.file?.originalname;
        const uploadProfileImg = await aws.uploadFileS3(
          fileName,
          blob,
          CONSTANTS.S3_BUCKET.FOLDERS.SCOPE,
          metadata,
          mimetype
        );

        if (uploadProfileImg) {
          Key = uploadProfileImg.Key;
          Location = uploadProfileImg.Location;
        }
      }
      // const base64 = await aws.convertS3UrlToBase64(Key);

      // add image in scope_images
      if (Key && Location) {
        const addImage = await db.ScopeImages.create({
          scope_id: scopeId,
          image_key: Key,
          image_url: Location,
          // base64: base64
        });

        // add touch points
        if (addImage) {
          for (let i = 0; i < touch_points.length; i++) {
            await db.ScopeImageTouchPoints.create({
              scope_image_id: addImage?.id,
              touch_point: touch_points[i]?.touch_point,
              x_coordinate: touch_points[i]?.x_coordinate,
              y_coordinate: touch_points[i]?.y_coordinate,
              title: touch_points[i]?.title,
            });
          }
        }
      }

      if (scope_id) {
        await db.Scopes.update(
          { source: CONSTANTS.DATA_SOURCE.CMS },
          { where: { id: scope_id } }
        );
      }

      return response.handler.success(MESSAGE.SCOPE.ADD.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.SCOPE.ADD.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

/*
const update = async (request, response) => {
    try {
        const { id, title } = request.body;

        // check if scope is exist with requested id or not.
        const scopeExist = await common.getScopeById(id);

        if (!scopeExist) {
            return response.handler.badRequest(MESSAGE.SCOPE.NOT_EXIST);
        }

        // check if requested title is unique or not.
        const checkScopeTitle = await common.getScopeByConditions([{ id: { [Op.ne]: id } }, { title: title }]);

        if (checkScopeTitle) {
            return response.handler.conflict(MESSAGE.SCOPE.EXIST);
        }

        const updateScope = await db.Scopes.update(
            { title: title },
            { where: { id: id } }
        );

        if (updateScope) {
            return response.handler.success(MESSAGE.SCOPE.UPDATE.SUCCESS);
        }
        else {
            return response.handler.badRequest(MESSAGE.SCOPE.UPDATE.ERROR);
        }
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}
*/

const get = async (request, response) => {
  try {
    const { id, search, status, source, order_id } = request.query;
    // const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    // const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const page = request?.query?.page && parseInt(request.query.page);
    const limit = request?.query?.limit && parseInt(request.query.limit);
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [{ is_part: { [Op.or]: { [Op.in]: [0], [Op.is]: null } } }];
    id && whereCond.push({ id });
    source && whereCond.push({ source });
    if (status !== undefined) {
      whereCond.push({ is_active: status });
    }

    if (search) {
      const searchArr = [
        { model: { [Op.like]: `%${search}%` } },
        { name: { [Op.like]: `%${search}%` } },
      ];

      whereCond.push({ [Op.or]: searchArr });
    }

    if (order_id) {
      const workOrderData = await db.WorkOrders.findAll({
        where: { order_id, is_active: 1 },
        include: [
          {
            model: db.WorkOrderScopes,
            as: "work_order_scopes",
            attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
          },
        ],
      });

      let scopeIds = workOrderData?.flatMap((item) =>
        item?.work_order_scopes?.flatMap((space) => space?.scope_id)
      );

      scopeIds = [...new Set(scopeIds)];
      whereCond.push({ id: { [Op.notIn]: scopeIds } });
    }

    const { count: totalCount, rows: scopeData } =
      await db.Scopes.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: [
          ...ATTRIBUTES.SCOPES,
          [
            db.sequelize.fn(
              "CONCAT",
              db.sequelize.col("height"),
              " ⨯ ",
              db.sequelize.col("width")
            ),
            "dimension",
          ],
        ],
        distinct: true,
        order: [["id", "DESC"]],
        include: [
          {
            model: db.ScopeImages,
            as: "scope_image",
            required: false,
            attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
              (value) => value !== "base64"
            ),
            include: [
              {
                model: db.ScopeImageTouchPoints,
                as: "touch_points",
                required: false,
                attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
              },
            ],
          },
        ],
      });

    if (!scopeData) {
      return response.handler.badRequest(MESSAGE.SCOPE.FOUND.ERROR);
    } else if (totalCount === 0 || scopeData.length === 0) {
      return response.handler.notFound(MESSAGE.SCOPE.FOUND.FAILED);
    } else {
      return response.handler.success(MESSAGE.SCOPE.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: scopeData.length,
        scopes: scopeData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const changeStatus = async (request, response) => {
  try {
    const { id, status } = request.body;

    // check if scope is exist with requested id or not.
    const scopeExist = await common.getScopeById(id);

    if (!scopeExist) {
      return response.handler.badRequest(MESSAGE.SCOPE.NOT_EXIST);
    }

    // if current status of scope is active and user request to inactive, then we will check if that scope is mapped with any work order or que set or not. if mapped then we will restrict to change the status.
    if (!status) {
      const scopeWo = await db.WorkOrderScopes.count({
        where: { scope_id: id },
      });

      const scopeQueSet = await db.QuestionMapping.count({
        where: { scope_id: id },
      });

      if (scopeWo || scopeQueSet) {
        return response.handler.badRequest(
          MESSAGE.SCOPE.STATUS_CHANGE.FAILED_DUE_TO_LINKED_TO_WO_OR_QUESET
        );
      }
    }

    if (status) {
      // check if same model & name with scope already exist and active or not. If available and active, then we will restrict to change status.
      const check = await common.getScopeByConditions([
        { model: scopeExist.model },
        { name: scopeExist.name },
        { is_active: 1 },
        { is_deleted: 0 },
      ]);

      if (!isEmpty(check)) {
        return response.handler.badRequest(
          MESSAGE.SCOPE.STATUS_CHANGE.FAILED_DUE_TO_SAME_EXIST
        );
      }
    }

    const changeScopeStatus = await db.Scopes.update(
      { is_active: status },
      { where: { id } }
    );

    if (changeScopeStatus) {
      return response.handler.success(MESSAGE.SCOPE.STATUS_CHANGE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.SCOPE.STATUS_CHANGE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  create,
  // update,
  get,
  changeStatus,
};
