const { Op } = require("sequelize");
const { isEmpty } = require("lodash");
const generateOtp = require("otp-generator").generate;
const fs = require("fs");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const {
  getTimeDiff,
  replaceData,
  getPadString,
  decrypt,
  encrypt,
} = require("../helpers/functions");
const { MESSAGE } = require("../helpers/messages");
const { sendSMS } = require("../helpers/twilio");
const { sendNotification } = require("../helpers/notification");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const common = require("./commonFunctions.js");
const aws = require("../helpers/awsS3.js");
const { sendMail } = require("../helpers/mail");
const zoho = require("../helpers/zohoApis.js");
const { default: axios } = require("axios");
const { sendWhatsappMsg } = require("../helpers/whatsappMessage.js");

const checkHandoverWorkOrderCreationFeasibility = async (orderData) => {
  const workOrderIds = orderData?.work_order_data?.map((item) => item?.id);

  const orderScopes = await db.OrderScopes.findAll({
    where: { order_id: orderData?.id },
    attributes: ["id", "scope_id"],
  });

  const workOrderScopes = await db.WorkOrderScopes.findAll({
    where: { work_order_id: workOrderIds },
    attributes: ["id", "scope_id"],
  });
  const orderScopesFinal = [
    ...new Set(orderScopes?.map((item) => item?.scope_id)),
  ];
  const workOrderScopesFinal = [
    ...new Set(workOrderScopes?.map((item) => item?.scope_id)),
  ];

  const data = orderScopesFinal.every((item) =>
    workOrderScopesFinal.includes(item)
  );

  return data;
};

const create = async (request, response) => {
  try {
    const { order_id } = request.body;
    if (!order_id || isNaN(order_id)) {
      return response.handler.badRequest(
        "Invalid order_id provided" + order_id
      );
    }

    const loggedInUser = await request?.login_user?.data;

    //check if hanover work order for this order exist or not.
    const handoverWorkOrderExist = await db.HandoverWorkOrders.findOne({
      where: { order_id: order_id },
    });

    if (!isEmpty(handoverWorkOrderExist)) {
      return response.handler.badRequest(
        MESSAGE.HANDOVER_WORK_ORDER.ALREADY_EXIST
      );
    }

    // check if requested order is exist or not.
    const orderExist = await db.Orders.findOne({ where: { id: order_id } });

    if (isEmpty(orderExist)) {
      return response.handler.notFound(MESSAGE.ORDERS.NOT_EXIST);
    }

    if (!orderExist?.zoho_order_id) {
      return response.handler.notFound(MESSAGE.ORDERS.ZOHO_ID_NOT_EXIST);
    }

    //check that zoho stage can not be WARRANTY
    const token = await zoho.generateAuthenticationToken();

    const fields = "id,Stage";
    const url = `${process.env.ZOHO_API_URL}v5/Deals/${orderExist?.zoho_order_id}?fields=${fields}`;

    const config = {
      method: "GET",
      url: url,
      headers: {
        Authorization: `Zoho-oauthtoken ${token?.access_token}`,
      },
    };

    const zohoOrderData = await axios(config);

    if (
      zohoOrderData?.data?.data?.[0]?.Stage ===
      CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE
    ) {
      return response.handler.badRequest(
        MESSAGE.HANDOVER_WORK_ORDER.INVALID_ZOHO_STAGE
      );
    }
    const woStatus = await db.WorkOrderStatus.findAll({
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
    });

    //check all the work order status if it is completed or service enginner approved then we can create handover
    const [completedWoStatus, serviceEngineerApprovedWoStatus] =
      await Promise.all([
        woStatus.find(
          (wot) => wot.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED
        ),
        woStatus.find(
          (wot) =>
            wot.title === CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED
        ),
      ]);

    const woType = await db.WorkOrderTypes.findOne({
      where: { title: CONSTANTS.WORK_ORDER.TYPE.QUALITY_CHECK },
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
    });

    const orderData = await db.Orders.findOne({
      where: { id: order_id },
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.WorkOrders,
          where: {
            work_order_status_id: [
              completedWoStatus?.id,
              serviceEngineerApprovedWoStatus?.id,
            ],
            work_order_type_id: woType?.id,
          },
          as: "work_order_data",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDERS,
          order: [["updatedAt", "DESC"]],
        },
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
      ],
    });

    //check all the scope include in order for that scope work order must be completed or service engineer approved.
    const checkAllScopeCompleted =
      await checkHandoverWorkOrderCreationFeasibility(orderData);

    if (!checkAllScopeCompleted) {
      return response.handler.badRequest(
        MESSAGE.HANDOVER_WORK_ORDER.SCOPE_NOT_COMPLETED
      );
    }

    const workOrderData = orderData?.work_order_data?.[0];

    const newScheduleDate = new Date();
    newScheduleDate.setDate(newScheduleDate.getDate() + 1);

    const nextDateStr = newScheduleDate.toISOString().split("T")[0];

    const pendingWoStatus = woStatus.find(
      (wot) => wot.title === CONSTANTS.WORK_ORDER.STATUS.PENDING
    );

    const handOverWorkOrderData = {
      order_id,
      customer_id: orderData?.customer_id,
      assigned_by_user_id: loggedInUser?.id,
      assigned_by_user_role_id: loggedInUser?.role.id,
      assigned_to_user_id: workOrderData?.assigned_to_user_id,
      assigned_to_user_role_id: workOrderData?.assigned_to_user_role_id,
      scheduled_start_date: nextDateStr,
      scheduled_start_time: workOrderData?.scheduled_start_time,
      work_order_status: pendingWoStatus?.id,
    };

    const addHandoverWorkorderData = await db.HandoverWorkOrders.create(
      handOverWorkOrderData
    );

    if (addHandoverWorkorderData) {
      const actual_work_order_id = (await getPadString(order_id, "ORD")) + "H";
      await db.HandoverWorkOrders.update(
        { actual_work_order_id: actual_work_order_id },
        { where: { id: addHandoverWorkorderData?.id } }
      );

      const zohoWoData = {
        zoho_order_id: orderData?.zoho_order_id,
        id: actual_work_order_id,
        email: orderData?.customer_data?.email,
      };

      //add handover work order to zoho
      await zoho.sendHandOverWorkOrderToZoho(zohoWoData);

      //update zoho stage to handover
      await zoho.checkAndUpdateZohoStage({
        zoho_order_id: orderData?.zoho_order_id,
      });

      //update qc_mode in zoho
      await zoho.updateQcModeInZoho({
        zoho_order_id: orderData?.zoho_order_id,
        mode: CONSTANTS.ZOHO.QC_MODE.APP,
      });

      //send notification to regarding handover work order assigned.
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: [loggedInUser?.id, workOrderData?.assigned_to_user_id],
        headers: request?.headers,
      });

      let assignedByUserData, assignedToUserData;
      if (userAPI) {
        assignedByUserData = await userAPI.find(
          (data) => data.id == loggedInUser?.id
        );
        assignedToUserData = await userAPI.find(
          (data) => data.id == workOrderData?.assigned_to_user_id
        );

        const assignedByUserName = `${assignedByUserData?.first_name} ${assignedByUserData?.last_name}`;

        // get notification template from user service.
        const templateAPI = await userService.getTemplate({
          headers: request?.headers,
          action: [
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
              .HANDOVER_WORK_ORDER_ASSIGNED,
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        if (templateAPI) {
          const { title, body } = templateAPI[0];
          let replaceBody = await replaceData(
            body,
            "{ASSIGNED_BY_USER}",
            assignedByUserName
          );
          replaceBody = await replaceData(
            replaceBody,
            "{WORK_ORDER_ID}",
            actual_work_order_id
          );

          // add notification alert history
          const addAlertAPI = await userService.addAlerts({
            headers: request?.headers,
            sender_user_id: loggedInUser.id,
            title,
            body: replaceBody,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
                .HANDOVER_WORK_ORDER_ASSIGNED,
            receiver_and_send_to: [
              {
                receiver_user_id: workOrderData?.assigned_to_user_id,
                send_to: assignedToUserData?.device_token,
                loginData: assignedToUserData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (assignedToUserData?.loginHistory) {
            notificationResponse = await sendNotification(
              [assignedToUserData?.device_token],
              title,
              replaceBody
            );
            // update alert based on notification response
            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        }
      }

      const customerEmail = orderData?.customer_data?.email;
      const customerMobile = orderData?.customer_data?.mobile;
      const encryptId = encrypt(addHandoverWorkorderData?.id?.toString());
      const url = await replaceData(
        CONSTANTS.HANDOVER_EMAIL_LINK,
        "{WORKORDER_ID}",
        encryptId
      );

      const link = await common.shortenWithTinyURL(url);

      const emailContent = `<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Hi ${orderData?.customer_data?.name},</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Hope you're doing great! 👋</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re happy to share that your <b>Eternia windows have successfully passed our Quality Check (QC).</b>✅</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re now just <b>one step away</b> from handing over your order <b>${orderData?.actual_order_id}</b> and sharing your <b>official warranty card.</b></p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Please click the link below to begin the handover process:</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">${link}</p><br/>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Need any help? Call us at 📞 <b>97699 40000</b> — we're here for you.</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;"><b>– Team Eternia</b></p>`;

      const smsContent = `Hi ${orderData?.customer_data?.name},\n\nHope you're doing great! 👋\nWe’re happy to share that your Eternia windows have successfully passed our Quality Check (QC).✅\nWe’re now just one step away from handing over your order ${orderData?.actual_order_id} and sharing your official warranty card.\nPlease click the link below to begin the handover process:\n${link}\n\nNeed any help? Call us at 📞 97699 40000 — we're here for you.\n– Team Eternia`;
      const whatsappTemplate = [
        orderData?.customer_data?.name,
        orderData?.actual_order_id,
        link,
      ];
      await sendSMS(`+91${customerMobile}`, smsContent);
      await sendMail(customerEmail, "Handover Sheet Link", emailContent);
      await sendWhatsappMsg({
        campaignName:
          CONSTANTS.WHATSAPP.TEMPLATE_NAME.HANDOVER_WORK_ORDER_INVITATION,
        destination: customerMobile,
        templateParams: whatsappTemplate,
      });

      return response.handler.success(MESSAGE.HANDOVER_WORK_ORDER.ADD.SUCCESS);
    }
    return response.handler.badRequest(MESSAGE.WORK_ORDER.ADD.ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const get = async (request, response) => {
  try {
    const {
      order_id,
      work_order_id,
      assigned_to_user_id,
      search,
      order_by = "id",
      sort = "DESC",
    } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];

    order_id && whereCond.push({ order_id });
    work_order_id && whereCond.push({ id: work_order_id });
    assigned_to_user_id && whereCond.push({ assigned_to_user_id });

    if (search) {
      const searchArr = [
        {
          "$HandoverWorkOrders.actual_work_order_id$": {
            [Op.like]: `%${search}%`,
          },
        },
      ];

      whereCond.push({ [Op.or]: searchArr });
    }

    let { count: totalCount, rows: workOrderData } =
      await db.HandoverWorkOrders.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.HANDOVER_WORK_ORDERS,
        distinct: true,
        order: [[order_by, sort]],
        include: [
          {
            model: db.Orders,
            as: "order_data",
            required: false,
            attributes: ATTRIBUTES.ORDERS,
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: ATTRIBUTES.CUSTOMERS,
              },
            ],
          },
          {
            model: db.WorkOrderStatus,
            as: "handover_work_order_status",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          },
        ],
      });

    if (!workOrderData) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.FOUND.ERROR);
    } else if (totalCount === 0 || workOrderData.length === 0) {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    } else {
      const assigned_to_user_ids = await workOrderData.map(
        (wod1) => wod1.assigned_to_user_id
      );
      const assigned_by_user_ids = await workOrderData.map(
        (wod2) => wod2.assigned_by_user_id
      );
      const check_in_user_ids = await workOrderData.map(
        (wod2) => wod2.check_in_user_id
      );

      const user_ids = [
        ...assigned_to_user_ids,
        ...assigned_by_user_ids,
        ...check_in_user_ids,
      ];

      // get user from user authentication service.
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids,
        headers: request?.headers,
      });

      const userData = userAPI || [];

      let respData = [];
      for (let i = 0; i < workOrderData.length; i++) {
        const assigned_to_user =
          (await userData.find(
            (ele) => ele.id === workOrderData[i].assigned_to_user_id
          )) || {};
        const assigned_by_user =
          (await userData.find(
            (ele2) => ele2.id === workOrderData[i].assigned_by_user_id
          )) || {};

        const check_in_user =
          (await userData.find(
            (ele2) => ele2.id === workOrderData[i].check_in_user_id
          )) || {};

        let responseData = {
          ...JSON.parse(JSON.stringify(workOrderData[i])),
          assigned_to_user,
          assigned_by_user,
          check_in_user,
        };

        respData.push(responseData);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: workOrderData.length,
        work_orders: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getById = async (request, response) => {
  try {
    let { id: woId, is_encrypted } = request.query;
    if (is_encrypted == "true") {
      woId = decrypt(woId);
    }
    const handoverWorkOrder = await db.HandoverWorkOrders.findOne({
      where: { id: woId, is_active: 1, is_deleted: 0 },
      attributes: ATTRIBUTES.HANDOVER_WORK_ORDERS,
      include: [
        {
          model: db.Orders,
          as: "order_data",
          required: false,
          attributes: ATTRIBUTES.ORDERS,
          include: [
            {
              model: db.Customers,
              as: "customer_data",
              required: true,
              attributes: ATTRIBUTES.CUSTOMERS,
            },
          ],
        },
        {
          model: db.WorkOrderStatus,
          as: "handover_work_order_status",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
        },
      ],
    });

    if (!handoverWorkOrder) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    const questionData = await db.HandoverQuestions.findAll({
      where: { is_active: 1, is_deleted: 0 },
      attributes: ATTRIBUTES.HANDOVER_QUESTIONS,
      distinct: true,
      order: [["order", "ASC"]],
      include: [
        {
          model: db.HandoverQuestionOptions,
          attributes: ATTRIBUTES.HANDOVER_QUESTION_OPTIONS,
          as: "handover_question_options",
          required: false,
          where: {
            is_active: 1,
          },
        },
        {
          model: db.HandoverWorkOrderAnswers,
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDER_ANSWERS,
          as: "question_answers",
          required: false,
          where: {
            is_active: 1,
            workorder_id: woId,
          },
        },
        {
          model: db.HandoverImageNaswers,
          attributes: ATTRIBUTES.HANDOVER_IAMGE_ANSWERS,
          as: "question_images",
          required: false,
          where: {
            is_active: 1,
            work_order_id: woId,
          },
        },
      ],
    });
    // Get user details from user authentication service
    const userAPI = await userService.getUserDetailsByUserIds({
      user_ids: [
        handoverWorkOrder.assigned_to_user_id,
        handoverWorkOrder.assigned_by_user_id,
        handoverWorkOrder.check_in_user_id,
      ],
      headers: request?.headers,
    });

    const userData = userAPI || [];

    const assigned_to_user =
      userData.find(
        (user) => user.id === handoverWorkOrder.assigned_to_user_id
      ) || {};
    const assigned_by_user =
      userData.find(
        (user) => user.id === handoverWorkOrder.assigned_by_user_id
      ) || {};

    const check_in_user =
      userData.find((user) => user.id === handoverWorkOrder.check_in_user_id) ||
      {};

    const responseData = {
      ...JSON.parse(JSON.stringify(handoverWorkOrder)),
      assigned_to_user,
      assigned_by_user,
      check_in_user,
      question: questionData,
    };

    return response.handler.success(
      MESSAGE.HANDOVER_WORK_ORDER.FOUND,
      responseData
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const submitAnswer = async (request, response) => {
  try {
    let {
      work_order_id: workOrderId,
      question_answer,
      is_encrypted,
    } = request?.body;
    if (is_encrypted == "true") {
      workOrderId = decrypt(workOrderId);
    }
    const questionAnswerData = question_answer?.flatMap((item) =>
      item.answers.map((answer) => ({
        question_id: item.question_id,
        answer,
        comment: item?.comment,
        workorder_id: workOrderId,
      }))
    );
    //check if hanover work order exist or not.
    const handoverWorkOrderData = await db.HandoverWorkOrders.findOne({
      where: { id: workOrderId, is_active: 1, is_deleted: 0 },
      include: {
        model: db.WorkOrderStatus,
        as: "handover_work_order_status",
        required: false,
        attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      },
    });

    if (isEmpty(handoverWorkOrderData)) {
      return response.handler.badRequest(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    const queIds = question_answer?.map((item) => item?.question_id);

    //check all the question exists
    const questionData = await db.HandoverQuestions.findAll({
      where: { id: queIds, is_active: 1, is_deleted: 0 },
    });

    if (queIds?.length !== questionData?.length) {
      return response.handler.badRequest(
        MESSAGE.HANDOVER_WORK_ORDER.INVALID_QUESTION_IDS
      );
    }

    //delete existing answers
    await db.HandoverWorkOrderAnswers.destroy({
      where: { question_id: queIds, workorder_id: workOrderId },
    });
    await db.HandoverImageNaswers.update(
      {
        is_active: 0,
        is_deleted: 1,
      },
      {
        where: { work_order_id: workOrderId },
      }
    );

    const imagePayloadData = [];
    if (request?.files?.length) {
      for (let i = 0; i < request?.files?.length; i++) {
        const data = request?.files?.[i];
        const { fieldname } = data;
        const index = fieldname.match(/\d+/g)[0];
        const questionId = question_answer?.[index]?.question_id;
        let Key, Location, metadata, mimetype;

        mimetype = data?.mimetype;
        const path = data?.path;
        const blob = fs.readFileSync(path); //convert file to blob
        const fileName = data?.originalname;
        const uploadSignatureImg = await aws.uploadFileS3(
          fileName,
          blob,
          `${CONSTANTS.S3_BUCKET.FOLDERS.HANDOVER_WORK_ORDER.BASE}`,
          metadata,
          mimetype
        );

        if (uploadSignatureImg) {
          Key = uploadSignatureImg.Key;
          Location = uploadSignatureImg.Location;
        }

        if (Key && Location) {
          imagePayloadData.push({
            question_id: questionId,
            work_order_id: workOrderId,
            image_key: Key,
            image_url: Location,
          });
        }
      }
    }

    await db.HandoverWorkOrderAnswers.bulkCreate(questionAnswerData);
    imagePayloadData?.length &&
      (await db.HandoverImageNaswers.bulkCreate(imagePayloadData));

    const inProgressWoStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS
    );

    await db.HandoverWorkOrders.update(
      { check_in_user_id: null, work_order_status: inProgressWoStatus?.id },
      { where: { id: workOrderId } }
    );

    if (handoverWorkOrderData?.zoho_handover_id) {
      const questionsData = await db.HandoverQuestions.findAll({
        where: { id: queIds, is_active: 1, is_deleted: 0 },
        attributes: ATTRIBUTES.HANDOVER_QUESTIONS,
        distinct: true,
        order: [["order", "ASC"]],
        include: [
          {
            model: db.HandoverWorkOrderAnswers,
            attributes: ATTRIBUTES.HANDOVER_WORK_ORDER_ANSWERS,
            as: "question_answers",
            required: false,
            where: {
              is_active: 1,
              workorder_id: workOrderId,
            },
          },
        ],
      });

      const answerPayload = {};

      CONSTANTS.ZOHO.HANDOVER_QUESTION?.forEach((item) => {
        const index = questionData?.findIndex(
          (que) => que?.title == item?.question
        );

        if (index !== -1) {
          answerPayload[item?.key] =
            questionsData?.[index]?.question_answers?.[0]?.answer;
        }
      });

      await zoho.updateHandoverQuestionAnswer({
        zoho_handover_id: handoverWorkOrderData?.zoho_handover_id,
        answerPayload,
      });
    }

    return response.handler.success(
      MESSAGE.HANDOVER_WORK_ORDER.SUBMIT_ANSWER_SUCCESS
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const checkIn = async (request, response) => {
  try {
    const { work_order_id } = request?.body;
    const loggedInUser = await request?.login_user?.data;
    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id: work_order_id,
        is_active: 1,
        is_deleted: 0,
      },
      include: {
        model: db.WorkOrderStatus,
        as: "handover_work_order_status",
        required: false,
        attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      },
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    if (
      woExist?.handover_work_order_status?.title ===
      CONSTANTS.HANDOVER_WORK_ORDER_STATUS.COMPLETED
    ) {
      return response.handler.notFound(
        MESSAGE.HANDOVER_WORK_ORDER.CHECK_IN.FAILED
      );
    }

    const inProgressWoStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS
    );

    const currentDateUTC = new Date();
    const time = currentDateUTC.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

    await db.HandoverWorkOrders.update(
      {
        actual_start_date: currentDateUTC,
        actual_start_time: time.toUpperCase(),
        check_in_user_id: loggedInUser?.id,
        work_order_status: inProgressWoStatus?.id,
      },
      {
        where: { id: work_order_id },
      }
    );
    return response.handler.success(
      MESSAGE.HANDOVER_WORK_ORDER.CHECK_IN.SUCCESS
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const checkOut = async (request, response) => {
  try {
    const { work_order_id } = request?.body;
    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id: work_order_id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    if (!woExist?.check_in_user_id) {
      return response.handler.notFound(
        MESSAGE.HANDOVER_WORK_ORDER.CHECK_OUT.CHECK_IN_FIRST
      );
    }
    await db.HandoverWorkOrders.update(
      {
        check_in_user_id: null,
      },
      {
        where: { id: work_order_id },
      }
    );
    return response.handler.success(
      MESSAGE.HANDOVER_WORK_ORDER.CHECK_OUT.SUCCESS
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendOtpToCustomer = async (request, response) => {
  try {
    let { work_order_id: id, is_encrypted } = request?.body;
    if (is_encrypted) {
      id = decrypt(id);
    }

    const questionId = await db.HandoverQuestions.findOne({
      where: {
        question_type: CONSTANTS.HANDOVER_QUESTION_TYPES.RATING,
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ["id"],
    });

    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id,
        is_active: 1,
        is_deleted: 0,
      },
      include: [
        {
          model: db.Customers,
          required: false,
          attributes: ATTRIBUTES.CUSTOMERS,
          as: "customer_data",
        },
        {
          model: db.WorkOrderStatus,
          as: "handover_work_order_status",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
        },
        {
          model: db.HandoverWorkOrderAnswers,
          as: "handover_work_order_answer",
          required: false,
          where: { is_active: 1, is_deleted: 0, question_id: questionId?.id },
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDER_ANSWERS,
        },
      ],
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    const templateSmsAPI = await userService.getTemplate({
      headers: request?.headers,
      action: [CONSTANTS.TEMPLATES.ACTION.SMS.SEND_HANDOVER_WORKORDER_OTP],
      type: CONSTANTS.TEMPLATES.TYPES.SMS,
    });

    const otp = await generateOtp(CONSTANTS.OTP.LENGTH, {
      digits: true,
      specialChars: false,
      upperCaseAlphabets: false,
      lowerCaseAlphabets: false,
    });

    if (templateSmsAPI) {
      const encryptId = encrypt(id.toString());
      const url = await replaceData(
        CONSTANTS.HANDOVER_EMAIL_LINK,
        "{WORKORDER_ID}",
        encryptId
      );

      const link = await common.shortenWithTinyURL(url);

      const emailContent = `<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Hi ${woExist?.customer_data?.name},</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">You're just <b>one step away from completing your handover process!</b> ✅</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Thank you for sharing your feedback with us!</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">You’ve rated us <b>${woExist?.handover_work_order_answer?.[0]?.answer}/10.</b> You can access the feedback and handover form here- 📝</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">${link}</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Please enter the OTP below to confirm the rating and complete the handover process:</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">🔐 ${otp}</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">(Valid for <b>${CONSTANTS.OTP.HANDOVER_EXPIRES_IN} minutes</b>)</p><br/>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Need help? Call us at 📞 97699 40000.</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;">– Team Eternia</p>`;

      const smsContent = `Hi ${woExist?.customer_data?.name},\n\nYou're just one step away from completing your handover process! ✅\nThank you for sharing your feedback with us!\nYou’ve rated us ${woExist?.handover_work_order_answer?.[0]?.answer}/10. You can access the feedback and handover form here- 📝\n${link}\nPlease enter the OTP below to confirm the rating and complete the handover process:\n🔐 ${otp}\n(Valid for ${CONSTANTS.OTP.HANDOVER_EXPIRES_IN} minutes)\n\nNeed help? Call us at 📞 97699 40000.\n– Team Eternia`;
      const whatsappTemplate = [
        woExist?.customer_data?.name,
        woExist?.handover_work_order_answer?.[0]?.answer.toString(),
        link,
        "Please enter the OTP below to confirm the rating and complete the handover process:",
        `🔐 *${otp}*  (Valid for ${CONSTANTS.OTP.HANDOVER_EXPIRES_IN} minutes)`,
      ];

      await sendSMS(`+91${woExist?.customer_data?.mobile}`, smsContent);
      await sendMail(
        woExist?.customer_data?.email,
        "OTP for handover submission",
        emailContent
      );
      await sendWhatsappMsg({
        campaignName: CONSTANTS.WHATSAPP.TEMPLATE_NAME.HANDOVER_WORK_ORDER_OTP,
        destination: woExist?.customer_data?.mobile,
        templateParams: whatsappTemplate,
      });

      await db.HandoverWorkOrders.update(
        {
          check_out_otp: otp,
          check_out_otp_created_at: new Date(),
        },
        { where: { id } }
      );
      return response.handler.success(
        MESSAGE.HANDOVER_WORK_ORDER.SEND_OTP.SUCCESS
      );
    }

    return response.handler.badRequest(MESSAGE.GENERAL_ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const verifyOtp = async (request, response) => {
  try {
    let { work_order_id: id, otp, is_encrypted } = request?.body;
    if (is_encrypted) {
      id = decrypt(id);
    }

    const questionId = await db.HandoverQuestions.findOne({
      where: {
        question_type: CONSTANTS.HANDOVER_QUESTION_TYPES.RATING,
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ["id"],
    });

    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id,
        is_active: 1,
        is_deleted: 0,
      },
      include: [
        {
          model: db.Customers,
          required: false,
          attributes: ATTRIBUTES.CUSTOMERS,
          as: "customer_data",
        },
        {
          model: db.WorkOrderStatus,
          as: "handover_work_order_status",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
        },
        {
          model: db.HandoverWorkOrderAnswers,
          as: "handover_work_order_answer",
          required: false,
          where: { is_active: 1, is_deleted: 0, question_id: questionId?.id },
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDER_ANSWERS,
        },
        {
          model: db.Orders,
          as: "order_data",
          required: false,
          attributes: ATTRIBUTES.ORDERS,
        },
      ],
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    if (woExist?.check_out_otp != otp) {
      return response.handler.badRequest(MESSAGE.OTP.INVALID);
    }

    const timeDiff = await getTimeDiff(
      woExist?.check_out_otp_created_at,
      new Date()
    );

    if (timeDiff?.minutes > CONSTANTS.OTP.HANDOVER_EXPIRES_IN) {
      return response.handler.badRequest(MESSAGE.OTP.EXPIRED);
    }

    const completedWoStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.COMPLETED
    );

    await db.HandoverWorkOrders.update(
      {
        check_out_otp: null,
        check_out_otp_created_at: null,
        work_order_status: completedWoStatus?.id,
        actual_end_datetime: new Date(),
      },
      { where: { id } }
    );

    //get current stage
    const token = await zoho.getZohoAuthToken();

    const currentStageUrl = `${process.env.ZOHO_API_URL}v8/Deals/${woExist?.order_data?.zoho_order_id}?fields=Stage`;
    const stageData = await axios.get(currentStageUrl, {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    });
    const currentStage = stageData?.data?.data?.[0]?.Stage;

    if (currentStage === CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE) {
      //if stage is warranty then need to remove tag that have added at raise ticket time
      await zoho.removeTagInOpportunity({
        zoho_order_id: woExist?.order_data?.zoho_order_id,
        tag_name: CONSTANTS.ZOHO_TAG.HANDOVER_FEEDBACK_ISSUE,
      });
    } else {
      const payload = {
        blueprint: [
          {
            transition_id: CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.ID,
            data: {
              CheckLists: CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.CHECKLIST,
            },
          },
        ],
      };
      await zoho.updateZohoStage({
        zoho_order_id: woExist?.order_data?.zoho_order_id,
        token,
        payload,
      });
    }

    let message = "";
    const rating = woExist?.handover_work_order_answer?.[0]?.answer;
    if (rating) {
      if (Number(rating) > 8) {
        message =
          "Thanks again for your wonderful feedback! We're glad you had a great experience. 😊";
      } else if (Number(rating) < 5) {
        message =
          " We’re sorry to hear that your experience wasn’t up to the mark. Your feedback is valuable and helps us improve.";
      } else {
        message = `You’ve rated us ${rating}/10.`;
      }
    } else {
      message =
        "Thank you for sharing your feedback with us. We truly appreciate it!";
    }

    const whatsappTemplate = [
      woExist?.customer_data?.name,
      message,
      rating?.toString(),
    ];
    await sendWhatsappMsg({
      campaignName: CONSTANTS.WHATSAPP.TEMPLATE_NAME.HANDOVER_WORK_ORDER_SUBMIT,
      destination: woExist?.customer_data?.mobile,
      templateParams: whatsappTemplate,
    });

    return response.handler.success(MESSAGE.OTP.VERIFICATION.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const addSignature = async (request, response) => {
  try {
    let { work_order_id: id, is_encrypted } = request?.body;
    if (is_encrypted == "true") {
      id = decrypt(id);
    }
    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    // upload signature
    if (request.file) {
      // upload img to s3
      let Key, Location, metadata, mimetype;
      if (request.file) {
        mimetype = request.file?.mimetype;
        const path = request.file?.path;
        const blob = fs.readFileSync(path); //convert file to blob
        const fileName = request.file?.originalname;
        const uploadSignatureImg = await aws.uploadFileS3(
          fileName,
          blob,
          `${CONSTANTS.S3_BUCKET.FOLDERS.HANDOVER_WORK_ORDER.BASE}/${CONSTANTS.S3_BUCKET.FOLDERS.HANDOVER_WORK_ORDER.SIGNATURE}`,
          metadata,
          mimetype
        );

        if (uploadSignatureImg) {
          Key = uploadSignatureImg.Key;
          Location = uploadSignatureImg.Location;
        }
      }
      // const base64 = await aws.convertS3UrlToBase64(Key);

      if (Key && Location) {
        await db.HandoverWorkOrders.update(
          {
            image_key: Key,
            image_url: Location,
          },
          { where: { id } }
        );
        return response.handler.success(
          MESSAGE.HANDOVER_WORK_ORDER.UPLOAD_SIGNATURE.SUCCESS
        );
      }
    }
    return response.handler.badRequest(
      MESSAGE.HANDOVER_WORK_ORDER.UPLOAD_SIGNATURE.EMPTY
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const update = async (request, response) => {
  try {
    const {
      id,
      assigned_to_user_id,
      assigned_to_user_role_id,
      scheduled_start_date,
      scheduled_start_time,
    } = request?.body;
    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }
    const pendingWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.PENDING
    );

    const newScheduleDate = new Date(woExist?.scheduled_start_date);
    newScheduleDate.setDate(newScheduleDate.getDate() + 1);

    const nextDateStr = newScheduleDate.toISOString().split("T")[0];

    const payload = {
      work_order_status: pendingWOStatus?.id,
      assigned_to_user_id: assigned_to_user_id || woExist?.assigned_to_user_id,
      assigned_to_user_role_id:
        assigned_to_user_role_id || woExist?.assigned_to_user_role_id,
      scheduled_start_date: scheduled_start_date || nextDateStr,
      scheduled_start_time:
        scheduled_start_time || woExist?.scheduled_start_time,
      check_in_user_id: null,
    };

    await db.HandoverWorkOrders.update(payload, { where: { id } });
    return response.handler.success(MESSAGE.HANDOVER_WORK_ORDER.UPDATE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const reject = async (request, response) => {
  try {
    const { work_order_id: id } = request?.body;
    const woExist = await db.HandoverWorkOrders.findOne({
      where: {
        id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (!woExist) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }
    const rejectWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.REJECTED
    );

    const payload = {
      work_order_status: rejectWOStatus?.id,
    };

    await db.HandoverWorkOrders.update(payload, { where: { id } });
    return response.handler.success(MESSAGE.HANDOVER_WORK_ORDER.UPDATE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const raiseComplaint = async (request, response) => {
  try {
    const userToken = request?.headers?.authorization;

    if (!userToken) {
      return response.handler.unauthorized(MESSAGE.TOKEN_NOT_FOUND);
    }

    if (userToken?.split(" ")?.[1] !== process.env.RAISE_TICKET_TOKEN) {
      return response.handler.unauthorized(MESSAGE.INVALID_TOKEN);
    }

    const { opp_id: opportunity_id } = request?.query;

    if (!opportunity_id) {
      return response.handler.badRequest(
        MESSAGE.ORDERS.OPPORTUNITY_ID_REQUIRED
      );
    }

    const orderData = await db.Orders.findOne({
      where: { zoho_order_id: opportunity_id },
      attributes: ATTRIBUTES.ORDERS,
      include: [
        {
          model: db.HandoverWorkOrders,
          as: "handover_work_order",
          required: false,
          attributes: ATTRIBUTES.HANDOVER_WORK_ORDERS,
        },
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
      ],
    });

    if (!orderData) {
      return response.handler.notFound(MESSAGE.ORDERS.FOUND.ERROR);
    }

    if (!orderData?.handover_work_order) {
      return response.handler.notFound(MESSAGE.HANDOVER_WORK_ORDER.NOT_FOUND);
    }

    //check zoho satge
    const token = await zoho.getZohoAuthToken();

    const fields = "id,Stage";
    const url = `${process.env.ZOHO_API_URL}v5/Deals/${orderData?.zoho_order_id}?fields=${fields}`;

    const config = {
      method: "GET",
      url: url,
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    const zohoOrderData = await axios(config);

    if (
      zohoOrderData?.data?.data?.[0]?.Stage !==
      CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE
    ) {
      return response.handler.badRequest(
        MESSAGE.HANDOVER_WORK_ORDER.INVALID_ZOHO_STAGE_FOR_COMPLAINT
      );
    }

    //update handover work order status to rejected
    const rejectWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.REJECTED
    );

    await db.HandoverWorkOrders.update(
      { work_order_status: rejectWOStatus?.id },
      { where: { id: orderData?.handover_work_order?.id } }
    );

    const encryptId = encrypt(orderData?.handover_work_order?.id.toString());
    const handoverWoUrl = await replaceData(
      CONSTANTS.HANDOVER_EMAIL_LINK,
      "{WORKORDER_ID}",
      encryptId
    );

    const link = await common.shortenWithTinyURL(handoverWoUrl);
    const ticketData = `Customer ${orderData?.customer_data?.name} has raised a feedback issue for the opportunity ${orderData?.zoho_order_id}. Kindly connect with the customer to rectify the feedback as per customer's request. Customer details : mobile - ${orderData?.customer_data?.mobile}, email id - ${orderData?.customer_data?.email}.\nHandover sheet link: ${link}.\nComplete the handover feedback from the above link as per customer's input to close this ticket.`;
    const raiseTicketData = {
      description: ticketData,
      subject: "Handover feedback issue",
      customer_data: orderData?.customer_data,
    };

    const data = await zoho.raiseTicket(raiseTicketData);

    await zoho.addTagInOpportunity({
      zoho_order_id: opportunity_id,
      tag_name: CONSTANTS.ZOHO_TAG.HANDOVER_FEEDBACK_ISSUE,
    });

    return response.status(200).json({
      status: true,
      message: "Complaint raised successfully",
      tickedID: data?.ticketNumber,
      order_id: orderData?.actual_order_id,
    });
  } catch (error) {
    return response.status(500).json({
      status: false,
      message: "Something went wrong",
    });
  }
};

module.exports = {
  create,
  get,
  getById,
  submitAnswer,
  checkIn,
  sendOtpToCustomer,
  checkOut,
  verifyOtp,
  addSignature,
  update,
  reject,
  raiseComplaint,
};
