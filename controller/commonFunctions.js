const { isEmpty } = require("lodash");
const https = require("https");
const querystring = require("querystring");

const db = require("../database/models");

const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { CONSTANTS } = require("../helpers/constants");
const { logger } = require("../helpers/logger.js");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const { Op } = require("sequelize");

const getOrderStatusByTitle = async (title) => {
  try {
    if (!title) {
      return null;
    }

    const order_status = await db.OrderStatus.findOne({
      where: { title },
      attributes: ATTRIBUTES.ORDER_STATUS,
    });

    return !isEmpty(order_status) ? order_status : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getOrderStatusById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const order_status = await db.OrderStatus.findByPk(id, {
      attributes: ATTRIBUTES.ORDER_STATUS,
    });

    return !isEmpty(order_status) ? order_status : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWorkOrderStatusByTitle = async (title) => {
  try {
    if (!title) {
      return null;
    }

    const wo_status = await db.WorkOrderStatus.findOne({
      where: { title },
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
    });

    return !isEmpty(wo_status) ? wo_status : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWorkOrderStatusById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const wo_status = await db.WorkOrderStatus.findByPk(id, {
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
    });

    return !isEmpty(wo_status) ? wo_status : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWorkOrderById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const work_order = await db.WorkOrders.findOne({
      where: { id, is_active: 1 },
      attributes: ATTRIBUTES.WORK_ORDERS.filter(
        (value) => value !== "selfie_image_base64"
      ),
      include: [
        {
          model: db.WorkOrderStatus,
          as: "work_order_status",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
        },
        {
          model: db.Customers,
          as: "customer_data",
          required: false,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
        {
          model: db.WorkOrderTypes,
          as: "work_order_type",
          required: false,
          attributes: ATTRIBUTES.WORK_ORDER_TYPES,
        },
        {
          model: db.Orders,
          as: "wo_order",
          required: false,
          attributes: ATTRIBUTES.ORDERS,
          include: [
            {
              model: db.OrderStatus,
              as: "order_status",
              required: false,
              attributes: ATTRIBUTES.ORDER_STATUS,
            },
          ],
        },
      ],
    });

    return !isEmpty(work_order) ? work_order : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getQuestionById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const que = await db.Questions.findByPk(id, {
      attributes: ATTRIBUTES.QUESTIONS,
    });

    return !isEmpty(que) ? que : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getQuestionByConditions = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const question = await db.Questions.findOne({
      where: condition,
      attributes: ATTRIBUTES.QUESTIONS,
    });

    return !isEmpty(question) ? question : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getQuestionMappingById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const queMapping = await db.QuestionMapping.findByPk(id, {
      attributes: ATTRIBUTES.QUESTION_MAPPING,
    });

    return !isEmpty(queMapping) ? queMapping : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getQuestionMappingByWoTypeIdScopeId = async (
  work_order_type_id,
  scope_id
) => {
  try {
    if (!work_order_type_id || !scope_id) {
      return null;
    }

    const queMapping = await db.QuestionMapping.findOne({
      where: {
        work_order_type_id,
        scope_id,
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ATTRIBUTES.QUESTION_MAPPING,
    });

    return !isEmpty(queMapping) ? queMapping : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getQuestionTypeById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const queType = await db.QuestionTypes.findByPk(id, {
      attributes: ATTRIBUTES.QUESTION_TYPES,
    });

    return !isEmpty(queType) ? queType : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getScopeById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const scope = await db.Scopes.findOne({
      where: { id },
      attributes: [
        ...ATTRIBUTES.SCOPES,
        [
          db.sequelize.fn(
            "CONCAT",
            db.sequelize.col("height"),
            " ⨯ ",
            db.sequelize.col("width")
          ),
          "dimension",
        ],
      ],
      include: [
        {
          model: db.ScopeImages,
          as: "scope_image",
          required: false,
          attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
            (value) => value !== "base64"
          ),
          include: [
            {
              model: db.ScopeImageTouchPoints,
              as: "touch_points",
              required: false,
              attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
            },
          ],
        },
      ],
    });

    return !isEmpty(scope) ? scope : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getScopeByModelAndName = async (model, name) => {
  try {
    if (!model && !name) {
      return null;
    }

    const scope = await db.Scopes.findOne({
      where: [{ model }, { [Op.or]: [{ name }, { new_name: name }] }],
      attributes: [
        ...ATTRIBUTES.SCOPES,
        [
          db.sequelize.fn(
            "CONCAT",
            db.sequelize.col("height"),
            " ⨯ ",
            db.sequelize.col("width")
          ),
          "dimension",
        ],
      ],
      logging: false,
    });

    return !isEmpty(scope) ? scope : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getScopeByConditions = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const scope = await db.Scopes.findOne({
      where: condition,
      attributes: [
        ...ATTRIBUTES.SCOPES,
        [
          db.sequelize.fn(
            "CONCAT",
            db.sequelize.col("height"),
            " ⨯ ",
            db.sequelize.col("width")
          ),
          "dimension",
        ],
      ],
    });

    return !isEmpty(scope) ? scope : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getSpaceById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const space = await db.Spaces.findByPk(id, {
      attributes: ATTRIBUTES.SPACES,
    });

    return !isEmpty(space) ? space : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getSpaceByTitle = async (title) => {
  try {
    if (!title) {
      return null;
    }

    const space = await db.Spaces.findOne({
      where: { title },
      attributes: ATTRIBUTES.SPACES,
      logging: false,
    });

    return !isEmpty(space) ? space : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getSpaceByConditions = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const space = await db.Spaces.findOne({
      where: condition,
      attributes: ATTRIBUTES.SPACES,
    });

    return !isEmpty(space) ? space : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getTaskById = async (id) => {
  try {
    if (!id) {
      return null;
    }
    const task = await db.Tasks.findByPk(id, { attributes: ATTRIBUTES.TASKS });

    return !isEmpty(task) ? task : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const isTaskExist = async (id) => {
  try {
    if (!id) {
      return false;
    }
    const task = await db.Tasks.count({ where: { id } });

    return task ? true : false;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return false;
  }
};

const getWorkOrderTypeById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const wo_type = await db.WorkOrderTypes.findByPk(id, {
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
    });

    return !isEmpty(wo_type) ? wo_type : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWorkOrderTypeByTitle = async (title) => {
  try {
    if (!title) {
      return null;
    }

    const wo_type = await db.WorkOrderTypes.findOne({
      where: { title },
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
    });

    return !isEmpty(wo_type) ? wo_type : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWoTypeByConditions = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const woType = await db.WorkOrderTypes.findOne({
      where: condition,
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
    });

    return !isEmpty(woType) ? woType : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getCustomerByEmail = async (email) => {
  try {
    if (!email) {
      return null;
    }

    const customer = await db.Customers.findOne({
      where: { email },
      attributes: ATTRIBUTES.CUSTOMERS,
    });

    return !isEmpty(customer) ? customer : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getPointRatesByCondition = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const rd = await db.PointRates.findOne({
      where: condition,
      attributes: ATTRIBUTES.POINT_RATES,
    });

    return !isEmpty(rd) ? rd : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getPointsOfWoTypeByCondition = async (condition) => {
  try {
    if (!condition?.length) {
      return null;
    }

    const woTp = await db.WorkOrderTypePoints.findOne({
      where: condition,
      attributes: ATTRIBUTES.WORK_ORDER_TYPE_POINTS,
    });

    return !isEmpty(woTp) ? woTp : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const updateUserPointBalance = async (user_id) => {
  try {
    if (!user_id) {
      return null;
    }

    // get point history of user.
    const pointHistory = await db.PointHistory.findAll({
      where: [{ user_id }, { is_read: 0 }],
      include: [
        {
          model: db.WorkOrderTypePoints,
          as: "wo_points_data",
          required: true,
          attributes: ATTRIBUTES.WORK_ORDER_TYPE_POINTS,
        },
        {
          model: db.PointRates,
          as: "rates_data",
          required: true,
          attributes: ATTRIBUTES.POINT_RATES,
        },
      ],
    });

    if (!pointHistory?.length) {
      return null;
    }

    let total_points = 0;
    let total_earnings = 0;

    const userBalance = await db.PointBalance.findOne({
      where: { user_id },
    });

    if (isEmpty(userBalance)) {
      const { wo_points_data, rates_data, id } = pointHistory[0];

      total_points =
        wo_points_data.transaction_type ===
        CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT
          ? total_points + wo_points_data.points_to_assign
          : total_points - wo_points_data.points_to_assign;

      total_earnings =
        wo_points_data.transaction_type ===
        CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT
          ? total_earnings +
            (total_points / rates_data.points) * rates_data.rate
          : total_earnings -
            (total_points / rates_data.points) * rates_data.rate;

      await db.PointBalance.create({
        user_id,
        total_points,
        total_earnings,
      });

      await db.PointHistory.update({ is_read: 1 }, { where: { id } });

      return true;
    } else {
      total_points = userBalance.total_points;
      total_earnings = parseFloat(userBalance.total_earnings);

      let points = 0;
      let earnings = 0;
      for (let i = 0; i < pointHistory.length; i++) {
        const { wo_points_data, rates_data, id } = pointHistory[i];

        points =
          wo_points_data.transaction_type ===
          CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT
            ? (points = points + wo_points_data.points_to_assign)
            : points - wo_points_data.points_to_assign;

        earnings =
          wo_points_data.transaction_type ===
          CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT
            ? earnings +
              (wo_points_data.points_to_assign / rates_data.points) *
                rates_data.rate
            : earnings -
              (wo_points_data.points_to_assign / rates_data.points) *
                rates_data.rate;

        await db.PointHistory.update({ is_read: 1 }, { where: { id } });
      }

      total_points = total_points + points;
      total_earnings = total_earnings + earnings;

      await db.PointBalance.update(
        { total_points, total_earnings },
        { where: { user_id } }
      );

      return true;
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getPointBalanceByUserId = async (user_id) => {
  try {
    if (!user_id) {
      return null;
    }

    const balance = await db.PointBalance.findOne({
      where: { user_id },
      attributes: ATTRIBUTES.POINT_BALANCE,
    });

    return !isEmpty(balance) ? balance : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getTaskVideoAnswerById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const taskVideoAnswer = await db.TaskVideoAnswers.findByPk(id, {
      attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
    });

    return !isEmpty(taskVideoAnswer) ? taskVideoAnswer : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const isLineItemExist = async (lineItemData) => {
  const {
    master_order_id,
    zoho_quote_line_item_id,
    room,
    product_name,
    description,
    quantity,
    name,
    height,
    width,
    reference,
  } = lineItemData;

  const lineItemExist = await db.MasterOrderQuoteLineItems.findOne({
    where: {
      master_order_id,
      zoho_quote_line_item_id,
      room,
      product_name,
      description,
      quantity,
      name,
      height,
      width,
      reference,
    },
    attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
    raw: true,
    logging: false,
  });

  return lineItemExist || null;
};

const getReportTagById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const reportTag = await db.ReportTags.findByPk(id, {
      attributes: ATTRIBUTES.REPORT_TAGS,
    });

    return !isEmpty(reportTag) && reportTag?.is_active ? reportTag : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getReportTagByName = async (name) => {
  try {
    if (!name) {
      return null;
    }

    const reportTag = await db.ReportTags.findOne({
      where: { name, is_active: 1 },
      attributes: ATTRIBUTES.REPORT_TAGS,
      logging: false,
    });

    return !isEmpty(reportTag) ? reportTag : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getWorkOrderCustomerById = async (id) => {
  try {
    if (!id) {
      return null;
    }

    const woCustomerData = await db.WorkOrders.findOne({
      where: { id },
      attributes: ATTRIBUTES.WORK_ORDERS.filter(
        (value) => value !== "selfie_image_base64"
      ),
      include: [
        {
          model: db.Customers,
          as: "customer_data",
          required: true,
          attributes: ATTRIBUTES.CUSTOMERS,
        },
      ],
    });

    return !isEmpty(woCustomerData) ? woCustomerData : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const sendNotificationToUsers = async (
  headers,
  sender_user_id,
  receiver_and_send_to,
  userDeviceTokens
) => {
  try {
    // get notification template from user service.
    const templateAPI = await userService.getTemplate({
      headers,
      action: [
        CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REQUEST,
      ],
      type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
    });

    if (templateAPI) {
      const { title, body } = templateAPI[0];

      // add notification alert history
      await userService.addAlerts({
        headers: request?.headers,
        sender_user_id,
        title,
        body,
        action:
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REQUEST,
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        receiver_and_send_to: receiver_and_send_to,
        is_sent: 1,
      });

      await sendNotification(userDeviceTokens, title, body);
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getScopeDetails = async (zoho_order_id, data) => {
  try {
    if (!zoho_order_id) {
      return null;
    }
    const masterOrderData = await db.MasterOrders.findOne({
      distinct: true,
      where: { zoho_order_id },
      include: [
        {
          model: db.MasterOrderQuoteLineItems,
          as: "line_items_data",
          required: true,
          where: { is_active: 1, is_deleted: 0 },
          attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
        },
      ],
    });

    const respWorkOrderSpaceData = [];
    data?.work_order_spaces?.forEach((space) => {
      let scopeName;
      const index = masterOrderData?.line_items_data?.findIndex(
        (master) => master?.room === space?.space_data?.title
      );
      if (index !== -1) {
        scopeName = masterOrderData?.line_items_data?.[index]?.product_name;
      }

      const scopeData = [];
      if (space?.work_order_scopes?.length) {
        space?.work_order_scopes?.forEach((scope) => {
          if (!scope?.scope_data?.is_part) {
            let tmpScopeData = { ...JSON.parse(JSON.stringify(scope)) };
            if (
              scopeName &&
              scope?.scope_data?.name !== scopeName &&
              scope?.scope_data?.new_name === scopeName
            ) {
              tmpScopeData = {
                ...JSON.parse(JSON.stringify(scope)),
                scope_data: {
                  ...JSON.parse(JSON.stringify(scope?.scope_data)),
                  name: scopeName,
                },
              };
            } else {
              tmpScopeData = {
                ...JSON.parse(JSON.stringify(scope)),
              };
            }
            scopeData.push(tmpScopeData);
          }
        });
      }

      if (scopeData?.length) {
        respWorkOrderSpaceData.push({
          ...JSON.parse(JSON.stringify(space)),
          work_order_scopes: scopeData,
        });
      }
    });

    return respWorkOrderSpaceData;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getInternalTeamData = async (id, headers) => {
  try {
    if (!id) return null;
    const userData = await userService.getUserDetailsByUserIds({
      user_ids: [id],
      headers,
    });
    return userData?.[0] || null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

function shortenWithTinyURL(longUrl) {
  return new Promise((resolve, reject) => {
    const query = querystring.stringify({ url: longUrl });
    const options = {
      hostname: "tinyurl.com",
      path: `/api-create.php?${query}`,
      method: "GET",
    };

    const req = https.request(options, (res) => {
      let data = "";
      res.on("data", (chunk) => (data += chunk));
      res.on("end", () => resolve(data));
    });

    req.on("error", (e) => reject(e));
    req.end();
  });
}

module.exports = {
  getOrderStatusByTitle,
  getOrderStatusById,
  getWorkOrderStatusByTitle,
  getWorkOrderStatusById,
  getWorkOrderById,
  getQuestionById,
  getWorkOrderCustomerById,
  getQuestionByConditions,
  getQuestionMappingById,
  getQuestionMappingByWoTypeIdScopeId,
  getQuestionTypeById,
  getScopeById,
  getScopeByModelAndName,
  getScopeByConditions,
  getSpaceById,
  getSpaceByTitle,
  getSpaceByConditions,
  getTaskById,
  isTaskExist,
  getWorkOrderTypeById,
  getWorkOrderTypeByTitle,
  getWoTypeByConditions,
  getCustomerByEmail,
  getPointRatesByCondition,
  getPointsOfWoTypeByCondition,
  updateUserPointBalance,
  getPointBalanceByUserId,
  getTaskVideoAnswerById,
  isLineItemExist,
  getReportTagById,
  getReportTagByName,
  sendNotificationToUsers,
  getScopeDetails,
  getInternalTeamData,
  shortenWithTinyURL,
};
