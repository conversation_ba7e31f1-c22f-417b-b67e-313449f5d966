const { Op } = require("sequelize");
const { isEmpty } = require("lodash");
const generateOtp = require("otp-generator").generate;
const fs = require("fs");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const {
  getTimeDiff,
  getUniqueAndNotNullValueFromArray,
  replaceData,
  getPadString,
  encrypt,
} = require("../helpers/functions");
const { MESSAGE } = require("../helpers/messages");
const { sendSMS } = require("../helpers/twilio");
const { sendNotification } = require("../helpers/notification");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const common = require("./commonFunctions.js");
const aws = require("../helpers/awsS3.js");
const { logger } = require("../helpers/logger");
const { generatePDF } = require("../helpers/pdf");
const { sendMail } = require("../helpers/mail");
const zoho = require("../helpers/zohoApis.js");
const { sendWhatsappMsg } = require("../helpers/whatsappMessage.js");

const create = async (request, response) => {
  try {
    const {
      order_id,
      customer_id,
      work_order_type_id,
      assigned_to_user_id,
      assigned_to_user_role_id,
      instructions,
      scheduled_start_date,
      scheduled_start_time,
      scheduled_duration,
      space_scopes,
    } = request.body;

    const loggedInUser = await request?.login_user?.data;

    // check if requested order is exist or not.
    const orderExist = await db.Orders.findOne({ where: { id: order_id } });

    if (isEmpty(orderExist)) {
      return response.handler.notFound(MESSAGE.ORDERS.NOT_EXIST);
    }

    // Work Order Types
    const woTypes = await db.WorkOrderTypes.findAll({
      attributes: ATTRIBUTES.WORK_ORDER_TYPES,
      raw: true,
    });

    const [surveyWoType, qcWoType, handoverWoType] = await Promise.all([
      woTypes.find((wot) => wot.title === CONSTANTS.WORK_ORDER.TYPE.SURVEY),
      woTypes.find(
        (wot) => wot.title === CONSTANTS.WORK_ORDER.TYPE.QUALITY_CHECK
      ),
      woTypes.find((wot) => wot.title === CONSTANTS.WORK_ORDER.TYPE.HANDOVER),
    ]);

    // Work Order Status
    const woStatus = await db.WorkOrderStatus.findAll({
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      raw: true,
    });

    const woData = await db.WorkOrders.findAll({
      where: { order_id, is_active: 1 },
      include: [
        {
          model: db.WorkOrderSpaces,
          as: "work_order_spaces",
          include: [
            {
              model: db.WorkOrderScopes,
              as: "work_order_scopes",
            },
          ],
        },
      ],
    });

    let woSpaceScopeObj = {};
    if (woData?.length) {
      woData?.forEach((wo) => {
        if (wo?.work_order_spaces?.length) {
          wo?.work_order_spaces?.forEach((woScope) => {
            const scopeId = woScope?.work_order_scopes?.map(
              (item) => item?.scope_id
            );
            woSpaceScopeObj[woScope?.space_id] = scopeId;
          });
        }
      });
    }

    let errorRes = "";
    if (space_scopes?.length) {
      for (let i = 0; i < space_scopes?.length; i++) {
        const spaceScopeData = space_scopes[i];
        if (spaceScopeData?.space_id in woSpaceScopeObj) {
          for (let j = 0; j < spaceScopeData?.scopes?.length; j++) {
            const item = spaceScopeData?.scopes[j];
            if (
              woSpaceScopeObj?.[spaceScopeData?.space_id]?.includes(
                item?.scope_id
              )
            ) {
              errorRes = `(${spaceScopeData?.space_id},${item?.scope_id})`;
              break;
            }
          }
        }
      }
    }

    if (errorRes) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.ADD.EXIST_WITH_SCOPES_AND_SPACES(errorRes)
      );
    }

    const [completedWoStatus, pendingWoStatus] = await Promise.all([
      woStatus.find(
        (wos) => wos.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED
      ),
      woStatus.find((wos) => wos.title === CONSTANTS.WORK_ORDER.STATUS.PENDING),
    ]);

    // get all scope's id from request.
    let requested_scope_ids = [];
    const scopes_data = await space_scopes.flatMap((item) => item.scopes);
    await scopes_data.map((data) => requested_scope_ids.push(data.scope_id));

    // same scope can be multiple times, so we need to filter unique from them.
    const unique_requested_scope_ids = await getUniqueAndNotNullValueFromArray(
      requested_scope_ids
    );

    /*
            check if requested scope have image & touchpoints or not?
            - we can identify by checking the source of scope. If source is ZOHO, that means scope does not have image & touch points.
        */
    const scopesWithoutImageTp = await verifyIfScopeHaveImageAndTouchPoints(
      unique_requested_scope_ids
    );

    if (scopesWithoutImageTp.length) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.ADD.IMAGE_TP_PENDNG(scopesWithoutImageTp.join(", "))
      );
    }

    /*
            check if question set is exist or not with requested WorkOrderType & Scope. If not exist then check with default scope model and WorkOrderType. If still not available then we will restrict to create WorkOrder.
        */
    const scopesWithoutQueMapping = await verifyQueMappingForWoTypeAndScope(
      work_order_type_id,
      unique_requested_scope_ids
    );

    if (scopesWithoutQueMapping) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.ADD.FAILED.QUE_SET_NOT_EXIST(scopesWithoutQueMapping)
      );
    }

    /*
            SURVEY -> QUALITY CHECK (QC) -> HANDOVER
            - while creating work order with QC type, first need to check if Survey is completed or not for that scope. If Survey not completed then not allow to create work order.
            - while creating work order with Handover type, first need to check if QC is completed or not for that scope. If QC not completed then not allow to create work order.

            if requested workOrderType is QualityCheck (QC), then we need to verify below points.
            - while creating QC for any scopes, the survey should be completed for that scope, if survey is not completed for any scope, then we will not allow to create QC for that scope.
            - for Ex: if there is scope with 4 quantity, and Survey is completed for 2, then we will create QC for only completed scope. for others QC will not be created.
        */

    if ([qcWoType.id, handoverWoType.id].includes(work_order_type_id)) {
      const verify = await verifyQualityCheckOrHandoverCreation(
        orderExist,
        work_order_type_id,
        surveyWoType,
        qcWoType,
        handoverWoType,
        completedWoStatus,
        space_scopes
      );

      if (verify?.error) {
        return response.handler.badRequest(verify.error);
      }
    }

    const scheduleDateTime = await getFormattedScheduleStartDateTime(
      scheduled_start_date,
      scheduled_start_time
    );

    const addWorkOrder = await db.WorkOrders.create({
      order_id: order_id,
      customer_id: customer_id,
      work_order_status_id: pendingWoStatus?.id || null,
      work_order_type_id: work_order_type_id,
      assigned_to_user_id: assigned_to_user_id,
      assigned_to_user_role_id: assigned_to_user_role_id,
      assigned_by_user_id: loggedInUser?.id,
      assigned_by_user_role_id: loggedInUser?.role?.id,
      scheduled_start_date: scheduleDateTime.date,
      scheduled_start_time: scheduleDateTime.time,
      scheduled_duration: scheduled_duration,
      instructions: instructions,
    });

    if (addWorkOrder) {
      const work_order_id = addWorkOrder?.id;
      // update actual_work_order_id
      const actual_work_order_id = await getPadString(work_order_id, "WO");
      await db.WorkOrders.update(
        { actual_work_order_id: actual_work_order_id },
        { where: { id: work_order_id } }
      );

      // Order Status
      const oStatus = await db.OrderStatus.findAll({
        attributes: ATTRIBUTES.ORDER_STATUS,
        raw: true,
      });

      const [completeOrderStatus, ongoingOrderStatus] = await Promise.all([
        oStatus.find((os) => os.title === CONSTANTS.ORDER_STATUS.COMPLETED),
        oStatus.find((os) => os.title === CONSTANTS.ORDER_STATUS.ONGOING),
      ]);

      // after creating work order, if order is completed then again we need to change order status to ongoing.
      if (orderExist.order_status_id === completeOrderStatus?.id) {
        await db.Orders.update(
          { order_status_id: ongoingOrderStatus?.id },
          { where: { id: order_id } }
        );
      }

      // map space & scopes with work order.
      let warningScopesArr = [];
      for (const spaceScope of space_scopes) {
        const space_id = await spaceScope.space_id;
        const addWoSpace = await db.WorkOrderSpaces.create({
          work_order_id,
          space_id,
        });

        if (addWoSpace) {
          for (const scope of spaceScope.scopes) {
            const scopeData = await common.getScopeById(scope.scope_id);

            const scope_counts =
              scope?.completed_parent_task_count !== undefined
                ? scope?.completed_parent_task_count
                : scope.scope_count || 1;

            if (!scopeData?.is_part) {
              const addWoScopes = await db.WorkOrderScopes.create({
                work_order_id,
                work_order_space_mapping_id: addWoSpace?.id,
                scope_id: scope.scope_id,
                count: scope.scope_count || 1,
                task_count: scope_counts,
              });

              // create task for the scope
              if (addWoScopes?.id) {
                //get question mapping based on work order type & scope
                let question_mapping_id = null;
                const questionMapping =
                  await common.getQuestionMappingByWoTypeIdScopeId(
                    work_order_type_id,
                    scope.scope_id
                  );

                if (questionMapping) {
                  question_mapping_id = questionMapping.id;
                } else {
                  const { name } = scopeData;
                  const scopeWithDefaultModel =
                    await common.getScopeByModelAndName(
                      CONSTANTS.SCOPE.DEFAULT_MODEL,
                      name
                    );
                  if (scopeWithDefaultModel) {
                    const defaultQuestionMapping =
                      await common.getQuestionMappingByWoTypeIdScopeId(
                        work_order_type_id,
                        scopeWithDefaultModel.id
                      );

                    if (defaultQuestionMapping) {
                      question_mapping_id = defaultQuestionMapping.id;
                    }
                  }
                }

                // we will create multiple tasks for the scope based on counts.
                if (scope_counts) {
                  for (let k = 0; k < scope_counts; k++) {
                    if (
                      scope.completed_parent_task_count !== undefined &&
                      scope.completed_parent_task_count !== scope.scope_count
                    ) {
                      warningScopesArr.push({
                        id: scope.scope_id,
                        name: scopeData.name,
                        model: scopeData.model,
                        total_qty: scope.scope_count,
                        created_qty: scope?.completed_parent_task_count,
                        pendingQty:
                          scope.scope_count -
                          scope?.completed_parent_task_count,
                      });
                    }

                    if (scope_counts > 0) {
                      const createTask = await db.Tasks.create({
                        work_order_id: work_order_id,
                        work_order_scope_mapping_id: addWoScopes?.id,
                        question_mapping_id: question_mapping_id,
                        status: CONSTANTS.TASK_STATUS.PENDING,
                      });

                      if (createTask?.id) {
                        const actual_task_id = await getPadString(
                          createTask.id,
                          "T"
                        );

                        await db.Tasks.update(
                          { actual_task_id: actual_task_id },
                          { where: { id: createTask.id } }
                        );
                      }
                    }
                  }
                } else {
                  warningScopesArr.push({
                    id: scope.scope_id,
                    name: scopeData.name,
                    model: scopeData.model,
                    total_qty: scope.scope_count,
                    created_qty: scope?.completed_parent_task_count,
                    pendingQty:
                      scope.scope_count - scope?.completed_parent_task_count,
                  });
                }
              }
            }
          }
        }
      }

      //send notification to regarding work order assigned.
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: [loggedInUser?.id, assigned_to_user_id],
        headers: request?.headers,
      });

      let assignedByUserData, assignedToUserData;
      if (userAPI) {
        assignedByUserData = await userAPI.find(
          (data) => data.id == loggedInUser?.id
        );
        assignedToUserData = await userAPI.find(
          (data) => data.id == assigned_to_user_id
        );
      }

      const assignedByUserName = `${assignedByUserData?.first_name} ${assignedByUserData?.last_name}`;

      // get notification template from user service.
      const templateAPI = await userService.getTemplate({
        headers: request?.headers,
        action: [CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_ASSIGNED],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        let replaceBody = await replaceData(
          body,
          "{ASSIGNED_BY_USER}",
          assignedByUserName
        );
        replaceBody = await replaceData(
          replaceBody,
          "{WORK_ORDER_ID}",
          actual_work_order_id
        );

        // add notification alert history
        const addAlertAPI = await userService.addAlerts({
          headers: request?.headers,
          sender_user_id: loggedInUser.id,
          title,
          body: replaceBody,
          action: CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_ASSIGNED,
          receiver_and_send_to: [
            {
              receiver_user_id: assigned_to_user_id,
              send_to: assignedToUserData?.device_token,
              loginData: assignedToUserData?.loginHistory,
            },
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        let notificationResponse;
        if (assignedToUserData?.loginHistory) {
          notificationResponse = await sendNotification(
            [assignedToUserData?.device_token],
            title,
            replaceBody
          );
          // update alert based on notification response
          if (notificationResponse[0].success) {
            const updateAlertAPI = await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 1,
              response: notificationResponse[0].message,
            });
          } else {
            const updateAlertAPI = await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 0,
              response: notificationResponse[0].message.toString(),
            });
          }
        }
      }

      let message, woType;
      if ([qcWoType.id, handoverWoType.id].includes(work_order_type_id)) {
        if (work_order_type_id === qcWoType.id) {
          woType = CONSTANTS.WORK_ORDER.TYPE.SURVEY;
        } else if (work_order_type_id === handoverWoType.id) {
          woType = CONSTANTS.WORK_ORDER.TYPE.QUALITY_CHECK;
        }

        message = `Tasks are not created for below scopes due to ${woType} not completed.`;
      }

      return response.handler.success(
        MESSAGE.WORK_ORDER.ADD.SUCCESS,
        warningScopesArr.length
          ? { warning: { message: message, data: warningScopesArr } }
          : {}
      );
    } else {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.ADD.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const verifyIfScopeHaveImageAndTouchPoints = async (
  unique_requested_scope_ids
) => {
  // check the scope image & touch point by source of scope.
  const checkScopes = await db.Scopes.findAll({
    where: {
      id: { [Op.in]: unique_requested_scope_ids || [] },
      source: CONSTANTS.DATA_SOURCE.ZOHO,
    },
    attributes: ATTRIBUTES.SCOPES,
  });

  let scopeNameModelArr = [];
  if (checkScopes.length) {
    // if scope's source is ZOHO, then check each scope with Default model, if default model scope has image & touch point then allow to create work order.
    for (let i = 0; i < checkScopes.length; i++) {
      const scopeWithDefaultModel = await common.getScopeByModelAndName(
        CONSTANTS.SCOPE.DEFAULT_MODEL,
        checkScopes[i].name
      );

      if (
        !scopeWithDefaultModel ||
        scopeWithDefaultModel?.source === CONSTANTS.DATA_SOURCE.ZOHO
      ) {
        scopeNameModelArr.push(
          `${checkScopes[i].name} ${checkScopes[i].model}`
        );
      }
    }
  }
  return scopeNameModelArr;
};

const verifyQueMappingForWoTypeAndScope = async (
  work_order_type_id,
  unique_requested_scope_ids
) => {
  let scopesWithoutQueMapping = [];

  for (const scopeId of unique_requested_scope_ids) {
    const scope = await common.getScopeById(scopeId);
    if (scope) {
      const { name, model } = scope;

      const hasMapping = await common.getQuestionMappingByWoTypeIdScopeId(
        work_order_type_id,
        scopeId
      );
      /*
                if questionMapping not available with requested scope & workOrderType,
                Then we need to find scope with requested scope's name & model = Default.
                Then with that scope & workOrderType we will check the QuestionMapping
            */
      if (!hasMapping) {
        const scopeWithDefaultModel = await common.getScopeByModelAndName(
          CONSTANTS.SCOPE.DEFAULT_MODEL,
          name
        );

        if (scopeWithDefaultModel) {
          const hasMappingWithDefaultModel =
            await common.getQuestionMappingByWoTypeIdScopeId(
              work_order_type_id,
              scopeWithDefaultModel.id
            );

          if (!hasMappingWithDefaultModel) {
            scopesWithoutQueMapping.push(`${name} ${model}`);
          }
        } else {
          scopesWithoutQueMapping.push(`${name} ${model}`);
        }
      }
    }
  }

  return scopesWithoutQueMapping.length > 0
    ? scopesWithoutQueMapping.join(", ")
    : null;
};

const verifyQualityCheckOrHandoverCreation = async (
  orderExist,
  work_order_type_id,
  surveyWoType,
  qcWoType,
  handoverWoType,
  completedWoStatus,
  space_scopes
) => {
  /*
        - If fabricactor creating QC and the order's zoho_stage is one of Installation Ongoing/QC In Progress/QC Approval, then we need to allow to create QC without survey.
    */

  if (
    work_order_type_id === qcWoType.id &&
    [
      CONSTANTS.ZOHO.ORDER_STAGE.INSTALLATION_ONGOING.TITLE,
      CONSTANTS.ZOHO.ORDER_STAGE.QC_IN_PROGRESS.TITLE,
      CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.TITLE,
    ].includes(orderExist.zoho_stage)
  ) {
    return null;
  }

  // SURVEY -> QUALITY CHECK (QC) -> HANDOVER
  let data = {};

  if (work_order_type_id === qcWoType.id) {
    // child = QC, parent = Survey
    data.woType = {
      child: qcWoType,
      parent: surveyWoType,
    };
  } else if (work_order_type_id === handoverWoType.id) {
    // child = Handover, parent = QC
    data.woType = {
      child: handoverWoType,
      parent: qcWoType,
    };
  }

  // get all work orders with parent work order type
  let whereCond = [
    { order_id: orderExist.id },
    { work_order_type_id: data.woType.parent.id },
    { is_active: 1 },
    { is_deleted: 0 },
  ];

  const parent_work_orders = await db.WorkOrders.findAll({
    where: whereCond,
    attributes: ATTRIBUTES.WORK_ORDERS.filter(
      (value) => value !== "selfie_image_base64"
    ),
  });

  if (!parent_work_orders.length) {
    return {
      error: MESSAGE.WORK_ORDER.ADD.FAILED.PARENT_WO_NOT_AVAILABLE(
        data.woType.parent.title
      ),
    };
  }

  const completed_parent_work_orders = parent_work_orders.filter(
    (pwo) => pwo.work_order_status_id === completedWoStatus.id
  );

  if (!completed_parent_work_orders.length) {
    return {
      error: MESSAGE.WORK_ORDER.ADD.FAILED.PARENT_WO_NOT_COMPLETED(
        data.woType.parent.title
      ),
    };
  }

  // get completed tasks of completed survey work orders.
  const wo_ids = completed_parent_work_orders.map((ele) => ele.id);
  const tasks = await db.Tasks.findAll({
    where: {
      work_order_id: { [Op.in]: wo_ids },
      status: CONSTANTS.TASK_STATUS.COMPLETED,
      is_submitted: 1,
      is_active: 1,
      is_deleted: 0,
    },
    attributes: ATTRIBUTES.TASKS,
    include: [
      {
        model: db.WorkOrderScopes,
        as: "work_order_task_scope",
        required: true,
        attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
        include: [
          {
            model: db.WorkOrderSpaces,
            as: "work_order_task_space",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
          },
        ],
      },
    ],
  });

  if (!tasks.length) {
    return {
      error: MESSAGE.WORK_ORDER.ADD.FAILED.PARENT_WO_TASK_NOT_COMPLETED(
        data.woType.parent.title
      ),
    };
  }

  // verify which scope's survey tasks are completed.
  for (let i = 0; i < space_scopes.length; i++) {
    const space_id = space_scopes[i].space_id;
    const scopes = space_scopes[i].scopes;

    for (let j = 0; j < scopes.length; j++) {
      let completed_parent_task_count = 0;
      tasks.forEach((task) => {
        if (
          task.work_order_task_scope.scope_id === scopes[j].scope_id &&
          task.work_order_task_scope.work_order_task_space.space_id === space_id
        ) {
          completed_parent_task_count++;
        }
      });

      space_scopes[i].scopes[j].completed_parent_task_count =
        completed_parent_task_count;
    }
  }
};

const getFormattedScheduleStartDateTime = async (
  scheduled_start_date,
  scheduled_start_time
) => {
  /*
        convert start date & time to utc.
        1. merge date & time
        2. convert to utc
        3. seprate date & time & store in db.
    */

  const istDatetime = new Date(
    `${scheduled_start_date} ${scheduled_start_time}`
  );
  const utcDatetime = new Date(istDatetime);
  const utcDate = utcDatetime.toISOString().split("T")[0];

  const utcTime = utcDatetime.toISOString().split("T")[1].split("Z")[0];
  // Parse the time string into a Date object
  const timeParts = utcTime.split(":");
  const hours = parseInt(timeParts[0], 10);
  const minutes = parseInt(timeParts[1], 10);
  const seconds = parseFloat(timeParts[2]);
  const dateObj = new Date();
  dateObj.setHours(hours);
  dateObj.setMinutes(minutes);
  dateObj.setSeconds(seconds);
  const ampmTime = dateObj.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  return {
    date: utcDate,
    time: ampmTime,
  };
};

const get = async (request, response) => {
  try {
    const {
      order_id,
      work_order_id,
      work_order_status_id,
      assigned_to_user_id,
      search,
      order_by = "id",
      sort = "DESC",
    } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    //check if requested work order status is exist or not.
    if (work_order_status_id) {
      const woStatusExist = await common.getWorkOrderStatusById(
        work_order_status_id
      );
      if (!woStatusExist) {
        return response.handler.notFound(MESSAGE.WORK_ORDER.STATUS.NOT_EXIST);
      }
    }

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];

    order_id && whereCond.push({ order_id });
    work_order_id && whereCond.push({ id: work_order_id });
    work_order_status_id && whereCond.push({ work_order_status_id });
    assigned_to_user_id && whereCond.push({ assigned_to_user_id });

    if (search) {
      const searchArr = [
        { "$WorkOrders.actual_work_order_id$": { [Op.like]: `%${search}%` } },
        { "$work_order_type.title$": { [Op.like]: `%${search}%` } },
        { "$work_order_status.title$": { [Op.like]: `%${search}%` } },
      ];

      whereCond.push({ [Op.or]: searchArr });
    }

    let { count: totalCount, rows: workOrderData } =
      await db.WorkOrders.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        distinct: true,
        order: [
          [order_by, sort],
          [
            { model: db.WorkOrderReviewer, as: "work_order_reviewer" },
            "id",
            "DESC",
          ],
        ],
        include: [
          {
            model: db.WorkOrderStatus,
            as: "work_order_status",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          },
          {
            model: db.WorkOrderTypes,
            as: "work_order_type",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
          {
            model: db.WorkOrderSpaces,
            as: "work_order_spaces",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
              {
                model: db.WorkOrderScopes,
                as: "work_order_scopes",
                required: false,
                attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.SCOPES,
                      [
                        db.sequelize.fn(
                          "CONCAT",
                          db.sequelize.col("height"),
                          " ⨯ ",
                          db.sequelize.col("width")
                        ),
                        "dimension",
                      ],
                    ],
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                        include: [
                          {
                            model: db.ScopeImageTouchPoints,
                            as: "touch_points",
                            required: false,
                            attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.WorkOrderReview,
            as: "work_order_review",
            required: false,
            order: [["id", "DESC"]],
            attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
          },
          {
            model: db.WorkOrderReviewer,
            as: "work_order_reviewer",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_REVIEWER,
          },
          {
            model: db.Orders,
            as: "order_data",
            required: false,
            attributes: ATTRIBUTES.ORDERS,
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: ATTRIBUTES.CUSTOMERS,
              },
            ],
          },
          {
            model: db.Warranty,
            as: "warranty_data",
            required: false,
            attributes: ATTRIBUTES.WARRANTY,
          },
        ],
      });

    workOrderData = workOrderData.map((workOrder) => {
      // workOrder.work_order_review = workOrder.work_order_review?.sort(
      //   (a, b) => b.id - a.id
      // );

      if (workOrder?.order_data?.amount < CONSTANTS.ORDER_AMOUNT) {
        workOrder.work_order_review = workOrder.work_order_review.slice(0, 1);
      }

      return workOrder;
    });

    if (!workOrderData) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.FOUND.ERROR);
    } else if (totalCount === 0 || workOrderData.length === 0) {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    } else {
      const assigned_to_user_ids = await workOrderData.map(
        (wod1) => wod1.assigned_to_user_id
      );
      const assigned_by_user_ids = await workOrderData.map(
        (wod2) => wod2.assigned_by_user_id
      );

      const internal_team_ids = await workOrderData.map(
        (wod3) => wod3?.internal_team_id
      );

      let reviewer_user_id = [];
      let se_sh_user_id = [];

      await Promise.all(
        workOrderData.map(async (wod3) => {
          // Check if work_order_review is an array
          const review_id = Array.isArray(wod3?.work_order_review)
            ? wod3.work_order_review
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          reviewer_user_id.push(...review_id);
        })
      );

      await Promise.all(
        workOrderData?.map(async (wod4) => {
          // Check if work_order_review is an array
          const reviewer_id = Array.isArray(wod4?.work_order_reviewer)
            ? wod4.work_order_reviewer
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          se_sh_user_id.push(...reviewer_id);
        })
      );

      if (reviewer_user_id?.length) {
        reviewer_user_id = [...new Set(reviewer_user_id)];
      }

      if (se_sh_user_id?.length) {
        se_sh_user_id = [...new Set(se_sh_user_id)];
      }

      const user_ids = [
        ...assigned_to_user_ids,
        ...assigned_by_user_ids,
        ...reviewer_user_id,
        ...internal_team_ids,
        ...se_sh_user_id,
      ];

      // get user from user authentication service.
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids,
        headers: request?.headers,
      });

      const userData = userAPI || [];

      let respData = [];
      for (let i = 0; i < workOrderData.length; i++) {
        const assigned_to_user =
          (await userData.find(
            (ele) => ele.id === workOrderData[i].assigned_to_user_id
          )) || {};
        const assigned_by_user =
          (await userData.find(
            (ele2) => ele2.id === workOrderData[i].assigned_by_user_id
          )) || {};

        const internal_team_user =
          (await userData.find(
            (ele2) => ele2.id === workOrderData[i].internal_team_id
          )) || {};

        let responseData = {
          ...JSON.parse(JSON.stringify(workOrderData[i])),
          assigned_to_user,
          assigned_by_user,
          internal_team_user,
        };

        if (workOrderData[i]?.work_order_review?.length) {
          const workOrderReviewData = [...workOrderData[i]?.work_order_review];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_review: resWorkOrderReview,
          };
        }

        if (workOrderData[i]?.work_order_reviewer?.length) {
          const workOrderReviewData = [
            ...workOrderData[i]?.work_order_reviewer,
          ];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_reviewer: resWorkOrderReview,
          };
        }

        const respWorkOrderSpaceData = await common.getScopeDetails(
          responseData?.order_data?.zoho_order_id,
          responseData
        );

        responseData = {
          ...responseData,
          work_order_spaces: respWorkOrderSpaceData,
        };

        respData.push(responseData);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: workOrderData.length,
        work_orders: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const update = async (request, response) => {
  try {
    const {
      work_order_id,
      assigned_to_user_id,
      assigned_to_user_role_id,
      scheduled_start_date,
      scheduled_start_time,
      scheduled_duration,
      instructions,
    } = request.body;

    const loggedInUser = await request?.login_user?.data;
    const assignedByUser = loggedInUser;

    // check if work order is exist or not
    const wo_exist = await common.getWorkOrderById(work_order_id);

    if (!wo_exist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    } else if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.PENDING,
        CONSTANTS.WORK_ORDER.STATUS.OVERDUE,
        CONSTANTS.WORK_ORDER.STATUS.REJECTED,
      ].includes(wo_exist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.UPDATE.ERROR_DUE_TO_STARTED
      );
    }

    let workOrderStatusId = wo_exist?.work_order_status_id;

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
    const day = currentDate.getDate().toString().padStart(2, "0");
    if (
      wo_exist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.OVERDUE &&
      `${month}/${day}/${year}` < scheduled_start_date
    ) {
      const pendingWorkOrderStatusId = await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.PENDING
      );
      workOrderStatusId = pendingWorkOrderStatusId?.id;
    }

    const updateWorkOrder = await db.WorkOrders.update(
      {
        assigned_to_user_id: assigned_to_user_id,
        assigned_to_user_role_id: assigned_to_user_role_id,
        assigned_by_user_id: assignedByUser?.id,
        assigned_by_user_role_id: assignedByUser?.role?.id,
        scheduled_start_date:
          scheduled_start_date && new Date(scheduled_start_date),
        scheduled_start_time: scheduled_start_time,
        scheduled_duration: scheduled_duration,
        instructions: instructions,
        work_order_status_id: workOrderStatusId,
      },
      { where: { id: work_order_id } }
    );

    /*
            notify the supervisor regarding the work order update.
            - if supervisor not changed, then he will get notification regarding data updated in work order.
            - if supervisor changed, then
                - previous supervisor get notification that he is removed from the work order.
                - new supervisor get notification that he has been assigned to work order.
        */

    const isAssignedToUserSame =
      wo_exist.assigned_to_user_id === assigned_to_user_id;
    let userIds = [assignedByUser?.id];
    if (isAssignedToUserSame) {
      userIds.push(wo_exist.assigned_to_user_id);
    } else {
      userIds.push(wo_exist.assigned_to_user_id, assigned_to_user_id);
    }

    let assignedByUserData, assignedToUserDataPrev, assignedToUserDataNew;
    const userAPI = await userService.getUserDetailsByUserIds({
      user_ids: userIds,
      headers: request?.headers,
    });

    const findUserById = (userId) =>
      userAPI?.find((data) => data.id === userId);

    if (userAPI) {
      assignedByUserData = await findUserById(assignedByUser?.id);
      assignedToUserDataPrev = await findUserById(wo_exist.assigned_to_user_id);
      assignedToUserDataNew = await findUserById(assigned_to_user_id);
    }

    let notificationAction1, notificationAction2, deviceToken1, deviceToken2;
    const assignedByUserName = `${assignedByUserData?.first_name} ${assignedByUserData?.last_name}`;
    const actualWoId = wo_exist.actual_work_order_id;

    if (isAssignedToUserSame) {
      notificationAction1 =
        CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_UPDATED;
      deviceToken1 = assignedToUserDataPrev?.device_token;
    } else {
      notificationAction1 =
        CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_UNASSIGNED;
      deviceToken1 = assignedToUserDataPrev?.device_token;

      if (assignedToUserDataNew?.id) {
        notificationAction2 =
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_ASSIGNED;
        deviceToken2 = assignedToUserDataNew?.device_token;
      }
    }

    // send notifications
    const processNotification = async (
      notificationAction,
      deviceToken,
      loginData
    ) => {
      // get notification template from user service.
      const templateAPI = await userService.getTemplate({
        headers: request?.headers,
        action: [notificationAction],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        let replaceBody = await replaceData(
          body,
          "{ASSIGNED_BY_USER}",
          assignedByUserName
        );
        replaceBody = await replaceData(
          replaceBody,
          "{WORK_ORDER_ID}",
          actualWoId
        );

        // add notification alert history
        const addAlertAPI = await userService.addAlerts({
          headers: request?.headers,
          sender_user_id: assignedByUser?.id,
          title,
          body: replaceBody,
          action: notificationAction,
          receiver_and_send_to: [
            {
              receiver_user_id: assigned_to_user_id,
              send_to: deviceToken,
              loginData,
            },
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        const notificationResponse = await sendNotification(
          [deviceToken],
          title,
          replaceBody
        );

        // update alert based on notification response
        if (notificationResponse[0].success) {
          const updateAlertAPI = await userService.updateAlert({
            headers: request?.headers,
            id: addAlertAPI.inserted_data[0].id,
            is_sent: 1,
            response: notificationResponse[0].message,
          });
        } else {
          const updateAlertAPI = await userService.updateAlert({
            headers: request?.headers,
            id: addAlertAPI.inserted_data[0].id,
            is_sent: 0,
            response: notificationResponse[0].message.toString(),
          });
        }
      }
    };

    await processNotification(
      notificationAction1,
      deviceToken1,
      assignedToUserDataPrev?.loginHistory
    );

    if (notificationAction2) {
      await processNotification(
        notificationAction2,
        deviceToken2,
        assignedToUserDataNew?.loginHistory
      );
    }

    if (updateWorkOrder) {
      // if updating rejected work order, then we need to change status to pending. and all the answers we need to mark as inactive.
      if (
        wo_exist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.REJECTED
      ) {
        const pendingStatus = await common.getWorkOrderStatusByTitle(
          CONSTANTS.WORK_ORDER.STATUS.PENDING
        );
        await db.WorkOrders.update(
          { work_order_status_id: pendingStatus?.id },
          { where: { id: work_order_id } }
        );

        // get all task associated with work order.
        const tasks = await db.Tasks.findAll({
          where: {
            work_order_id: work_order_id,
            is_active: 1,
            is_deleted: 0,
          },
          attributes: ATTRIBUTES.TASKS,
          raw: true,
        });

        if (tasks.length) {
          // update is_submitted = 0 & status = Pending in tasks
          const taskIds = await tasks.map((data) => data.id);
          await db.Tasks.update(
            { status: CONSTANTS.TASK_STATUS.PENDING, is_submitted: 0 },
            { where: { id: { [Op.in]: taskIds } } }
          );

          // get task touch point ids based on task
          const taskTouchPoint = await db.TaskTouchPoints.findAll({
            where: { task_id: { [Op.in]: taskIds } },
            attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
            raw: true,
          });

          if (taskTouchPoint.length) {
            const taskTouchPointIds = await taskTouchPoint.map((ttp) => ttp.id);

            // get question ids based on task touch point
            const taskQuestions = await db.TaskQuestions.findAll({
              where: { id: { [Op.in]: taskTouchPointIds } },
              attributes: ATTRIBUTES.TASK_QUESTIONS,
              raw: true,
            });

            if (taskQuestions.length) {
              const taskQuestionsIds = taskQuestions?.map((tq) => tq.id);

              // update answer as inactive & deleted with task question ids.
              await db.TaskAnswers.update(
                { is_active: 0, is_deleted: 1 },
                { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
              );

              await db.TaskImageAnswers.update(
                { is_active: 0, is_deleted: 1 },
                { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
              );

              await db.TaskVideoAnswers.update(
                { is_active: 0, is_deleted: 1 },
                { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
              );
            }
          }
        }
      }

      return response.handler.success(MESSAGE.WORK_ORDER.UPDATE.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.UPDATE.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getAssignedWorkOrders = async (request, response) => {
  try {
    const { id, work_order_status_id } = request.query;
    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    const workOrderStatusIds = work_order_status_id
      ? [...new Set(JSON.parse(work_order_status_id))]
      : [];

    //check if requested work order status is exist or not.
    if (workOrderStatusIds?.length) {
      const woStatusExist = await await db.WorkOrderStatus.findAll({
        where: {
          id: { [Op.in]: workOrderStatusIds },
        },
        attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      });
      if (woStatusExist?.length !== workOrderStatusIds?.length) {
        return response.handler.notFound(MESSAGE.WORK_ORDER.STATUS.NOT_EXIST);
      }
    }

    const loggedInUser = await request?.login_user?.data;

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];

    loggedInUser?.id &&
      whereCond.push({ assigned_to_user_id: loggedInUser?.id });
    id && whereCond.push({ id });
    workOrderStatusIds?.length &&
      whereCond.push({ work_order_status_id: { [Op.in]: workOrderStatusIds } });

    const { count: totalCount, rows: workOrderData } =
      await db.WorkOrders.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        distinct: true,
        order: [
          ["id", "DESC"],
          [
            { model: db.WorkOrderReviewer, as: "work_order_reviewer" },
            "id",
            "DESC",
          ],
        ],
        include: [
          {
            model: db.WorkOrderStatus,
            as: "work_order_status",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          },
          {
            model: db.WorkOrderTypes,
            as: "work_order_type",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
          {
            model: db.WorkOrderSpaces,
            as: "work_order_spaces",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
              {
                model: db.WorkOrderScopes,
                as: "work_order_scopes",
                required: false,
                attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.SCOPES,
                      [
                        db.sequelize.fn(
                          "CONCAT",
                          db.sequelize.col("height"),
                          " ⨯ ",
                          db.sequelize.col("width")
                        ),
                        "dimension",
                      ],
                    ],
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                        include: [
                          {
                            model: db.ScopeImageTouchPoints,
                            as: "touch_points",
                            required: false,
                            attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.WorkOrderReview,
            as: "work_order_review",
            required: false,
            order: [["id", "DESC"]],
            attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
          },
          {
            model: db.Orders,
            as: "order_data",
            required: false,
            attributes: ATTRIBUTES.ORDERS,
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: ATTRIBUTES.CUSTOMERS,
              },
            ],
          },
          {
            model: db.Warranty,
            as: "warranty_data",
            required: false,
            attributes: ATTRIBUTES.WARRANTY,
          },
          {
            model: db.WorkOrderReviewer,
            as: "work_order_reviewer",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_REVIEWER,
          },
        ],
      });

    if (!workOrderData) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.FOUND.ERROR);
    } else if (totalCount === 0 || workOrderData.length === 0) {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    } else {
      const assigned_to_user_ids = await workOrderData.map(
        (wod1) => wod1.assigned_to_user_id
      );
      const assigned_by_user_ids = await workOrderData.map(
        (wod2) => wod2.assigned_by_user_id
      );

      const internal_team_user_ids = await workOrderData.map(
        (wod3) => wod3.internal_team_id
      );

      let reviewer_user_id = [];
      let se_sh_user_id = [];

      await Promise.all(
        workOrderData.map(async (wod3) => {
          // Check if work_order_review is an array
          const review_id = Array.isArray(wod3?.work_order_review)
            ? wod3.work_order_review
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          reviewer_user_id.push(...review_id);
        })
      );

      await Promise.all(
        workOrderData?.map(async (wod4) => {
          // Check if work_order_review is an array
          const reviewer_id = Array.isArray(wod4?.work_order_reviewer)
            ? wod4.work_order_reviewer
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          se_sh_user_id.push(...reviewer_id);
        })
      );

      if (reviewer_user_id?.length) {
        reviewer_user_id = [...new Set(reviewer_user_id)];
      }

      if (se_sh_user_id?.length) {
        se_sh_user_id = [...new Set(se_sh_user_id)];
      }

      const user_ids = [
        ...assigned_to_user_ids,
        ...assigned_by_user_ids,
        ...internal_team_user_ids,
        ...reviewer_user_id,
      ];
      // get user from user authentication service.
      let userData = [];
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: user_ids,
        headers: request?.headers,
      });

      if (userAPI) {
        userData = userAPI;
      }

      let respData = [];
      for (let i = 0; i < workOrderData.length; i++) {
        let assigned_to_user = {};
        let assigned_by_user = {};
        let internal_team_user = {};
        let responseData = {};

        if (userData.length) {
          assigned_to_user = await userData.find(
            (ele) => ele.id === workOrderData[i].assigned_to_user_id
          );

          assigned_by_user = await userData.find(
            (ele2) => ele2.id === workOrderData[i].assigned_by_user_id
          );

          internal_team_user = await userData.find(
            (ele3) => ele3.id === workOrderData[i].internal_team_id
          );
        }

        responseData = {
          ...JSON.parse(JSON.stringify(workOrderData[i])),
          assigned_to_user: assigned_to_user,
          assigned_by_user: assigned_by_user,
          internal_team_user: internal_team_user || {},
        };

        if (workOrderData[i]?.work_order_review?.length) {
          const workOrderReviewData = [...workOrderData[i]?.work_order_review];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_review: resWorkOrderReview,
          };
        }

        if (workOrderData[i]?.work_order_reviewer?.length) {
          const workOrderReviewData = [
            ...workOrderData[i]?.work_order_reviewer,
          ];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_reviewer: resWorkOrderReview,
          };
        }

        const respWorkOrderSpaceData = await common.getScopeDetails(
          responseData?.order_data?.zoho_order_id,
          responseData
        );

        responseData = {
          ...responseData,
          work_order_spaces: respWorkOrderSpaceData,
        };

        respData.push(responseData);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: workOrderData.length,
        work_orders: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getAssignedWorkOrdersWithoutPagination = async (request, response) => {
  try {
    const { id, work_order_status_id } = request.query;

    const workOrderStatusIds = work_order_status_id
      ? [...new Set(JSON.parse(work_order_status_id))]
      : [];

    //check if requested work order status is exist or not.
    if (workOrderStatusIds?.length) {
      const woStatusExist = await await db.WorkOrderStatus.findAll({
        where: {
          id: { [Op.in]: workOrderStatusIds },
        },
        attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      });
      if (woStatusExist?.length !== workOrderStatusIds?.length) {
        return response.handler.notFound(MESSAGE.WORK_ORDER.STATUS.NOT_EXIST);
      }
    }

    const loggedInUser = await request?.login_user?.data;

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];

    loggedInUser?.id &&
      whereCond.push({ assigned_to_user_id: loggedInUser?.id });
    id && whereCond.push({ id });
    workOrderStatusIds?.length &&
      whereCond.push({ work_order_status_id: { [Op.in]: workOrderStatusIds } });

    const { count: totalCount, rows: workOrderData } =
      await db.WorkOrders.findAndCountAll({
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        distinct: true,
        order: [
          ["id", "DESC"],
          [
            { model: db.WorkOrderReviewer, as: "work_order_reviewer" },
            "id",
            "DESC",
          ],
        ],
        include: [
          {
            model: db.WorkOrderStatus,
            as: "work_order_status",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          },
          {
            model: db.WorkOrderTypes,
            as: "work_order_type",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
          {
            model: db.WorkOrderSpaces,
            as: "work_order_spaces",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
              {
                model: db.WorkOrderScopes,
                as: "work_order_scopes",
                required: false,
                attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.SCOPES,
                      [
                        db.sequelize.fn(
                          "CONCAT",
                          db.sequelize.col("height"),
                          " ⨯ ",
                          db.sequelize.col("width")
                        ),
                        "dimension",
                      ],
                    ],
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                        include: [
                          {
                            model: db.ScopeImageTouchPoints,
                            as: "touch_points",
                            required: false,
                            attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.WorkOrderReview,
            as: "work_order_review",
            required: false,
            order: [["id", "DESC"]],
            attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
          },
          {
            model: db.Orders,
            as: "order_data",
            required: false,
            attributes: ATTRIBUTES.ORDERS,
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: ATTRIBUTES.CUSTOMERS,
              },
            ],
          },
          {
            model: db.Warranty,
            as: "warranty_data",
            required: false,
            attributes: ATTRIBUTES.WARRANTY,
          },
          {
            model: db.WorkOrderReviewer,
            as: "work_order_reviewer",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_REVIEWER,
          },
        ],
      });

    if (!workOrderData) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.FOUND.ERROR);
    } else if (totalCount === 0 || workOrderData.length === 0) {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    } else {
      const assigned_to_user_ids = await workOrderData.map(
        (wod1) => wod1.assigned_to_user_id
      );
      const assigned_by_user_ids = await workOrderData.map(
        (wod2) => wod2.assigned_by_user_id
      );

      const internal_team_user_ids = await workOrderData.map(
        (wod3) => wod3.internal_team_id
      );

      let reviewer_user_id = [];
      let se_sh_user_id = [];

      await Promise.all(
        workOrderData.map(async (wod3) => {
          // Check if work_order_review is an array
          const review_id = Array.isArray(wod3?.work_order_review)
            ? wod3.work_order_review
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          reviewer_user_id.push(...review_id);
        })
      );

      await Promise.all(
        workOrderData?.map(async (wod4) => {
          // Check if work_order_review is an array
          const reviewer_id = Array.isArray(wod4?.work_order_reviewer)
            ? wod4.work_order_reviewer
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          se_sh_user_id.push(...reviewer_id);
        })
      );

      if (reviewer_user_id?.length) {
        reviewer_user_id = [...new Set(reviewer_user_id)];
      }

      if (se_sh_user_id?.length) {
        se_sh_user_id = [...new Set(se_sh_user_id)];
      }

      const user_ids = [
        ...assigned_to_user_ids,
        ...assigned_by_user_ids,
        ...internal_team_user_ids,
        ...reviewer_user_id,
        ...se_sh_user_id,
      ];
      // get user from user authentication service.
      let userData = [];
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: user_ids,
        headers: request?.headers,
      });

      if (userAPI) {
        userData = userAPI;
      }

      let respData = [];
      for (let i = 0; i < workOrderData.length; i++) {
        let assigned_to_user = {};
        let assigned_by_user = {};
        let internal_team_user = {};
        let responseData = {};

        if (userData.length) {
          assigned_to_user = await userData.find(
            (ele) => ele.id === workOrderData[i].assigned_to_user_id
          );

          assigned_by_user = await userData.find(
            (ele2) => ele2.id === workOrderData[i].assigned_by_user_id
          );

          internal_team_user = await userData.find(
            (ele3) => ele3.id === workOrderData[i].internal_team_id
          );
        }

        responseData = {
          ...JSON.parse(JSON.stringify(workOrderData[i])),
          assigned_to_user: assigned_to_user,
          assigned_by_user: assigned_by_user,
          internal_team_user: internal_team_user || {},
        };

        if (workOrderData[i]?.work_order_review?.length) {
          const workOrderReviewData = [...workOrderData[i]?.work_order_review];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_review: resWorkOrderReview,
          };
        }

        if (workOrderData[i]?.work_order_reviewer?.length) {
          const workOrderReviewData = [
            ...workOrderData[i]?.work_order_reviewer,
          ];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_reviewer: resWorkOrderReview,
          };
        }

        const respWorkOrderSpaceData = await common.getScopeDetails(
          responseData?.order_data?.zoho_order_id,
          responseData
        );

        responseData = {
          ...responseData,
          work_order_spaces: respWorkOrderSpaceData,
        };

        respData.push(responseData);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: workOrderData.length,
        work_orders: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const checkIn = async (request, response) => {
  try {
    const {
      work_order_id,
      internal_team_id = null,
      date = null,
      start_time = null,
    } = request.body;
    const loggedInUser = await request?.login_user?.data;
    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    // only pending work orders can be checked in.
    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.PENDING,
        CONSTANTS.WORK_ORDER.STATUS.REJECTED,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECKED_IN.FAILED);
    }

    // upload selfie
    if (request.file) {
      // upload img to s3
      let Key, Location, metadata, mimetype;
      if (request.file) {
        mimetype = request.file?.mimetype;
        const path = request.file?.path;
        const blob = fs.readFileSync(path); //convert file to blob
        const fileName = request.file?.originalname;
        const uploadSelfieImg = await aws.uploadFileS3(
          fileName,
          blob,
          `${CONSTANTS.S3_BUCKET.FOLDERS.WORK_ORDER.BASE}/${CONSTANTS.S3_BUCKET.FOLDERS.WORK_ORDER.SELFIE}`,
          metadata,
          mimetype
        );

        if (uploadSelfieImg) {
          Key = uploadSelfieImg.Key;
          Location = uploadSelfieImg.Location;
        }
      }
      // const base64 = await aws.convertS3UrlToBase64(Key);

      if (Key && Location) {
        await db.WorkOrders.update(
          {
            selfie_image_key: Key,
            selfie_image_url: Location,
            // selfie_image_base64: base64,
          },
          { where: { id: work_order_id } }
        );
      }
    } else {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.CHECKED_IN.UPLOAD_SELFIE.EMPTY
      );
    }

    // while cheking in the work order, status will be updated as in progress and actual date & time need to update.
    const woStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS
    );

    const currentDateUTC = new Date();
    const time = currentDateUTC.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

    let payload = {
      actual_start_date: date || currentDateUTC,
      internal_team_id,
      actual_start_time: start_time?.toUpperCase() || time.toUpperCase(),
      internal_team_approval_status: false,
    };
    if (!internal_team_id) {
      payload = {
        ...payload,
        work_order_status_id: woStatus?.id,
      };
    }

    const updateWO = await db.WorkOrders.update(payload, {
      where: { id: work_order_id },
    });

    if (updateWO) {
      if (!internal_team_id) {
        // update order status to ongoing as, order's work order is started.
        const orderStatus = await common.getOrderStatusByTitle(
          CONSTANTS.ORDER_STATUS.ONGOING
        );
        if (
          woExist?.wo_order?.order_status?.title === CONSTANTS.ORDER_STATUS.NEW
        ) {
          await db.Orders.update(
            { order_status_id: orderStatus?.id },
            { where: { id: woExist.wo_order.id } }
          );
        }
      } else {
        //send notification to internal team regarding request.
        const userAPI = await userService.getUserDetailsByUserIds({
          user_ids: [internal_team_id, loggedInUser?.id],
          headers: request?.headers,
        });

        // get notification template from user service.
        const templateAPI = await userService.getTemplate({
          headers: request?.headers,
          action: [
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SUPERVISOR_TAG_REQUEST,
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        let loggedinUserData, internalTeamUserData;
        if (userAPI) {
          loggedinUserData = await userAPI.find(
            (data) => data.id == loggedInUser?.id
          );
          internalTeamUserData = await userAPI.find(
            (data) => data.id == internal_team_id
          );
        }

        if (templateAPI) {
          const { title, body } = templateAPI[0];
          let replaceBody = await replaceData(
            body,
            "{USER_NAME}",
            `${loggedinUserData?.first_name} ${loggedinUserData?.last_name}`
          );
          replaceBody = await replaceData(
            replaceBody,
            "{WORK_ORDER_ID}",
            woExist?.actual_work_order_id
          );

          // add notification alert history
          const addAlertAPI = await userService.addAlerts({
            headers: request?.headers,
            sender_user_id: loggedInUser.id,
            title,
            body: replaceBody,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SUPERVISOR_TAG_REQUEST,
            receiver_and_send_to: [
              {
                receiver_user_id: internal_team_id,
                send_to: internalTeamUserData?.device_token,
                loginData: internalTeamUserData?.loginHistory,
                info: work_order_id,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (internalTeamUserData?.loginHistory) {
            notificationResponse = await sendNotification(
              [internalTeamUserData?.device_token],
              title,
              replaceBody
            );
            // update alert based on notification response
            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        }

        return response.handler.success(
          MESSAGE.WORK_ORDER.CHECKED_IN.REQUEST_SEND_TO_SE_SH
        );
      }

      return response.handler.success(MESSAGE.WORK_ORDER.CHECKED_IN.SUCCESS);
    } else {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECKED_IN.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const checkOut = async (request, response) => {
  try {
    const { work_order_id } = request.body;
    const loggedInUser = await request?.login_user?.data;

    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    /*
            // check if otp verified or not. if otp not verified. then we will not allow to checkout.
            const verifiedOtp = await db.Otps.findOne({
                where: [
                    { work_order_id: work_order_id },
                    { is_otp_verified: 1 },
                    { action: CONSTANTS.OTP.ACTION.SUBMIT_WORK_ORDER },
                ],
                order: [["id", "DESC"]],
                limit: 1,
                raw: true
            });

            if (!verifiedOtp) {
                return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECK_OUT.FAILED_1);
            }
        */

    /*
            - old logic: while checkout the work order we need to verify the associated tasks to work order. That should be completed or it can not be checked out.
            - new logic: we need to allow check out for partially completed work order. if all task of work order is not completed then also we will allow to checkout. (But min 1 task should be completed.)
        */
    const amount = woExist?.wo_order?.amount;
    let status = CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL;
    const totalWOTaskCount = await db.Tasks.count({
      where: {
        work_order_id: work_order_id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    const completedWOTaskCount = await db.Tasks.count({
      where: {
        work_order_id: work_order_id,
        status: CONSTANTS.TASK_STATUS.COMPLETED,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (totalWOTaskCount !== completedWOTaskCount) {
      status = CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED;
      // return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECK_OUT.FAILED);
    }

    if (completedWOTaskCount < 1) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECK_OUT.FAILED_3);
    }

    // if work order type is survey, then we will change the status & will send pdf to zoho for customer approval. survey work order can not be reviewed by internal team.
    if (woExist.work_order_type.title !== CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
      // send notification for checkout work order to service head of the same zone of supervisor.
      const woCustomerData = await db.WorkOrders.findOne({
        where: { id: work_order_id },
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        include: [
          {
            model: db.Customers,
            as: "customer_data",
            required: true,
            attributes: ATTRIBUTES.CUSTOMERS,
          },
        ],
      });

      const zone = await woCustomerData?.customer_data?.zone;

      if (!zone) {
        return response.handler.badRequest(
          MESSAGE.WORK_ORDER.CHECK_OUT.ZONE_NOT_FOUND
        );
      }

      let roles = [CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER];

      // get users based on zone.
      let userDeviceTokens = [];
      let receiver_and_send_to = [];
      const userAPI = await userService.getUsersByZone({
        zone: zone,
        headers: request?.headers,
        roles,
      });

      if (userAPI) {
        userAPI.map(async (user) => {
          if (user.device_token) {
            if (user?.loginHistory) {
              userDeviceTokens.push(user.device_token);
            }

            receiver_and_send_to.push({
              receiver_user_id: user.id,
              send_to: user.device_token,
              loginData: user?.loginHistory,
            });
          }
        });
      }

      if (userDeviceTokens?.length || receiver_and_send_to?.length) {
        // get notification template from user service.
        const templateAPI = await userService.getTemplate({
          headers: request?.headers,
          action: [
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REQUEST,
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        if (templateAPI) {
          const { title, body } = templateAPI[0];

          // add notification alert history
          const addAlertAPI = await userService.addAlerts({
            headers: request?.headers,
            sender_user_id: loggedInUser?.id,
            title,
            body,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REQUEST,
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
            receiver_and_send_to: receiver_and_send_to,
            is_sent: 1,
          });

          if (userDeviceTokens?.length) {
            const notificationResponse = await sendNotification(
              userDeviceTokens,
              title,
              body
            );
          }
        }
      }
    }

    // work order status should be updated  and update end datetime.
    const woStatus = await common.getWorkOrderStatusByTitle(status);
    const woCheckOut = await db.WorkOrders.update(
      {
        work_order_status_id: woStatus?.id,
        actual_end_datetime: new Date(), // utc current date
      },
      { where: { id: work_order_id } }
    );

    if (woCheckOut) {
      // create work order in zoho if type is not survey.
      if (woExist.work_order_type.title !== CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
        await createWorkOrderInZoho(woExist);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.CHECK_OUT.SUCCESS);
    } else {
      return response.handler.success(MESSAGE.WORK_ORDER.CHECK_OUT.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendOtpForSubmitWorkOrder = async (request, response) => {
  try {
    const { country_code, mobile, work_order_id } = request.body;

    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    /*
            - all task associate with work order should be completed, then only we can request otp.
            - total work order count and completed work order count should be same.
        */

    const totalWOTaskCount = await db.Tasks.count({
      where: {
        work_order_id,
        is_active: 1,
        is_deleted: 0,
      },
    });

    const completedWOTaskCount = await db.Tasks.count({
      where: {
        work_order_id: work_order_id,
        status: CONSTANTS.TASK_STATUS.COMPLETED,
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (totalWOTaskCount === completedWOTaskCount) {
      // check if user has request otp before?
      let whereCond2 = [
        // { work_order_id: work_order_id },
        { action: CONSTANTS.OTP.ACTION.SUBMIT_WORK_ORDER },
        { is_otp_verified: 0 },
        { country_code },
        { mobile },
      ];

      let whereCond3 = [
        { action: CONSTANTS.OTP.ACTION.SUBMIT_WORK_ORDER },
        { is_otp_verified: 1 },
        { country_code },
        { mobile },
      ];

      const where = `country_code = "${country_code}" AND mobile = "${mobile}"`;

      const isVerifiedOtp = await db.Otps.count({
        where: whereCond3,
      });

      if (isVerifiedOtp > 0) {
        whereCond2.push({
          id: {
            [Op.gt]: db.sequelize.literal(
              `(SELECT max(id) FROM otps WHERE ${where} AND is_otp_verified = 1)`
            ),
          },
        });
      }

      const requestedOtpsCount = await db.Otps.count({
        where: whereCond2,
      });

      const lastOtpData = await db.Otps.findOne({
        where: whereCond2,
        limit: 1,
        order: [["id", "DESC"]],
      });

      let timeDiff;
      if (lastOtpData) {
        timeDiff = await getTimeDiff(lastOtpData.createdAt, new Date());
      }

      let resend_otp_duration = 0;

      if (requestedOtpsCount === 0) {
        resend_otp_duration = CONSTANTS.OTP.RESEND_OPT_DURATION.FIRST;
      } else if (requestedOtpsCount === 1) {
        resend_otp_duration = CONSTANTS.OTP.RESEND_OPT_DURATION.SECOND;
        if (timeDiff.seconds < CONSTANTS.OTP.RESEND_OPT_DURATION.FIRST) {
          return response.handler.success(MESSAGE.OTP.RESEND_DURATION.FIRST, {
            new_otp_request_time: CONSTANTS.OTP.RESEND_OPT_DURATION.FIRST,
            requested_otp_count: requestedOtpsCount,
          });
        }
      } else if (requestedOtpsCount === 2) {
        resend_otp_duration =
          CONSTANTS.OTP.RESEND_OPT_DURATION.THIRD_OR_GREATER;
        if (timeDiff.seconds < CONSTANTS.OTP.RESEND_OPT_DURATION.SECOND) {
          return response.handler.success(MESSAGE.OTP.RESEND_DURATION.SECOND, {
            new_otp_request_time: CONSTANTS.OTP.RESEND_OPT_DURATION.SECOND,
            requested_otp_count: requestedOtpsCount,
          });
        }
      } else if (requestedOtpsCount >= 3) {
        resend_otp_duration =
          CONSTANTS.OTP.RESEND_OPT_DURATION.THIRD_OR_GREATER;
        if (
          timeDiff.seconds < CONSTANTS.OTP.RESEND_OPT_DURATION.THIRD_OR_GREATER
        ) {
          return response.handler.success(
            MESSAGE.OTP.RESEND_DURATION.THIRD_OR_GREATER,
            {
              new_otp_request_time:
                CONSTANTS.OTP.RESEND_OPT_DURATION.THIRD_OR_GREATER,
              requested_otp_count: requestedOtpsCount,
            }
          );
        }
      }

      const otp = await generateOtp(CONSTANTS.OTP.LENGTH, {
        digits: true,
        specialChars: false,
        upperCaseAlphabets: false,
        lowerCaseAlphabets: false,
      });

      await db.Otps.create({
        work_order_id: work_order_id,
        // customer_id: customer_id,
        country_code: country_code,
        mobile: mobile,
        otp: otp,
        action: CONSTANTS.OTP.ACTION.SUBMIT_WORK_ORDER,
        otp_send_to: CONSTANTS.OTP.SEND_OPTIONS.MOBILE,
      });

      const content = `Your OTP for Customer Approval of Work Order is ${otp}. This OTP will be valid for ${CONSTANTS.OTP.EXPIRES_IN} minutes.`;

      const sendOtpToMobile = await sendSMS(
        `${country_code}${mobile}`,
        content
      );
      // if (sendOtpToMobile.success) {
      return response.handler.success(MESSAGE.OTP.SENT.SUCCESS, {
        new_otp_request_time: resend_otp_duration,
        requested_otp_count: requestedOtpsCount + 1,
      });
      // }
      // else {
      //    return response.handler.serverError(error);
      // }
    } else {
      return response.handler.badRequest(MESSAGE.OTP.SENT.FAILED);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const verifyOtpForSubmitWorkOrder = async (request, response) => {
  try {
    const { work_order_id, otp, country_code, mobile } = request.body;

    let whereCond = [
      { work_order_id },
      { action: CONSTANTS.OTP.ACTION.SUBMIT_WORK_ORDER },
      // { otp: otp },
      { is_otp_verified: 0 },
      { country_code },
      { mobile },
    ];

    const lastOtpData = await db.Otps.findOne({
      where: whereCond,
      limit: 1,
      order: [["id", "DESC"]],
    });

    if (lastOtpData?.otp === otp) {
      const timeDiff = await getTimeDiff(lastOtpData.createdAt, new Date());

      if (timeDiff.minutes < CONSTANTS.OTP.EXPIRES_IN) {
        const verifyOtp = await db.Otps.update(
          { is_otp_verified: 1 },
          { where: { id: lastOtpData.id } }
        );

        if (verifyOtp) {
          // update otp verified flag in work order.
          await db.WorkOrders.update(
            { is_cus_otp_verified: 1 },
            { where: { id: work_order_id } }
          );

          return response.handler.success(MESSAGE.OTP.VERIFICATION.SUCCESS);
        }
      } else {
        return response.handler.badRequest(MESSAGE.OTP.EXPIRED);
      }
    } else {
      return response.handler.serverError(error);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getReviewList = async (request, response) => {
  try {
    const loggedInUser = await request?.login_user?.data;

    const page = parseInt(request?.query?.page) || CONSTANTS.PAGINATION.PAGE;
    const limit = parseInt(request?.query?.limit) || CONSTANTS.PAGINATION.LIMIT;
    const start = page > 1 ? (page - 1) * limit : 0;

    const underApprovalWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL
    );
    const partialSubmittedWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED
    );
    const serviceEnginnerApprovedWOStatus =
      await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED
      );

    const partiallyServiceEnginnerApprovedWOStatus =
      await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE
      );
    const partiallyServiceHeadApprovedWOStatus =
      await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH
      );

    let whereCond = {
      is_active: 1,
      is_deleted: 0,
      "$order_data.customer_data.zone$": `${loggedInUser?.zone?.name}`,
      work_order_status_id: {
        [Op.in]: [
          underApprovalWOStatus?.id,
          partialSubmittedWOStatus?.id,
          serviceEnginnerApprovedWOStatus?.id,
          partiallyServiceHeadApprovedWOStatus?.id,
        ],
      },
    };

    if (loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER) {
      whereCond = {
        ...whereCond,
        work_order_status_id: {
          [Op.in]: [
            underApprovalWOStatus?.id,
            partialSubmittedWOStatus?.id,
            partiallyServiceEnginnerApprovedWOStatus?.id,
          ],
        },
      };
    }

    const { count: totalCount, rows: reviewWorkOrderData } =
      await db.WorkOrders.findAndCountAll({
        limit: limit,
        offset: start,
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
        distinct: true,
        order: [
          ["id", "DESC"],
          [
            { model: db.WorkOrderReviewer, as: "work_order_reviewer" },
            "id",
            "DESC",
          ],
        ],
        include: [
          {
            model: db.WorkOrderStatus,
            as: "work_order_status",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          },
          {
            model: db.WorkOrderTypes,
            as: "work_order_type",
            required: true,
            attributes: ATTRIBUTES.WORK_ORDER_TYPES,
          },
          {
            model: db.WorkOrderSpaces,
            as: "work_order_spaces",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_SPACES,
            include: [
              {
                model: db.Spaces,
                as: "space_data",
                required: false,
                attributes: ATTRIBUTES.SPACES,
              },
              {
                model: db.WorkOrderScopes,
                as: "work_order_scopes",
                required: false,
                attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: ATTRIBUTES.SCOPES,
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                        include: [
                          {
                            model: db.ScopeImageTouchPoints,
                            as: "touch_points",
                            required: false,
                            attributes: ATTRIBUTES.SCOPE_IMAGE_TOUCH_POINTS,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.Tasks,
            as: "work_order_task",
            required: false,
            attributes: ATTRIBUTES.TASKS,
            include: [
              {
                model: db.WorkOrderScopes,
                as: "task_scope",
                required: false,
                attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
                include: [
                  {
                    model: db.Scopes,
                    as: "scope_data",
                    required: false,
                    attributes: [
                      ...ATTRIBUTES.SCOPES,
                      [
                        db.sequelize.fn(
                          "CONCAT",
                          db.sequelize.col(
                            "work_order_task.task_scope.scope_data.height"
                          ),
                          " ⨯ ",
                          db.sequelize.col(
                            "work_order_task.task_scope.scope_data.width"
                          )
                        ),
                        "dimension",
                      ],
                    ],
                    include: [
                      {
                        model: db.ScopeImages,
                        as: "scope_image",
                        required: false,
                        attributes: ATTRIBUTES.SCOPE_IMAGES.filter(
                          (value) => value !== "base64"
                        ),
                      },
                    ],
                  },
                ],
              },
              {
                model: db.TaskTouchPoints,
                as: "task_touch_points",
                required: false,
                attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
                include: [
                  {
                    model: db.TaskQuestions,
                    as: "task_questions",
                    required: false,
                    attributes: ATTRIBUTES.TASK_QUESTIONS,
                    include: [
                      {
                        model: db.Questions,
                        as: "question_data",
                        required: false,
                        attributes: ATTRIBUTES.QUESTIONS,
                        include: [
                          {
                            model: db.QuestionTypes,
                            as: "question_type",
                            required: false,
                            attributes: ATTRIBUTES.QUESTION_TYPES,
                          },
                        ],
                      },
                      {
                        model: db.TaskAnswers,
                        as: "answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null, is_deleted: null },
                          ],
                        },
                        attributes: ATTRIBUTES.TASK_ANSWERS,
                      },
                      {
                        model: db.TaskImageAnswers,
                        as: "image_answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null },
                            { is_deleted: null },
                          ],
                        },
                        attributes: ATTRIBUTES.TASK_IMAGE_ANSWERS.filter(
                          (value) => value !== "base64"
                        ),
                      },
                      {
                        model: db.TaskVideoAnswers,
                        as: "video_answer_data",
                        required: false,
                        where: {
                          is_video_uploaded: 1,
                          is_active: 1,
                          is_deleted: 0,
                        },
                        attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: db.Orders,
            as: "order_data",
            required: true,
            attributes: ATTRIBUTES.ORDERS,
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                // where: { zone: loggedInUser?.zone?.name },
                attributes: ATTRIBUTES.CUSTOMERS,
              },
            ],
          },
          {
            model: db.WorkOrderReview,
            as: "work_order_review",
            required: false,
            order: [["id", "DESC"]],
            attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
          },
          {
            model: db.Warranty,
            as: "warranty_data",
            required: false,
            attributes: ATTRIBUTES.WARRANTY,
          },
          {
            model: db.WorkOrderReviewer,
            as: "work_order_reviewer",
            required: false,
            attributes: ATTRIBUTES.WORK_ORDER_REVIEWER,
          },
        ],
      });

    if (!reviewWorkOrderData) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.FOUND.ERROR);
    } else if (totalCount === 0 || reviewWorkOrderData.length === 0) {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    } else {
      const assigned_to_user_ids = reviewWorkOrderData.map(
        (wod1) => wod1.assigned_to_user_id
      );
      const assigned_by_user_ids = reviewWorkOrderData.map(
        (wod2) => wod2.assigned_by_user_id
      );

      const internal_team_ids = reviewWorkOrderData.map(
        (wod3) => wod3.internal_team_id
      );

      let reviewer_user_id = [];
      let se_sh_user_id = [];

      await reviewWorkOrderData.map(async (wod3) => {
        const review_id = await wod3?.work_order_review
          ?.map((wo_review) => wo_review?.reviewer_user_id)
          ?.filter((val) => val);
        review_id && reviewer_user_id.push(...review_id);
      });

      await Promise.all(
        reviewWorkOrderData?.map(async (wod4) => {
          // Check if work_order_review is an array
          const reviewer_id = Array.isArray(wod4?.work_order_reviewer)
            ? wod4.work_order_reviewer
                .map((wo_review) => wo_review?.reviewer_user_id)
                .filter((val) => val) // Filter out null/undefined values
            : [];

          se_sh_user_id.push(...reviewer_id);
        })
      );

      if (reviewer_user_id?.length) {
        reviewer_user_id = [...new Set(reviewer_user_id)];
      }

      if (se_sh_user_id?.length) {
        se_sh_user_id = [...new Set(se_sh_user_id)];
      }

      const user_ids = [
        ...assigned_to_user_ids,
        ...assigned_by_user_ids,
        ...reviewer_user_id,
        ...internal_team_ids,
        ...se_sh_user_id,
      ];
      // get user from user authentication service.
      let userData = [];
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: user_ids,
        headers: request?.headers,
      });

      if (userAPI) userData = userAPI;

      let respData = [];

      for (let i = 0; i < reviewWorkOrderData.length; i++) {
        let assigned_to_user = {};
        let assigned_by_user = {};
        let internal_team_user = {};

        if (userData.length) {
          assigned_to_user = userData.find(
            (ele) => ele.id === reviewWorkOrderData[i].assigned_to_user_id
          );
          assigned_by_user = userData.find(
            (ele2) => ele2.id === reviewWorkOrderData[i].assigned_by_user_id
          );
          internal_team_user = userData.find(
            (ele3) => ele3.id === reviewWorkOrderData[i].internal_team_id
          );
        }

        let responseData = {
          ...JSON.parse(JSON.stringify(reviewWorkOrderData[i])),
          assigned_to_user: assigned_to_user,
          assigned_by_user: assigned_by_user,
          internal_team_user: internal_team_user || {},
        };

        if (reviewWorkOrderData[i]?.work_order_review?.length) {
          const workOrderReviewData = [
            ...reviewWorkOrderData[i]?.work_order_review,
          ];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_review: resWorkOrderReview,
          };
        }

        if (reviewWorkOrderData[i]?.work_order_reviewer?.length) {
          const workOrderReviewData = [
            ...reviewWorkOrderData[i]?.work_order_reviewer,
          ];
          const resWorkOrderReview = [];
          for (let j = 0; j < workOrderReviewData?.length; j++) {
            let tmpWoReviewData = workOrderReviewData?.[j] || {};

            let reviewer_user_data = {};
            reviewer_user_data =
              (await userData.find(
                (ele2) => ele2.id === tmpWoReviewData?.reviewer_user_id
              )) || {};

            tmpWoReviewData = {
              ...JSON.parse(JSON.stringify(tmpWoReviewData)),
              reviewer_user_details: reviewer_user_data,
            };
            resWorkOrderReview.push(tmpWoReviewData);
          }

          responseData = {
            ...responseData,
            work_order_reviewer: resWorkOrderReview,
          };
        }

        const respWorkOrderSpaceData = await common.getScopeDetails(
          responseData?.order_data?.zoho_order_id,
          responseData
        );

        responseData = {
          ...responseData,
          work_order_spaces: respWorkOrderSpaceData,
        };

        respData.push(responseData);
      }

      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: totalCount,
        count: reviewWorkOrderData.length,
        work_orders: respData,
      });
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const review = async (request, response) => {
  try {
    const { work_order_id, order_id, review_status, reason, task_details } =
      request.body;

    const loggedInUser = await request?.login_user?.data;

    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }
    // only internal team user can access. & their zone should be matched with work order's zone
    if (loggedInUser?.zone?.name !== woExist?.customer_data?.zone) {
      return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
    }

    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED,
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.REVIEW.FAILED);
    }

    if (
      woExist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED &&
      loggedInUser?.role?.name === CONSTANTS?.ROLES?.NAMES?.SERVICE_ENGINEER
    ) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.REVIEW.ALREADY_APPROVED_BY_SERVICE_ENGINEER
      );
    }

    // survey type work order can not be reviewed. As it will be reviewed based on customer approval flag which we will receive from zoho.
    if (woExist.work_order_type.title === CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.REVIEW.FAILED_DUE_TO_SURVEY
      );
    }

    const checkInData = await db.WorkOrderReviewer.findOne({
      where: {
        work_order_id,
        reviewer_user_id: loggedInUser?.id,
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
      },
    });

    if (!checkInData) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.REVIEW.PLEASE_CHECK_IN
      );
    }

    const amount = woExist?.wo_order?.amount;

    if (
      woExist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL &&
      loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_HEAD &&
      amount < CONSTANTS.ORDER_AMOUNT
    ) {
      return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
    }

    // get user from user authentication service.
    let userData = [];
    const userAPI = await userService.getUserDetailsByUserIds({
      user_ids: [
        woExist?.assigned_to_user_id,
        woExist?.wo_order?.fabricator_id,
      ],
      headers: request?.headers,
    });

    if (userAPI) {
      userData = userAPI;
    }

    let woStatus, message, notificationAction;

    //get work order data with task
    const woData = await db.WorkOrders.findOne({
      where: { id: work_order_id },
      attributes: ATTRIBUTES.WORK_ORDERS,
      include: [
        {
          model: db.Tasks,
          as: "work_order_task",
          attributes: ATTRIBUTES.TASKS,
        },
      ],
    });

    if (review_status === 1) {
      const taskRejectedData = task_details?.filter(
        (task) => !task?.status || task?.status === 2
      );

      const taskDetailIds = task_details?.map((item) => item?.id);

      const taskIds = woData?.work_order_task?.map((task) => task?.id);

      const remainingTask = woData?.work_order_task?.filter(
        (task) =>
          !taskDetailIds?.includes(task?.id) &&
          (task?.status === CONSTANTS.TASK_STATUS.PENDING ||
            task?.status === CONSTANTS.TASK_STATUS.COMPLETED ||
            (loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_HEAD &&
              task?.status === CONSTANTS.TASK_STATUS.APPROVED_BY_SE))
      );
      //if any task is rejected or any task is in pending status
      if (taskRejectedData?.length || remainingTask?.length) {
        //check taskids from body exist in work order or not
        const taskIdNotExist = task_details
          ?.filter((item) => !taskIds?.includes(item?.id))
          ?.map((data) => data?.id);

        if (taskIdNotExist?.length) {
          return response.handler.badRequest(
            MESSAGE.TASK.IDS_NOT_EXIST(taskIdNotExist.join(","))
          );
        }

        const statusName =
          loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER
            ? CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE
            : CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH;

        woStatus = await common.getWorkOrderStatusByTitle(statusName);
      } else {
        if (
          amount >= CONSTANTS.ORDER_AMOUNT &&
          [
            CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL,
            CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED,
            CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
            CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
          ].includes(woExist?.work_order_status?.title)
        ) {
          if (
            loggedInUser?.role?.name !==
            CONSTANTS?.ROLES?.NAMES?.SERVICE_ENGINEER
          ) {
            return response.handler.badRequest(
              MESSAGE.WORK_ORDER.REVIEW.APPROVAL_RMAINING_FROM_SERVICE_ENGINNER
            );
          }
          const woCustomerData = await common.getWorkOrderCustomerById(
            work_order_id
          );
          const zone = await woCustomerData?.customer_data?.zone;
          if (!zone) {
            return response.handler.badRequest(
              MESSAGE.WORK_ORDER.CHECK_OUT.ZONE_NOT_FOUND
            );
          }

          let userDeviceTokens = [];
          let receiver_and_send_to = [];
          const userAPI = await userService.getUsersByZone({
            zone: zone,
            headers: request?.headers,
            roles: [CONSTANTS.ROLES.NAMES.SERVICE_HEAD],
          });

          if (userAPI) {
            userAPI.map(async (user) => {
              if (user.device_token) {
                userDeviceTokens.push(user.device_token);

                receiver_and_send_to.push({
                  receiver_user_id: user.id,
                  send_to: user.device_token,
                  loginData: user?.loginHistory,
                });
              }
            });
          }

          if (userDeviceTokens.length) {
            await common.sendNotificationToUsers(
              request?.headers,
              loggedInUser?.id,
              receiver_and_send_to,
              userDeviceTokens
            );
          }

          // work order status should be updated  and update end datetime.
          woStatus = await common.getWorkOrderStatusByTitle(
            CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED
          );
        } else {
          notificationAction =
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_APPROVED;
          message = MESSAGE.WORK_ORDER.REVIEW.APPROVED;
          woStatus = await common.getWorkOrderStatusByTitle(
            CONSTANTS.WORK_ORDER.STATUS.COMPLETED
          );

          // if work order is approved, then we need to assign points to user based on work order type & transaction_type.
          const pointData = await common.getPointsOfWoTypeByCondition([
            { work_order_type_id: woExist.work_order_type_id },
            { transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT },
            { is_active: 1 },
          ]);

          // get current rate of points
          const rateData = await common.getPointRatesByCondition([
            { is_active: 1 },
          ]);

          if (pointData && rateData) {
            const addPointHistory = await db.PointHistory.create({
              user_id: woExist.assigned_to_user_id,
              work_order_id,
              work_order_type_point_id: pointData.id,
              point_rate_id: rateData.id,
            });

            if (addPointHistory) {
              await common.updateUserPointBalance(woExist.assigned_to_user_id);
            }
          }

          // check if work order type is handover, if then we need to generate warranty and send to mail
          if (
            woExist.work_order_type.title === CONSTANTS.WORK_ORDER.TYPE.HANDOVER
          ) {
            await generateWarrantyAndSendMail(woExist, request);
          }

          // send work order pdf to zoho.
          if (woExist?.inserted_zoho_wo_id) {
            const pdfData = await getWorkOrderPdfData(woExist);

            const payload = {
              template: {
                name: CONSTANTS.PDF.JS_REPORT_TEMPLATE.WORK_ORDER,
              },
              data: {
                title: `${CONSTANTS.PDF.SURVEY.ACTION.FINAL} WORK ORDER REPORT`,
                name: woExist.wo_order.title,
                location: woExist.customer_data.location,
                universe: woExist.customer_data.universe,
                zoho_order_id: woExist.wo_order.zoho_order_id,
                work_order_data: pdfData,
                terms: CONSTANTS.PDF.TEMPLATE_DATA.WORK_ORDER.TERMS,
              },
            };

            const pdf = await generatePDF(payload);
            if (pdf.pdfBuffer) {
              const { BASE, WORK_ORDER } = CONSTANTS.S3_BUCKET.FOLDERS.PDF;
              const fileName = `${woExist.actual_work_order_id}.pdf`;
              const { Key, Location } = await aws.uploadFileS3(
                fileName,
                pdf.pdfBuffer,
                `${BASE}/${WORK_ORDER}`
              );

              if (Key && Location) {
                // add pdf data
                const createAttachment = await db.WorkOrderAttachments.create({
                  work_order_id: woExist.id,
                  work_order_type_id: woExist.work_order_type_id,
                  file_key: Key,
                  file_url: Location,
                  action: CONSTANTS.PDF.SURVEY.ACTION.FINAL,
                  type: "pdf",
                });

                if (createAttachment.id) {
                  // send wo pdf link to zoho
                  const data = {
                    zoho_work_order_id: woExist?.inserted_zoho_wo_id,
                    attachment_url: Location,
                    work_order_attachment_id: createAttachment.id,
                  };

                  await zoho.sendWorkOrderPdfToZoho(data);
                }
              }
            }
          }
        }
      }
    } else if (review_status === 0) {
      notificationAction =
        CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REJECTED;
      message = MESSAGE.WORK_ORDER.REVIEW.REJECT;
      woStatus = await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.REJECTED
      );

      // check if any work order rejected for 3rd time or not, which we can identify by modulo operation.
      if (woExist.rejected_count > 0 && woExist.rejected_count % 2 === 0) {
        // if work order is rejected, then we need to deduct points from user based on  transaction_type.
        const pointData = await common.getPointsOfWoTypeByCondition([
          // { work_order_type_id: woExist.work_order_type_id },
          { transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.DEBIT },
          { is_active: 1 },
        ]);

        // get current rate of points
        const rateData = await common.getPointRatesByCondition([
          { is_active: 1 },
        ]);

        if (pointData && rateData) {
          const addPointHistory = await db.PointHistory.create({
            user_id: woExist.assigned_to_user_id,
            work_order_id,
            work_order_type_point_id: pointData.id,
            point_rate_id: rateData.id,
          });

          if (addPointHistory) {
            await common.updateUserPointBalance(woExist.assigned_to_user_id);
          }
        }
      }

      // update rejected count
      await db.WorkOrders.update(
        { rejected_count: woExist.rejected_count + 1 },
        { where: { id: work_order_id } }
      );

      const tasks = await db.Tasks.findAll({
        where: {
          work_order_id: work_order_id,
          is_active: 1,
          is_deleted: 0,
        },
        attributes: ATTRIBUTES.TASKS,
        raw: true,
      });

      if (tasks.length) {
        // update is_submitted = 0 & status = Pending in tasks
        const taskIds = await tasks.map((data) => data.id);

        const pendingTaskIds = taskIds?.filter(
          (item) => task_details?.findIndex((data) => data?.id === item) === -1
        );

        if (taskIds?.length) {
          await db.Tasks.update(
            { status: CONSTANTS.TASK_STATUS.PENDING, is_submitted: 0 },
            { where: { id: { [Op.in]: pendingTaskIds } } }
          );
        }

        // get task touch point ids based on task
        const taskTouchPoint = await db.TaskTouchPoints.findAll({
          where: { task_id: { [Op.in]: taskIds } },
          attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
          raw: true,
        });

        if (taskTouchPoint.length) {
          const taskTouchPointIds = await taskTouchPoint.map((ttp) => ttp.id);

          // get question ids based on task touch point
          const taskQuestions = await db.TaskQuestions.findAll({
            where: { id: { [Op.in]: taskTouchPointIds } },
            attributes: ATTRIBUTES.TASK_QUESTIONS,
            raw: true,
          });

          if (taskQuestions.length) {
            const taskQuestionsIds = await taskQuestions.map((tq) => tq.id);

            // update answer as inactive & deleted with task question ids.
            await db.TaskAnswers.update(
              { is_active: 0, is_deleted: 1 },
              { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
            );

            await db.TaskImageAnswers.update(
              { is_active: 0, is_deleted: 1 },
              { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
            );

            await db.TaskVideoAnswers.update(
              { is_active: 0, is_deleted: 1 },
              { where: { task_question_id: { [Op.in]: taskQuestionsIds } } }
            );
          }
        }
      }
    }
    if (userData.length) {
      // get notification template from user service.
      const templateAPI = await userService.getTemplate({
        headers: request?.headers,
        action: [notificationAction],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        let replaceBody = await replaceData(
          body,
          "{WORK_ORDER_ID}",
          woExist.actual_work_order_id
        );

        if (review_status === 0) {
          replaceBody = await replaceData(
            replaceBody,
            "{REJECT_REASON}",
            reason
          );
        }

        const deviceTokens = await userData.map((data) => data.device_token);
        let receiver_and_send_to = [];
        await userData.map((data) => {
          receiver_and_send_to.push({
            receiver_user_id: data.id,
            send_to: data.device_token,
            loginData: data?.loginHistory,
          });
        });

        // add notification alert history
        const addAlertAPI = await userService.addAlerts({
          headers: request?.headers,
          sender_user_id: loggedInUser?.id,
          title,
          body: replaceBody,
          action:
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REQUEST,
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          receiver_and_send_to: receiver_and_send_to,
          is_sent: 1,
        });

        const notificationResponse = await sendNotification(
          deviceTokens,
          title,
          replaceBody
        );
      }
    }

    if (woStatus?.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED) {
      let taskData = woData?.work_order_task?.map((item) => ({
        id: item?.id,
        status: item?.status,
      }));

      task_details?.forEach((item) => {
        const index = taskData?.findIndex((data) => data?.id === item?.id);
        if (index !== -1) {
          let status;
          switch (item?.status) {
            case 1:
              status = CONSTANTS.TASK_STATUS.COMPLETED;
              break;
            case 2:
              status = CONSTANTS.TASK_STATUS.NOT_REVIEWED;
              break;
            default:
              status = CONSTANTS.TASK_STATUS.REJECTED;
              break;
          }
          taskData[index] = { ...taskData[index], status };
        }
      });

      taskData = taskData?.filter(
        (data) =>
          data?.status === CONSTANTS.TASK_STATUS.COMPLETED ||
          data?.status === CONSTANTS.TASK_STATUS.APPROVED_BY_SE ||
          data?.status === CONSTANTS.TASK_STATUS.APPROVED_BY_SH
      );

      if (taskData?.length !== woData?.work_order_task?.length) {
        return response.handler.badRequest(
          MESSAGE.WORK_ORDER.REVIEW.ALL_TASK_COMPLETED
        );
      }
    }

    // work order status will be updated.
    await db.WorkOrders.update(
      { work_order_status_id: woStatus?.id },
      { where: { id: work_order_id } }
    );

    if (
      review_status === 1 &&
      woStatus?.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED
    ) {
      await checkForHandoverWorkorder(order_id);
    }

    if (task_details?.length) {
      const taskReviewData = [];
      const taskPromiseArr = [];
      for (let i = 0; i < task_details?.length; i++) {
        const item = task_details?.[i];
        const approvedStatus =
          loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER
            ? CONSTANTS.TASK_STATUS.APPROVED_BY_SE
            : CONSTANTS.TASK_STATUS.APPROVED_BY_SH;
        let status, is_submitted;

        switch (item?.status) {
          case 1:
            status = approvedStatus;
            is_submitted = 1;
            break;
          case 2:
            status = CONSTANTS.TASK_STATUS.NOT_REVIEWED;
            is_submitted = 1;
            break;
          default:
            status = CONSTANTS.TASK_STATUS.REJECTED;
            is_submitted = 0;
            break;
        }
        const taskData = await db.Tasks.findOne({
          where: { id: item?.id },
          attributes: ["id"],
          include: [
            {
              model: db.TaskTouchPoints,
              as: "task_touch_points",
              required: false,
              attributes: ["id", "task_id"],
              include: [
                {
                  model: db.TaskQuestions,
                  as: "task_questions",
                  required: false,
                  attributes: ["id", "task_touch_point_id"],
                },
              ],
            },
          ],
        });
        taskPromiseArr.push(
          db.Tasks.update(
            {
              status,
              reason: item?.reason,
              is_submitted,
            },
            { where: { id: item?.id } }
          )
        );

        const questionIds = taskData?.task_touch_points
          ?.flatMap((item) => item?.task_questions)
          ?.map((item) => item?.id);

        item?.status &&
          taskPromiseArr.push(
            db.TaskTouchPoints.update(
              { status: approvedStatus },
              {
                where: {
                  task_id: item?.id,
                },
              }
            )
          );
        item?.status &&
          taskPromiseArr.push(
            db.TaskQuestions.update(
              { status: approvedStatus },
              {
                where: {
                  id: { [Op.in]: questionIds },
                },
              }
            )
          );

        //data for task review
        taskReviewData.push({
          task_id: item?.id,
          status,
          reason: item?.reason,
          assigned_to_user_id: woExist?.assigned_to_user_id,
          reviewer_user_id: loggedInUser?.id,
        });
      }
      await Promise.all(taskPromiseArr);
      taskReviewData?.length &&
        (await db.TaskReview.bulkCreate(taskReviewData));
    }
    const reviewDataExist = await db.WorkOrderReview.findOne({
      where: {
        reviewer_user_id: loggedInUser?.id,
        requester_user_id: woExist?.assigned_to_user_id,
        work_order_id: work_order_id,
      },
      attributes: ATTRIBUTES.WORK_ORDER_REVIEW,
    });

    if (reviewDataExist) {
      await db.WorkOrderReview.update(
        {
          work_order_id: work_order_id,
          reviewer_user_id: loggedInUser?.id,
          requester_user_id: woExist?.assigned_to_user_id,
          review_status: review_status,
          reason: reason || null,
        },
        {
          where: { id: reviewDataExist?.id },
        }
      );
    } else {
      await db.WorkOrderReview.create({
        work_order_id: work_order_id,
        reviewer_user_id: loggedInUser?.id,
        requester_user_id: woExist?.assigned_to_user_id,
        review_status: review_status,
        reason: reason || null,
      });
    }

    // we need to change status of order to complete, if work order type is handover and review status is 1.
    if (
      woExist.work_order_type.title === CONSTANTS.WORK_ORDER.TYPE.HANDOVER &&
      review_status === 1 &&
      woStatus?.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED
    ) {
      const completeOrderStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.COMPLETED
      );
      await db.Orders.update(
        { order_status_id: completeOrderStatus?.id },
        { where: { id: order_id } }
      );
    }

    //update work order reviewer table details
    const woReviewerData = await db.WorkOrderReviewer.findOne({
      where: {
        work_order_id,
        reviewer_user_id: loggedInUser?.id,
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
      },
    });

    if (woReviewerData) {
      const reviewStatus =
        review_status === 0
          ? CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.REJECTED
          : CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.COMPLETED;

      await db.WorkOrderReviewer.update(
        {
          status: reviewStatus,
        },
        {
          where: { id: woReviewerData?.id },
        }
      );
    }

    return response.handler.success(message);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const generateWarrantyAndSendMail = async (work_order, request) => {
  try {
    const s3BaseUrl = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_S3_REGION}.amazonaws.com/templates/`;

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();

    const payload = {
      template: {
        // name: "/warranty/warranty.html"
        name: CONSTANTS.PDF.JS_REPORT_TEMPLATE.WARRANTY,
      },
      data: {
        image: `${s3BaseUrl}image/warranty01.jpg`,
        logo: {
          eternia: `${s3BaseUrl}logo/eternia.png`,
          adityBirla: `${s3BaseUrl}logo/aditya-birla.png`,
        },
        customer_name: work_order.customer_data.name,
        warranty_end_date_aluminium_profiles:
          work_order?.wo_order?.profile_warranty_end_date ||
          `${year}-${month}-${day}`,
        warranty_end_date_hardwares:
          work_order?.wo_order?.hardware_warranty_end_date ||
          `${year}-${month}-${day}`,
        additional_remarks: "",
        warranty_id: work_order?.wo_order?.zoho_warranty_id || "",
      },
    };

    const pdf = await generatePDF(payload);

    if (pdf.pdfBuffer) {
      const { BASE, WARRANTY } = CONSTANTS.S3_BUCKET.FOLDERS.PDF;
      const fileName = `${work_order.actual_work_order_id}_warranty.pdf`;
      const { Key, Location } = await aws.uploadFileS3(
        fileName,
        pdf.pdfBuffer,
        `${BASE}/${WARRANTY}`
      );

      if (Key && Location) {
        // add warranty data in db.
        const addWarranty = await db.Warranty.create({
          work_order_id: work_order.id,
          customer_id: work_order.customer_data.id,
        });

        const warranty_id = addWarranty.id;
        const actual_warranty_id = await getPadString(warranty_id, "WA");

        await db.Warranty.update(
          {
            actual_warranty_id,
            file_key: Key,
            file_url: Location,
          },
          { where: { id: warranty_id } }
        );
      }

      // get warranty mail template from user service.
      const templateAPI = await userService.getTemplate({
        headers: request?.headers,
        action: [CONSTANTS.TEMPLATES.ACTION.EMAIL.WORK_ORDER_WARRANTY],
        type: CONSTANTS.TEMPLATES.TYPES.EMAIL,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        const customerEmail = work_order.customer_data.email;

        await sendMail(customerEmail, title, body, {
          name: fileName,
          url: Location,
        });
      }
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
  }
};

const createWorkOrderInZoho = async (workOrder) => {
  const woSpaces = await db.WorkOrderSpaces.findAll({
    where: { work_order_id: workOrder.id },
    attributes: ATTRIBUTES.WORK_ORDER_SPACES,
    include: [
      {
        model: db.Spaces,
        as: "space_data",
        required: true,
        attributes: ATTRIBUTES.SPACES,
      },
    ],
  });

  const woScopes = await db.WorkOrderScopes.findAll({
    where: { work_order_id: workOrder.id },
    attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
    include: [
      {
        model: db.Scopes,
        as: "scope_data",
        required: true,
        attributes: ATTRIBUTES.SCOPES,
      },
    ],
  });

  const spaces = woSpaces.map((sp) => sp.space_data.title);
  const scopes = woScopes.map(
    (sc) => `${sc.scope_data.name} (${sc.scope_data.model})`
  );

  // Convert time string to 24-hour format
  const time = workOrder.actual_start_time;
  const formattedTime = time.replace(
    /^(\d{1,2}):(\d{2}) (AM|PM)$/,
    (match, hour, minute, period) => {
      const hh = parseInt(hour, 10);
      const mm = parseInt(minute, 10);
      return (
        (period === "AM"
          ? hh === 12
            ? "00"
            : hh < 10
            ? "0" + hh
            : hh
          : hh === 12
          ? 12
          : hh + 12) +
        ":" +
        (mm < 10 ? "0" + mm : mm)
      );
    }
  );

  const startDate = `${workOrder.actual_start_date}T${formattedTime}:00`;
  const dateTime = new Date(startDate);
  const formattedDateTime = dateTime.toISOString().slice(0, 19);

  // const response = await buildWOQueAnsResponse(workOrder);

  const data = {
    zoho_order_id: workOrder.wo_order.zoho_order_id,
    // response: response, // all task's question & answer
    scopes: (scopes.length && scopes.join(", ")) || "", // comma seprated all scopes
    spaces: (spaces.length && spaces.join(", ")) || "", // comma seprated all spaces
    // start_date: new Date(`${workOrder.actual_start_date} ${workOrder.actual_start_time}`),
    start_date: formattedDateTime,
    work_order_status: workOrder.work_order_status.title, // wo status
    work_order_type: workOrder.work_order_type.title, // wo type
    actual_work_order_id: workOrder.actual_work_order_id, // wo id
    zoho_fabricator_id: workOrder.wo_order.zoho_fabricator_id,
  };

  const zohoWo = await zoho.sendWorkOrderToZoho(data);
  return zohoWo;
};

const buildWOQueAnsResponse = async (workOrder) => {
  let response = "";
  /*
        1. get all task by work order id
        2. get task's touch points
        3. get touch point wise question
        4. get answer of question
    */

  const tasks = await db.Tasks.findAll({
    where: {
      work_order_id: workOrder.id,
      id: 3,
    },
    attributes: ATTRIBUTES.TASKS,
    include: [
      {
        model: db.TaskTouchPoints,
        as: "task_touch_points",
        required: true,
        attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
        include: [
          {
            model: db.TaskQuestions,
            as: "task_questions",
            required: true,
            attributes: ATTRIBUTES.TASK_QUESTIONS,
            include: [
              {
                model: db.Questions,
                as: "question_data",
                required: true,
                attributes: ATTRIBUTES.QUESTIONS,
                where: { is_active: 1 },
              },
              {
                model: db.TaskAnswers,
                as: "answer_data",
                required: true,
                where: { is_active: 1, is_deleted: 0 },
                attributes: ATTRIBUTES.TASK_ANSWERS,
              },
              {
                model: db.TaskImageAnswers,
                as: "image_answer_data",
                required: false,
                where: {
                  [Op.or]: [
                    { is_active: 1, is_deleted: 0 },
                    { is_active: null },
                    { is_deleted: null },
                  ],
                },
                attributes: ATTRIBUTES.TASK_IMAGE_ANSWERS.filter(
                  (value) => value !== "base64"
                ),
              },
              {
                model: db.TaskVideoAnswers,
                as: "video_answer_data",
                required: false,
                where: {
                  is_video_uploaded: 1,
                  is_active: 1,
                  is_deleted: 0,
                },
                attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
              },
            ],
          },
        ],
      },
    ],
  });

  if (tasks.length) {
    for (let i = 0; i < tasks.length; i++) {
      response += `TASK: ${tasks[i].actual_task_id}\n`;

      const task_touch_points = tasks[i].task_touch_points;
      if (task_touch_points.length) {
        for (let j = 0; j < task_touch_points.length; j++) {
          response += `\nTOUCH_POINT: ${task_touch_points[j].touch_point}\n`;

          const task_questions = task_touch_points[j].task_questions;
          if (task_questions.length) {
            for (let k = 0; k < task_questions.length; k++) {
              response += `QUESTION ${k + 1}: ${
                task_questions[k].question_data.title
              }\n`;

              const descriptive_answers = task_questions[k].answer_data;
              if (descriptive_answers.length) {
                response += `DESCRIPTIVE_ANSWERS:\n`;
                for (let l = 0; l < descriptive_answers.length; l++) {
                  response += `ANS_${l + 1}: ${
                    descriptive_answers[l].answer
                  }\n`;
                }
              }

              const image_answers = task_questions[k].image_answer_data;
              if (image_answers?.length) {
                response += `IMAGE_ANSWERS:\n`;
                for (let m = 0; m < image_answers.length; m++) {
                  response += `ANS_${m + 1}: ${image_answers[m].image_url}\n`;
                }
              }

              const video_answers = task_questions[k].video_answer_data;
              if (video_answers?.length) {
                response += `VIDEO_ANSWERS:\n`;
                for (let n = 0; n < video_answers.length; n++) {
                  response += `ANS_${n + 1}: ${video_answers[n].video_url}\n`;
                }
              }
            }
          }
        }

        response += `\n`;
      }
    }
  }

  return response;
};

const getStatus = async (request, response) => {
  try {
    const woStatus = await db.WorkOrderStatus.findAll({
      where: {
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ATTRIBUTES.WORK_ORDER_STATUS,
    });

    if (woStatus) {
      return response.handler.success(MESSAGE.WORK_ORDER.STATUS.FOUND.SUCCESS, {
        work_order_status: woStatus,
      });
    } else {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.STATUS.FOUND.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const sendSurveyPdfToZoho = async (request, response) => {
  try {
    /*
            action = draft:
            - keep wo status in progress
            - create work order in zoho if not available
            - send draft pdf to zoho

            action = submit:
            - change status to under approval / partially submitted
            - create work order in zoho if not available
            - send final pdf to zoho
        */

    const { action, work_order_id } = request.body;

    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    if (woExist.work_order_type.title !== CONSTANTS.WORK_ORDER.TYPE.SURVEY) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.SEND_PDF.FAILED);
    }

    // if zoho work order id is null that means work order is not created in zoho. So first we need to create work order in zoho.
    let zoho_work_order_id = woExist.inserted_zoho_wo_id;
    if (!zoho_work_order_id) {
      zoho_work_order_id = await createWorkOrderInZoho(woExist);
    }

    //get work order pdf data
    const pdfData = await getWorkOrderPdfData(woExist);

    const payload = {
      template: {
        name: CONSTANTS.PDF.JS_REPORT_TEMPLATE.WORK_ORDER,
      },
      data: {
        title: `${action.toUpperCase()} WORK ORDER REPORT`,
        name: woExist.wo_order.title,
        location: woExist.customer_data.location,
        universe: woExist.customer_data.universe,
        zoho_order_id: woExist.wo_order.zoho_order_id,
        work_order_data: pdfData,
        terms: CONSTANTS.PDF.TEMPLATE_DATA.WORK_ORDER.TERMS,
      },
    };

    const pdf = await generatePDF(payload);

    if (pdf.pdfBuffer) {
      const { BASE, WORK_ORDER } = CONSTANTS.S3_BUCKET.FOLDERS.PDF;
      const fileName = `${woExist.actual_work_order_id}_${action}.pdf`;
      const { Key, Location } = await aws.uploadFileS3(
        fileName,
        pdf.pdfBuffer,
        `${BASE}/${WORK_ORDER}`
      );

      if (Key && Location) {
        // add pdf data
        const createAttachment = await db.WorkOrderAttachments.create({
          work_order_id: woExist.id,
          work_order_type_id: woExist.work_order_type_id,
          file_key: Key,
          file_url: Location,
          action: action,
          type: "pdf",
        });

        if (createAttachment.id) {
          // send wo pdf link to zoho
          const data = {
            zoho_work_order_id: zoho_work_order_id,
            attachment_url: Location,
            work_order_attachment_id: createAttachment.id,
          };

          const sendPdf = await zoho.sendWorkOrderPdfToZoho(data);

          if (sendPdf) {
            return response.handler.success(
              MESSAGE.WORK_ORDER.SEND_PDF.SUCCESS
            );
          } else {
            return response.handler.badRequest(
              MESSAGE.WORK_ORDER.SEND_PDF.ERROR
            );
          }
        }
      }
    } else {
      return response.handler.badRequest(
        pdf?.error || MESSAGE.WORK_ORDER.SEND_PDF.ERROR
      );
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getWorkOrderPdfData = async (workOrder) => {
  const data = await db.WorkOrderSpaces.findAll({
    where: { work_order_id: workOrder.id },
    // attributes: ATTRIBUTES.WORK_ORDER_SPACES,
    attributes: [],
    include: [
      {
        model: db.Spaces,
        as: "space_data",
        required: true,
        // attributes: ATTRIBUTES.SPACES
        attributes: ["id", "title"],
      },
      {
        model: db.WorkOrderScopes,
        as: "work_order_scopes",
        required: true,
        // attributes: ATTRIBUTES.WORK_ORDER_SCOPES,
        attributes: ["work_order_space_mapping_id"],
        include: [
          {
            model: db.Scopes,
            as: "scope_data",
            required: true,
            // attributes: ATTRIBUTES.SCOPES,
            attributes: ["id", "model", "name", "reference"],
          },
          {
            model: db.Tasks,
            as: "work_order_task",
            required: false,
            // attributes: ATTRIBUTES.TASKS,
            attributes: ["id", "status"],
            include: [
              {
                model: db.TaskTouchPoints,
                as: "task_touch_points",
                required: true,
                // attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
                attributes: ["id"],
                include: [
                  {
                    model: db.TaskQuestions,
                    as: "task_questions",
                    required: true,
                    // attributes: ATTRIBUTES.TASK_QUESTIONS,
                    attributes: ["question_id"],
                    include: [
                      {
                        model: db.Questions,
                        as: "question_data",
                        required: true,
                        // attributes: ATTRIBUTES.QUESTIONS,
                        attributes: ["title"],
                        include: [
                          {
                            model: db.ReportTags,
                            as: "report_tag",
                            required: false,
                            attributes: ATTRIBUTES.REPORT_TAGS,
                            where: { is_active: 1 },
                          },
                        ],
                      },
                      {
                        model: db.TaskAnswers,
                        as: "answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null, is_deleted: null },
                          ],
                        },
                        // attributes: ATTRIBUTES.TASK_ANSWERS,
                        attributes: ["answer"],
                      },
                      {
                        model: db.TaskImageAnswers,
                        as: "image_answer_data",
                        required: false,
                        where: {
                          [Op.or]: [
                            { is_active: 1, is_deleted: 0 },
                            { is_active: null },
                            { is_deleted: null },
                          ],
                        },
                        attributes: ["image_url"],
                      },
                      {
                        model: db.TaskVideoAnswers,
                        as: "video_answer_data",
                        required: false,
                        where: {
                          is_video_uploaded: 1,
                          is_active: 1,
                          is_deleted: 0,
                        },
                        // attributes: ATTRIBUTES.TASK_VIDEO_ANSWERS,
                        attributes: ["video_url"],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  });

  return data;
};

const deleteWorkOrder = async (request, response) => {
  try {
    const { id } = request.params;
    if (!id) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.DELETE.ID_REQUIRE);
    }

    const loggedInUser = request?.login_user?.data;
    // check if work order is exist and that is created by this fabricator
    const woData = await db.WorkOrders.findOne({
      where: {
        id,
        assigned_by_user_id: loggedInUser?.id,
      },
      include: [
        {
          model: db.WorkOrderStatus,
          as: "work_order_status",
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
        },
      ],
    });

    if (isEmpty(woData)) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    //check work order status if it is pending or not
    if (
      woData?.work_order_status?.title !== CONSTANTS.WORK_ORDER.STATUS.PENDING
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.DELETE.ERROR);
    }

    //delete work order
    await db.WorkOrders.destroy({ where: { id } });

    return response.handler.success(MESSAGE.WORK_ORDER.DELETE.SUCCESS);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const acceptRejectTagRequest = async (request, response) => {
  try {
    const { work_order_id, status, alert_id } = request.body;
    const loggedInUser = await request?.login_user?.data;
    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    if (woExist?.internal_team_id !== loggedInUser?.id) {
      return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
    }

    // only pending work orders can be checked in.
    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.PENDING,
        CONSTANTS.WORK_ORDER.STATUS.REJECTED,
        CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECKED_IN.FAILED);
    }

    // while cheking in the work order, status will be updated as in progress and actual date & time need to update.
    const woStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS
    );

    let payload = {};

    if (status) {
      payload = {
        internal_team_approval_status: true,
        work_order_status_id: woStatus?.id,
      };
    } else {
      payload = {
        internal_team_id: null,
        internal_team_approval_status: false,
      };
    }

    const updateWO = await db.WorkOrders.update(payload, {
      where: { id: work_order_id },
    });

    if (alert_id) {
      //delete alert for request check in
      await userService.deleteAlert({
        id: alert_id,
        headers: request?.headers,
      });
    }
    if (updateWO) {
      //send notification to internal team regarding request.
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: [loggedInUser?.id, woExist?.assigned_to_user_id],
        headers: request?.headers,
      });

      let loggedinUserData, assignedToUserData;
      if (userAPI) {
        loggedinUserData = await userAPI.find(
          (data) => data.id == loggedInUser?.id
        );
        assignedToUserData = await userAPI.find(
          (data) => data.id == woExist?.assigned_to_user_id
        );
      }
      if (status) {
        if (woExist?.work_order_status_id !== woStatus?.id) {
          // update order status to ongoing as, order's work order is started.
          const orderStatus = await common.getOrderStatusByTitle(
            CONSTANTS.ORDER_STATUS.ONGOING
          );
          if (
            woExist?.wo_order?.order_status?.title ===
            CONSTANTS.ORDER_STATUS.NEW
          ) {
            await db.Orders.update(
              { order_status_id: orderStatus?.id },
              { where: { id: woExist.wo_order.id } }
            );
          }
        }

        // get notification template from user service.
        const templateAPI = await userService.getTemplate({
          headers: request?.headers,
          action: [CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SE_SH_TAG_ACCEPTED],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        if (templateAPI) {
          const { title, body } = templateAPI[0];
          let replaceBody = await replaceData(
            body,
            "{USER_NAME}",
            `${loggedinUserData?.first_name} ${loggedinUserData?.last_name}`
          );
          replaceBody = await replaceData(
            replaceBody,
            "{WORK_ORDER_ID}",
            woExist?.actual_work_order_id
          );

          // add notification alert history
          const addAlertAPI = await userService.addAlerts({
            headers: request?.headers,
            sender_user_id: loggedInUser.id,
            title,
            body: replaceBody,
            action: CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SE_SH_TAG_ACCEPTED,
            receiver_and_send_to: [
              {
                receiver_user_id: assignedToUserData?.id,
                send_to: assignedToUserData?.device_token,
                loginData: assignedToUserData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (assignedToUserData?.loginHistory) {
            notificationResponse = await sendNotification(
              [assignedToUserData?.device_token],
              title,
              replaceBody
            );
            // update alert based on notification response
            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: request?.headers,
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        }
        return response.handler.success(
          MESSAGE.WORK_ORDER.CHECKED_IN.REQUEST_ACCEPTED_SE_SH
        );
      }
      // get notification template from user service.
      const templateAPI = await userService.getTemplate({
        headers: request?.headers,
        action: [CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SE_SH_TAG_REJECTED],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        let replaceBody = await replaceData(
          body,
          "{USER_NAME}",
          `${loggedinUserData?.first_name} ${loggedinUserData?.last_name}`
        );
        replaceBody = await replaceData(
          replaceBody,
          "{WORK_ORDER_ID}",
          woExist?.actual_work_order_id
        );

        // add notification alert history
        const addAlertAPI = await userService.addAlerts({
          headers: request?.headers,
          sender_user_id: loggedInUser.id,
          title,
          body: replaceBody,
          action: CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SE_SH_TAG_REJECTED,
          receiver_and_send_to: [
            {
              receiver_user_id: assignedToUserData?.id,
              send_to: assignedToUserData?.device_token,
              loginData: assignedToUserData?.loginHistory,
            },
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        let notificationResponse;
        if (assignedToUserData?.loginHistory) {
          notificationResponse = await sendNotification(
            [assignedToUserData?.device_token],
            title,
            replaceBody
          );
          // update alert based on notification response
          if (notificationResponse[0].success) {
            await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 1,
              response: notificationResponse[0].message,
            });
          } else {
            await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 0,
              response: notificationResponse[0].message.toString(),
            });
          }
        }
      }
      return response.handler.success(
        MESSAGE.WORK_ORDER.CHECKED_IN.REQUEST_REJECTED_SE_SH
      );
    } else {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.CHECKED_IN.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const reviewQuestionTouchPoint = async (request, response) => {
  try {
    const { work_order_id, task_id, question_details, touch_point_details } =
      request.body;

    const loggedInUser = await request?.login_user?.data;
    const approvedStatus =
      loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER
        ? CONSTANTS.TASK_STATUS.APPROVED_BY_SE
        : CONSTANTS.TASK_STATUS.APPROVED_BY_SH;
    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    // only internal team user can access. & their zone should be matched with work order's zone
    if (loggedInUser?.zone?.name !== woExist?.customer_data?.zone) {
      return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
    }

    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED,
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.REVIEW.FAILED);
    }

    const taskTouchPointIds = touch_point_details?.map((data) => data?.id);

    const taskData = await db.Tasks.findOne({
      where: { id: task_id, work_order_id },
      include: [
        {
          model: db.TaskTouchPoints,
          as: "task_touch_points",
          attributes: ATTRIBUTES.TASK_TOUCH_POINTS,
          include: [
            {
              model: db.TaskQuestions,
              as: "task_questions",
              attributes: ATTRIBUTES.TASK_QUESTIONS,
            },
          ],
        },
      ],
    });

    if (!taskData) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.REVIEW_QUESTION_TP.TASK_NOT_EXIST
      );
    }

    const promiseArr = [];
    const questionDetails = [];
    const touchpointDetails = [];

    if (question_details?.length) {
      let questionIds = {};
      taskData?.task_touch_points?.forEach((tp) => {
        tp?.task_questions?.forEach((que) => {
          questionIds[tp?.touch_point] = questionIds[tp?.touch_point]?.length
            ? [...questionIds[tp?.touch_point], que?.question_id]
            : [que?.question_id];
        });
      });

      //check if all question with touch point exists or not.
      const invlaidQueIds = [];

      question_details?.forEach((item) => {
        if (!questionIds?.[item?.touch_point]?.includes(item?.id)) {
          invlaidQueIds.push(`(${item?.id},${item?.touch_point})`);
        }
      });

      if (invlaidQueIds?.length) {
        return response.handler.badRequest(
          MESSAGE.WORK_ORDER.REVIEW_QUESTION_TP.QUESTION_NOT_EXIST(
            invlaidQueIds?.join(",")
          )
        );
      }

      question_details?.forEach((item) => {
        const tpData = taskData?.task_touch_points?.filter(
          (tp) => item?.touch_point === tp?.touch_point
        );
        if (tpData?.length) {
          const index = tpData[0]?.task_questions?.findIndex(
            (que) => que?.question_id === item?.id
          );

          if (index !== -1) {
            questionDetails.push({
              ...item,
              task_que_id: tpData[0]?.task_questions?.[index]?.id,
              touch_point_id: tpData?.[0]?.id,
              que_id: tpData[0]?.task_questions?.[index]?.question_id,
            });
          }
        }
      });
    }

    if (taskTouchPointIds?.length) {
      const touchPoints = taskData?.task_touch_points?.map(
        (tp) => tp?.touch_point
      );

      //check touch point exist in task or not
      const invalidTouchPoint = taskTouchPointIds?.filter(
        (item) => !touchPoints?.includes(item)
      );

      if (invalidTouchPoint?.length) {
        return response.handler.badRequest(
          MESSAGE.WORK_ORDER.REVIEW_QUESTION_TP.TOUCH_POINT_NOT_EXIST(
            invalidTouchPoint?.join(",")
          )
        );
      }

      touch_point_details?.forEach((item) => {
        const index = taskData?.task_touch_points?.findIndex(
          (tp) => item?.id === tp?.touch_point
        );

        if (index !== -1) {
          touchpointDetails.push({
            ...item,
            touch_point_id: taskData?.task_touch_points?.[index]?.id,
          });
        }
      });
    }

    questionDetails?.forEach((que) => {
      promiseArr.push(
        db.TaskQuestions.update(
          {
            status:
              que?.status === 0
                ? CONSTANTS.TASK_STATUS.REJECTED
                : approvedStatus,
            reason: que?.reason || "",
          },
          {
            where: { id: que?.task_que_id },
          }
        )
      );
    });

    const rejectedTouchPoint = [
      ...new Set(
        question_details
          ?.filter((item) => !item?.status)
          ?.map((item) => item?.touch_point)
      ),
    ];

    if (rejectedTouchPoint?.length) {
      promiseArr.push(
        db.TaskTouchPoints.update(
          { status: CONSTANTS.TASK_STATUS.REJECTED },
          {
            where: {
              task_id: taskData?.id,
              touch_point: { [Op.in]: rejectedTouchPoint },
            },
          }
        )
      );

      const taskTouchPointData = await db.TaskTouchPoints.findAll({
        where: {
          task_id: taskData?.id,
          touch_point: { [Op.in]: rejectedTouchPoint },
        },
      });

      if (taskTouchPointData?.length) {
        const touchPointReviewArr = taskTouchPointData?.map((item) => ({
          task_id,
          task_touch_point_id: item?.id,
          touch_point: item?.touch_point,
          assigned_to_user_id: woExist?.assigned_to_user_id,
          reviewer_user_id: loggedInUser?.id,
          status: CONSTANTS.TASK_STATUS.REJECTED,
        }));

        touchPointReviewArr?.length &&
          promiseArr.push(
            db.TaskTouchPointReview.bulkCreate(touchPointReviewArr)
          );
      }
    }
    touchpointDetails?.forEach((tp) => {
      promiseArr.push(
        db.TaskTouchPoints.update(
          {
            status:
              tp?.status === 0
                ? CONSTANTS.TASK_STATUS.REJECTED
                : approvedStatus,
            reason: tp?.reason || "",
          },
          {
            where: { id: tp?.touch_point_id },
          }
        )
      );
      promiseArr.push(
        tp?.status
          ? db.TaskQuestions.update(
              {
                status: approvedStatus,
              },
              {
                where: { task_touch_point_id: tp?.touch_point_id },
              }
            )
          : db.TaskQuestions.update(
              {
                status: null,
                reason: null,
              },
              {
                where: { task_touch_point_id: tp?.touch_point_id },
              }
            )
      );
    });

    if (touch_point_details?.filter((item) => item?.status === 0)?.length) {
      promiseArr.push(
        db.Tasks.update(
          {
            status: CONSTANTS.TASK_STATUS.REJECTED,
          },
          {
            where: { id: task_id },
          }
        )
      );
      promiseArr.push(
        db.TaskReview.create({
          task_id,
          assigned_to_user_id: woExist?.assigned_to_user_id,
          reviewer_user_id: loggedInUser?.id,
          status: CONSTANTS.TASK_STATUS.REJECTED,
        })
      );
    } else if (
      touch_point_details?.length === taskData?.task_touch_points?.length
    ) {
      promiseArr.push(
        db.Tasks.update(
          {
            status: approvedStatus,
          },
          {
            where: { id: task_id },
          }
        )
      );
      promiseArr.push(
        db.TaskReview.create({
          task_id,
          assigned_to_user_id: woExist?.assigned_to_user_id,
          reviewer_user_id: loggedInUser?.id,
          status: approvedStatus,
        })
      );
    }

    if (question_details?.filter((item) => item?.status === 0)?.length) {
      promiseArr.push(
        db.Tasks.update(
          {
            status: CONSTANTS.TASK_STATUS.REJECTED,
          },
          {
            where: { id: task_id },
          }
        )
      );
    }

    await Promise.all(promiseArr);

    const touchPointReviewer = [];
    const questionReview = [];

    questionDetails?.forEach((item) => {
      questionReview?.push({
        task_id,
        task_touch_point_id: item?.touch_point_id,
        touch_point: item?.touch_point,
        assigned_to_user_id: woExist?.assigned_to_user_id,
        reviewer_user_id: loggedInUser?.id,
        status: item?.status ? approvedStatus : CONSTANTS.TASK_STATUS.REJECTED,
        reason: item?.reason,
        task_question_id: item?.task_que_id,
        question_id: item?.que_id,
      });
    });
    touchpointDetails?.forEach((item) => {
      touchPointReviewer?.push({
        task_id,
        task_touch_point_id: item?.touch_point_id,
        touch_point: item?.id,
        assigned_to_user_id: woExist?.assigned_to_user_id,
        reviewer_user_id: loggedInUser?.id,
        status: item?.status ? approvedStatus : CONSTANTS.TASK_STATUS.REJECTED,
        reason: item?.reason,
      });
    });

    touchPointReviewer?.length &&
      (await db.TaskTouchPointReview.bulkCreate(touchPointReviewer));
    questionReview?.length &&
      (await db.TaskQuestionReview.bulkCreate(questionReview));

    //update touch point status based on question status
    if (question_details?.length) {
      const touchPointIds = question_details?.map((item) => item?.touch_point);
      if (touchPointIds?.length) {
        const touchPointBasedque = await db.TaskTouchPoints.findAll({
          where: {
            touch_point: { [Op.in]: touchPointIds },
            task_id: taskData?.id,
          },
          include: [
            {
              model: db.TaskQuestions,
              as: "task_questions",
              where: { status: { [Op.ne]: approvedStatus } },
              required: false,
            },
          ],
        });

        const completedTouchPointIds = touchPointBasedque
          ?.filter((item) => item?.task_questions?.length === 0)
          ?.map((item) => ({ id: item?.id, touch_point: item?.touch_point }));

        if (completedTouchPointIds?.length) {
          const ids = completedTouchPointIds?.map((item) => item?.id);
          const tpReviewArr = completedTouchPointIds?.map((item) => ({
            task_id,
            task_touch_point_id: item?.id,
            touch_point: item?.touch_point,
            assigned_to_user_id: woExist?.assigned_to_user_id,
            reviewer_user_id: loggedInUser?.id,
            status: approvedStatus,
          }));
          await db.TaskTouchPoints.update(
            { status: approvedStatus },
            { where: { id: { [Op.in]: ids } } }
          );
          tpReviewArr?.length &&
            (await db.TaskTouchPointReview.bulkCreate(tpReviewArr));
        }
      }
    }
    return response.handler.success(
      MESSAGE.WORK_ORDER.REVIEW_QUESTION_TP.UPDATED
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const seShCheckIn = async (request, response) => {
  try {
    const { work_order_id } = request.body;
    const loggedInUser = await request?.login_user?.data;
    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    // only pending work orders can be checked in.
    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED,
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.SE_SH_REVIEW.FAILED
      );
    }

    if (
      woExist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED &&
      loggedInUser?.role?.name === CONSTANTS?.ROLES?.NAMES?.SERVICE_ENGINEER
    ) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.REVIEW.ALREADY_APPROVED_BY_SERVICE_ENGINEER
      );
    }

    const amount = woExist?.wo_order?.amount;

    if (
      woExist?.work_order_status?.title ===
        CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL &&
      loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SERVICE_HEAD &&
      amount < CONSTANTS.ORDER_AMOUNT
    ) {
      return response.handler.badRequest(MESSAGE.INVALID_ACCESS);
    }

    const reviewerExistData = await db.WorkOrderReviewer.findOne({
      where: {
        work_order_id,
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
      },
    });

    if (reviewerExistData) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.SE_SH_REVIEW.ALREADY
      );
    }
    let payload = {
      work_order_id,
      reviewer_user_id: loggedInUser?.id,
      status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
    };
    const result = await db.WorkOrderReviewer.create(payload);
    return response.handler.success(
      MESSAGE.WORK_ORDER.SE_SH_REVIEW.SUCCESS,
      result
    );
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const cancelSeShCheckIn = async (request, response) => {
  try {
    const { id } = request.body;

    const woReviewerData = await db.WorkOrderReviewer.findOne({
      where: { id },
    });

    if (!woReviewerData) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.SE_SH_REVIEW.NOT_EXIST
      );
    }
    await db.WorkOrderReviewer.update(
      {
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.CANCELLED,
      },
      {
        where: { id },
      }
    );
    return response.handler.success(MESSAGE.WORK_ORDER.SE_SH_REVIEW.CANCEL);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const seShTagRequest = async (request, response) => {
  try {
    const { work_order_id, internal_team_id } = request.body;
    const loggedInUser = await request?.login_user?.data;
    // check if work order is exist or not
    const woExist = await common.getWorkOrderById(work_order_id);

    if (!woExist) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.NOT_EXIST);
    }

    // only pending work orders can be checked in.
    if (
      ![
        CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS,
        CONSTANTS.WORK_ORDER.STATUS.REJECTED,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE,
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
      ].includes(woExist?.work_order_status?.title)
    ) {
      return response.handler.badRequest(MESSAGE.WORK_ORDER.TAG_REQUEST.FAILED);
    }

    if (woExist?.internal_team_id) {
      return response.handler.badRequest(
        MESSAGE.WORK_ORDER.TAG_REQUEST.ALREADY
      );
    }

    const updateWO = await db.WorkOrders.update(
      { internal_team_id },
      {
        where: { id: work_order_id },
      }
    );

    //send notification to internal team regarding request.
    const userAPI = await userService.getUserDetailsByUserIds({
      user_ids: [internal_team_id, loggedInUser?.id],
      headers: request?.headers,
    });

    // get notification template from user service.
    const templateAPI = await userService.getTemplate({
      headers: request?.headers,
      action: [CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SUPERVISOR_TAG_REQUEST],
      type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
    });

    if (updateWO) {
      let loggedinUserData, internalTeamUserData;
      if (userAPI) {
        loggedinUserData = await userAPI.find(
          (data) => data.id == loggedInUser?.id
        );
        internalTeamUserData = await userAPI.find(
          (data) => data.id == internal_team_id
        );
      }

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        let replaceBody = await replaceData(
          body,
          "{USER_NAME}",
          `${loggedinUserData?.first_name} ${loggedinUserData?.last_name}`
        );
        replaceBody = await replaceData(
          replaceBody,
          "{WORK_ORDER_ID}",
          woExist?.actual_work_order_id
        );

        // add notification alert history
        const addAlertAPI = await userService.addAlerts({
          headers: request?.headers,
          sender_user_id: loggedInUser.id,
          title,
          body: replaceBody,
          action:
            CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.SUPERVISOR_TAG_REQUEST,
          receiver_and_send_to: [
            {
              receiver_user_id: internal_team_id,
              send_to: internalTeamUserData?.device_token,
              loginData: internalTeamUserData?.loginHistory,
              info: work_order_id,
            },
          ],
          type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
        });

        let notificationResponse;
        if (internalTeamUserData?.loginHistory) {
          notificationResponse = await sendNotification(
            [internalTeamUserData?.device_token],
            title,
            replaceBody
          );
          // update alert based on notification response
          if (notificationResponse[0].success) {
            await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 1,
              response: notificationResponse[0].message,
            });
          } else {
            await userService.updateAlert({
              headers: request?.headers,
              id: addAlertAPI.inserted_data[0].id,
              is_sent: 0,
              response: notificationResponse[0].message.toString(),
            });
          }
        }
      }

      return response.handler.success(
        MESSAGE.WORK_ORDER.TAG_REQUEST.REQUEST_SEND_TO_SE_SH
      );
    }
    return response.handler.badRequest(MESSAGE.WORK_ORDER.TAG_REQUEST.ERROR);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const checkForHandoverWorkorder = async (orderId) => {
  //check if handover work order already exist or not
  const handoverWorkOrderExist = await db.HandoverWorkOrders.findOne({
    where: { order_id: orderId },
  });

  if (handoverWorkOrderExist) {
    return;
  }

  //get completed work order status data
  const woStatus = await db.WorkOrderStatus.findOne({
    where: { title: CONSTANTS.WORK_ORDER.STATUS.COMPLETED },
    attributes: ATTRIBUTES.WORK_ORDER_STATUS,
  });

  //get quality check work order type data
  const woType = await db.WorkOrderTypes.findOne({
    where: { title: CONSTANTS.WORK_ORDER.TYPE.QUALITY_CHECK },
    attributes: ATTRIBUTES.WORK_ORDER_TYPES,
  });

  //get work order based on order where work order type is quality check and work order status is completed
  const orderData = await db.Orders.findOne({
    where: { id: orderId },
    attributes: ATTRIBUTES.ORDERS,
    include: [
      {
        model: db.WorkOrders,
        where: {
          work_order_status_id: woStatus?.id,
          work_order_type_id: woType?.id,
        },
        as: "work_order_data",
        required: true,
        attributes: ATTRIBUTES.WORK_ORDERS,
        order: [["updatedAt", "DESC"]],
      },
      {
        model: db.Customers,
        as: "customer_data",
        required: true,
        attributes: ATTRIBUTES.CUSTOMERS,
      },
    ],
  });

  if (orderData) {
    const workOrderIds = orderData?.work_order_data?.map((item) => item?.id);

    //get all the order scopes
    const orderScopes = await db.OrderScopes.findAll({
      where: { order_id: orderData?.id },
      attributes: ["id", "scope_id"],
    });

    //get all the work order scopes based on work order id
    const workOrderScopes = await db.WorkOrderScopes.findAll({
      where: { work_order_id: workOrderIds },
      attributes: ["id", "scope_id"],
    });

    const orderScopesFinal = [
      ...new Set(orderScopes?.map((item) => item?.scope_id)),
    ];
    const workOrderScopesFinal = [
      ...new Set(workOrderScopes?.map((item) => item?.scope_id)),
    ];

    //check if all the scope in order those are in work order scopes as well.
    const data = orderScopesFinal.every((item) =>
      workOrderScopesFinal.includes(item)
    );

    //if data is true then create handover work order
    if (data) {
      const workOrderData = orderData?.work_order_data?.[0];

      //get schedle date for handover work order which is one day ahed of work oroder
      const newScheduleDate = new Date();
      newScheduleDate.setDate(newScheduleDate.getDate() + 1);

      const nextDateStr = newScheduleDate.toISOString().split("T")[0];

      const pendingWoStatus = await db.WorkOrderStatus.findOne({
        where: { title: CONSTANTS.WORK_ORDER.STATUS.PENDING },
        attributes: ATTRIBUTES.WORK_ORDER_STATUS,
      });

      const handOverWorkOrderData = {
        order_id: orderId,
        customer_id: orderData?.customer_id,
        assigned_by_user_id: workOrderData?.assigned_by_user_id,
        assigned_by_user_role_id: workOrderData?.assigned_by_user_role_id,
        assigned_to_user_id: workOrderData?.assigned_to_user_id,
        assigned_to_user_role_id: workOrderData?.assigned_to_user_role_id,
        scheduled_start_date: nextDateStr,
        scheduled_start_time: workOrderData?.scheduled_start_time,
        work_order_status: pendingWoStatus?.id,
      };

      const addHandoverWorkorderData = await db.HandoverWorkOrders.create(
        handOverWorkOrderData
      );

      if (addHandoverWorkorderData) {
        const actual_work_order_id = (await getPadString(orderId, "ORD")) + "H";
        await db.HandoverWorkOrders.update(
          { actual_work_order_id: actual_work_order_id },
          { where: { id: addHandoverWorkorderData?.id } }
        );

        const zohoWoData = {
          zoho_order_id: orderData?.zoho_order_id,
          id: actual_work_order_id,
          email: orderData?.customer_data?.email,
        };

        //add handover work order to zoho
        await zoho.sendHandOverWorkOrderToZoho(zohoWoData);

        //update zoho stage to handover
        await zoho.checkAndUpdateZohoStage({
          zoho_order_id: orderData?.zoho_order_id,
        });

        //update qc_mode in zoho
        await zoho.updateQcModeInZoho({
          zoho_order_id: orderData?.zoho_order_id,
          mode: CONSTANTS.ZOHO.QC_MODE.APP,
        });

        //send email and sms to cutomer
        const customerEmail = orderData?.customer_data?.email;
        const customerMobile = orderData?.customer_data?.mobile;

        const encryptId = encrypt(addHandoverWorkorderData?.id?.toString());
        const url = await replaceData(
          CONSTANTS.HANDOVER_EMAIL_LINK,
          "{WORKORDER_ID}",
          encryptId
        );

        const link = await common.shortenWithTinyURL(url);

        const emailContent = `<p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 18px;">Hi ${orderData?.customer_data?.name},</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">Hope you're doing great! 👋</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re happy to share that your <b>Eternia windows have successfully passed our Quality Check (QC).</b>✅</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;line-height:1.5;">We’re now just <b>one step away</b> from handing over your order <b>${orderData?.actual_order_id}</b> and sharing your <b>official warranty card.</b></p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Please click the link below to begin the handover process:</p>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">${link}</p><br/>
          <p style="font-size: 14px;margin: 0;padding: 0;padding-bottom: 5px;">Need any help? Call us at 📞 <b>97699 40000</b> — we're here for you.</p>
				  <p style="font-size: 14px;margin: 0;padding: 0;"><b>– Team Eternia</b></p>`;

        const smsContent = `Hi ${orderData?.customer_data?.name},\n\nHope you're doing great! 👋\nWe’re happy to share that your Eternia windows have successfully passed our Quality Check (QC).✅\nWe’re now just one step away from handing over your order ${orderData?.actual_order_id} and sharing your official warranty card.\nPlease click the link below to begin the handover process:\n${link}\n\nNeed any help? Call us at 📞 97699 40000 — we're here for you.\n– Team Eternia`;

        const whatsappTemplate = [
          orderData?.customer_data?.name,
          orderData?.actual_order_id,
          link,
        ];

        await sendWhatsappMsg({
          campaignName:
            CONSTANTS.WHATSAPP.TEMPLATE_NAME.HANDOVER_WORK_ORDER_INVITATION,
          destination: customerMobile,
          templateParams: whatsappTemplate,
        });
        await sendSMS(`+91${customerMobile}`, smsContent);
        await sendMail(customerEmail, "Handover Sheet Link", emailContent);
      }
    }
  }
  return;
};

module.exports = {
  create,
  get,
  update,
  getAssignedWorkOrders,
  checkIn,
  checkOut,
  sendOtpForSubmitWorkOrder,
  verifyOtpForSubmitWorkOrder,
  getReviewList,
  review,
  getStatus,
  sendSurveyPdfToZoho,
  deleteWorkOrder,
  acceptRejectTagRequest,
  getAssignedWorkOrdersWithoutPagination,
  reviewQuestionTouchPoint,
  seShCheckIn,
  cancelSeShCheckIn,
  seShTagRequest,
};
