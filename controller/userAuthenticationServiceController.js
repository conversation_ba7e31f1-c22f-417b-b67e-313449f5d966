const { Op } = require("sequelize");
const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const { MESSAGE } = require("../helpers/messages");
const common = require("./commonFunctions.js");

const isAssignedWorkOrderCompleted = async (request, response) => {
  try {
    const { user_id, role } = request.body;

    /*
            if the role is fabricator, then we need to check orders
            else if role is supervisot, then we need to check work orders.
        */

    let whereCond = [{ is_active: 1 }, { is_deleted: 0 }];

    if (role?.name === CONSTANTS.ROLES.NAMES.FABRICATOR) {
      const orderCompleteStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.COMPLETED
      );

      whereCond.push({ fabricator_id: user_id });
      const assignedOrders = await db.Orders.count({
        where: whereCond,
        attributes: ATTRIBUTES.ORDERS,
      });

      whereCond.push({ order_status_id: orderCompleteStatus?.id });
      const completedOrders = await db.Orders.count({
        where: whereCond,
        attributes: ATTRIBUTES.ORDERS,
      });

      if (assignedOrders) {
        return response.handler.success(
          MESSAGE.ASSIGNED_ORDER_AND_WO.FOUND.SUCCESS,
          { totalCount: assignedOrders, completedCount: completedOrders }
        );
      } else {
        return response.handler.badRequest(
          MESSAGE.ASSIGNED_ORDER_AND_WO.FOUND.ERROR
        );
      }
    } else if (role.name === CONSTANTS.ROLES.NAMES.SUPERVISOR) {
      const workOrderCompleteStatus = await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.COMPLETED
      );

      whereCond.push({ assigned_to_user_id: user_id });
      const assignedWorkOrders = await db.WorkOrders.count({
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
      });

      whereCond.push({ work_order_status_id: workOrderCompleteStatus?.id });
      const completedWorkOrders = await db.WorkOrders.count({
        where: whereCond,
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
      });

      if (assignedWorkOrders && completedWorkOrders) {
        return response.handler.success(
          MESSAGE.ASSIGNED_ORDER_AND_WO.FOUND.SUCCESS,
          {
            totalCount: assignedWorkOrders,
            completedCount: completedWorkOrders,
          }
        );
      } else {
        return response.handler.badRequest(
          MESSAGE.ASSIGNED_ORDER_AND_WO.FOUND.ERROR
        );
      }
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getAssignedOrders = async (request, response) => {
  try {
    const { fabricator_ids } = request.query;
    const ordersData = await db.Orders.findAll({
      where: { fabricator_id: { [Op.in]: fabricator_ids } },
      attributes: ATTRIBUTES.ORDERS,
    });

    if (ordersData.length) {
      return response.handler.success(MESSAGE.ORDERS.FOUND.SUCCESS, {
        totalCount: ordersData.length,
        orders: ordersData,
      });
    } else {
      return response.handler.notFound(MESSAGE.ORDERS.FOUND.FAILED);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getAssignedOrdersByStatus = async (request, response) => {
  try {
    const { fabricator_ids, order_status_title } = request.query;
    const orderStatusId = await common.getOrderStatusByTitle(
      order_status_title
    );
    if (!orderStatusId) {
      return response.handler.notFound(MESSAGE.ORDERS.FOUND.FAILED);
    }
    const ordersData = await db.Orders.findAll({
      where: {
        fabricator_id: { [Op.in]: fabricator_ids },
        order_status_id: {
          [Op.in]: [orderStatusId?.id],
        },
      },
      attributes: ATTRIBUTES.ORDERS,
    });

    if (ordersData.length) {
      return response.handler.success(MESSAGE.ORDERS.FOUND.SUCCESS, {
        totalCount: ordersData.length,
        orders: ordersData,
      });
    } else {
      return response.handler.notFound(MESSAGE.ORDERS.FOUND.FAILED);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getAssignedWorkOrders = async (request, response) => {
  try {
    const { supervisor_ids } = request.query;
    const workOrdersData = await db.WorkOrders.findAll({
      where: {
        assigned_to_user_id: { [Op.in]: supervisor_ids },
        is_active: 1,
        is_deleted: 0,
      },
      attributes: ATTRIBUTES.WORK_ORDERS.filter(
        (value) => value !== "selfie_image_base64"
      ),
    });

    if (workOrdersData.length) {
      return response.handler.success(MESSAGE.WORK_ORDER.FOUND.SUCCESS, {
        totalCount: workOrdersData.length,
        work_orders: workOrdersData,
      });
    } else {
      return response.handler.notFound(MESSAGE.WORK_ORDER.FOUND.FAILED);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const getPointsEarningsOfUser = async (request, response) => {
  try {
    const userPointsEarnings = await db.PointBalance.findAll({
      attributes: ATTRIBUTES.POINT_BALANCE,
    });

    if (userPointsEarnings.length) {
      return response.handler.success(MESSAGE.POINTS.BALANCE.FOUND.SUCCESS, {
        totalCount: userPointsEarnings.length,
        points_earnings: userPointsEarnings,
      });
    } else {
      return response.handler.notFound(MESSAGE.POINTS.BALANCE.FOUND.ERROR);
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

const updateFabricatorInOrder = async (request, response) => {
  try {
    const { fabricator_email, fabricator_id } = request.body;

    // check if order data exist with fabricator email or not.
    const orderExistWithFabricator = await db.Orders.count({
      where: {
        fabricator_email,
        fabricator_id: 0,
      },
    });

    if (orderExistWithFabricator) {
      const updateOrder = await db.Orders.update(
        { fabricator_id },
        { where: { fabricator_email } }
      );

      if (updateOrder) {
        return response.handler.success(
          MESSAGE.ORDER.UPDATE_FABRICATOR.SUCCESS
        );
      } else {
        return response.handler.badRequest(
          MESSAGE.ORDER.UPDATE_FABRICATOR.ERROR
        );
      }
    } else {
      return response.handler.badRequest(
        MESSAGE.ORDER.UPDATE_FABRICATOR.NO_DATA
      );
    }
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  isAssignedWorkOrderCompleted,
  getAssignedOrders,
  getAssignedWorkOrders,
  getPointsEarningsOfUser,
  updateFabricatorInOrder,
  getAssignedOrdersByStatus,
};
