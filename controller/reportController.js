const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");

const getOrderReport = async (request, response) => {
    try {
        const { year } = request.query;

        const filterYear = (year && parseInt(year)) || new Date().getFullYear();

        const orderStatuses = await db.OrderStatus.findAll({
            where: { is_active: 1 },
            attributes: ["title"],
            raw: true
        });

        const statusNames = orderStatuses.map((status) => status.title);

        const result = await db.Orders.findAll({
            where: db.sequelize.literal(`YEAR(Orders.createdAt) = ${filterYear}`),
            attributes: [
                [db.sequelize.literal("MONTH(Orders.createdAt)"), "month"],
                ...statusNames.map((status) => [
                    db.sequelize.literal(`SUM(CASE WHEN order_status.title = "${status}" THEN 1 ELSE 0 END)`),
                    status,
                ]),
            ],
            include: [
                {
                    model: db.OrderStatus,
                    as: "order_status",
                    attributes: [],
                },
            ],
            group: [db.sequelize.literal("MONTH(Orders.createdAt)")],
            raw: true,
        });

        const resultObject = {};
        CONSTANTS.MONTH_NAMES_ARR.forEach((monthName, index) => {
            const month = index + 1;
            const monthData = result.find((row) => row.month === month) || {};

            resultObject[monthName] = {};
            statusNames.forEach((status) => {
                resultObject[monthName][status] = monthData[status] || 0;
            });
        });

        return response.handler.success(
            "Data fetched successfully.",
            resultObject
        );
    }
    catch (error) {
        return response.handler.serverError(error);
    }
}

module.exports = {
    getOrderReport,
}
