const { Op } = require("sequelize");

const db = require("../database/models");

const { CONSTANTS } = require("../helpers/constants");
const { ATTRIBUTES } = require("../helpers/dbAttributes");
const userService = require("../helpers/userAuthenticationServiceApis.js");
const { MESSAGE } = require("../helpers/messages.js");

const getDashboardData = async (request, response) => {
  try {
    const loggedInUser = await request?.login_user?.data;
    const { updated } = request?.query;

    if (!updated) {
      return response.handler.badRequest(MESSAGE.APK_UPDATE);
    }

    let data = {};

    let show_order_count = false;
    let show_work_order_count = false;
    let show_work_order_date_wise_count = false;
    let show_supervisor = false;
    let show_freelancer_count = false;
    let show_fabricator = false;
    let show_earnings = false;

    let orderWhereCond = [];
    let woWhereCond = [];

    if (
      [
        CONSTANTS.ROLES.NAMES.SERVICE_HEAD,
        CONSTANTS.ROLES.NAMES.SERVICE_ENGINEER,
      ].includes(loggedInUser?.role?.name)
    ) {
      show_order_count = true;
      show_work_order_count = true;
      show_supervisor = true;
      show_fabricator = true;
      show_freelancer_count = true;
    } else if (loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.FABRICATOR) {
      show_order_count = true;
      show_work_order_count = true;
      show_supervisor = true;
      show_freelancer_count = true;

      orderWhereCond.push({ fabricator_id: loggedInUser?.id });
    } else if (loggedInUser?.role?.name === CONSTANTS.ROLES.NAMES.SUPERVISOR) {
      show_work_order_count = true;
      show_work_order_date_wise_count = true;
      show_earnings = true;

      woWhereCond.push({ assigned_to_user_id: loggedInUser?.id });
    }

    if (show_order_count) {
      const order_data = await db.OrderStatus.findAll({
        where: [{ is_active: 1 }, { is_deleted: 0 }],
        attributes: [
          "title",
          [
            db.sequelize.fn("COUNT", db.sequelize.col("order_status.id")),
            "count",
          ],
        ],
        include: [
          {
            model: db.Orders,
            as: "order_status",
            required: false,
            where: orderWhereCond,
            attributes: [],
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: [],
                where: { zone: loggedInUser?.zone?.name },
              },
            ],
          },
        ],
        group: ["OrderStatus.id"],
      });

      if (order_data && order_data.length) {
        data.orders = { status_wise_count: order_data };
      }
    }

    if (show_work_order_count) {
      // Work order status wise count
      const work_order_data = await db.WorkOrderStatus.findAll({
        where: [{ is_active: 1 }, { is_deleted: 0 }],
        attributes: [
          "title",
          [
            db.sequelize.fn("COUNT", db.sequelize.col("work_order_status.id")),
            "count",
          ],
        ],
        include: [
          {
            model: db.WorkOrders,
            as: "work_order_status",
            required: false,
            where: [...woWhereCond, { is_active: 1 }, { is_deleted: 0 }],
            attributes: [],
            include: [
              {
                model: db.Orders,
                as: "order_data",
                required: true,
                where: orderWhereCond,
                attributes: [],
                include: [
                  {
                    model: db.Customers,
                    as: "customer_data",
                    required: true,
                    attributes: [],
                    // where: { zone: loggedInUser?.zone?.name },
                  },
                ],
              },
            ],
          },
        ],
        group: ["WorkOrderStatus.id"],
      });

      if (work_order_data && work_order_data.length) {
        data.work_orders = { status_wise_count: work_order_data };
      }

      // Work order total count
      const work_order_total_count = await db.WorkOrders.count({
        where: woWhereCond,
        include: [
          {
            model: db.Orders,
            as: "order_data",
            required: true,
            where: orderWhereCond,
            attributes: [],
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: [],
                // where: { zone: loggedInUser?.zone?.name },
              },
            ],
          },
        ],
      });

      data.work_orders = {
        ...data.work_orders,
        total_count: work_order_total_count,
      };
    }

    if (show_work_order_date_wise_count) {
      const today = await db.WorkOrders.count({
        where: {
          scheduled_start_date: { [Op.eq]: new Date() },
        },
      });

      const upcoming = await db.WorkOrders.count({
        where: {
          scheduled_start_date: { [Op.gt]: new Date() },
        },
      });

      data.work_orders = {
        ...data.work_orders,
        date_wise_count: { today: today, upcoming: upcoming },
      };
    }

    if (show_supervisor) {
      const assignedUserWoCount = await db.WorkOrders.findAll({
        attributes: [
          "assigned_to_user_id",
          [
            db.sequelize.fn("COUNT", "assigned_to_user_id"),
            "assigned_work_order_count",
          ],
        ],
        where: { is_active: 1, is_deleted: 0 },
        group: ["assigned_to_user_id"],
        raw: true,
        include: [
          {
            model: db.Orders,
            as: "order_data",
            required: true,
            where: orderWhereCond,
            attributes: [],
            include: [
              {
                model: db.Customers,
                as: "customer_data",
                required: true,
                attributes: [],
                where: { zone: loggedInUser?.zone?.name },
              },
            ],
          },
        ],
      });

      let assigned_to_user_ids = [];
      if (assignedUserWoCount && assignedUserWoCount.length) {
        assigned_to_user_ids = await assignedUserWoCount.map(
          (wo) => wo.assigned_to_user_id
        );

        const userAPI = await userService.getUserDetailsByUserIds({
          user_ids: assigned_to_user_ids,
          headers: request?.headers,
        });

        if (userAPI) {
          let result = [];
          for (let i = 0; i < assignedUserWoCount.length; i++) {
            const user = await userAPI.find(
              (api) => api.id === assignedUserWoCount[i].assigned_to_user_id
            );

            if (user) {
              result.push({
                user_id: user.id,
                name: `${user?.first_name} ${user?.last_name}`,
                email: user?.email,
                profile_image_url: user?.profile_image_url,
                assigned_work_order_count:
                  assignedUserWoCount[i].assigned_work_order_count,
                zone: user?.address?.[0]?.zone,
              });
            }
          }

          //sort data in desc order based on assigned wo count
          result = result.sort(
            (a, b) => b.assigned_work_order_count - a.assigned_work_order_count
          );

          //keep only first 5 records
          result = result.slice(0, CONSTANTS.DASHBOARD.LIST_LENGTH);
          data.supervisors = result;
        }
      }
    }

    if (show_fabricator) {
      const assignedUserOrderCount = await db.Orders.findAll({
        attributes: [
          "fabricator_id",
          [db.sequelize.fn("COUNT", "fabricator_id"), "assigned_order_count"],
        ],
        group: ["fabricator_id"],
        raw: true,
        include: [
          {
            model: db.Customers,
            as: "customer_data",
            required: true,
            attributes: [],
            where: { zone: loggedInUser?.zone?.name },
          },
        ],
      });

      let fabricator_user_ids = [];
      if (assignedUserOrderCount && assignedUserOrderCount.length) {
        fabricator_user_ids = await assignedUserOrderCount.map(
          (o) => o.fabricator_id
        );

        const userAPI = await userService.getUserDetailsByUserIds({
          user_ids: fabricator_user_ids,
          headers: request?.headers,
        });

        if (userAPI) {
          let result = [];
          for (let i = 0; i < assignedUserOrderCount.length; i++) {
            const user = await userAPI.find(
              (api) => api.id === assignedUserOrderCount[i].fabricator_id
            );

            if (user) {
              result.push({
                user_id: user.id,
                name: `${user?.first_name} ${user?.last_name}`,
                email: user?.email,
                profile_image_url: user?.profile_image_url,
                assigned_order_count:
                  assignedUserOrderCount[i].assigned_order_count,
                zone: user.address?.[0]?.zone,
              });
            }
          }

          //sort data in desc order based on assigned order count
          result = result.sort(
            (a, b) => b.assigned_order_count - a.assigned_order_count
          );

          //keep only first 5 records
          result = result.slice(0, CONSTANTS.DASHBOARD.LIST_LENGTH);
          data.fabricator = result;
        }
      }
    }

    if (show_freelancer_count) {
      let freelancerCount = 0;
      const userAPI = await userService.getUsersByZone({
        zone: loggedInUser?.zone?.name,
        headers: request?.headers,
        roles: [CONSTANTS.ROLES.NAMES.SUPERVISOR],
        other_data: { supervisor_type: "Freelancer" },
      });

      if (userAPI) {
        freelancerCount = userAPI?.length;
      }

      data.freelancer_count = freelancerCount;
    }

    if (show_earnings) {
      const earnings = await db.PointBalance.findOne({
        where: { user_id: loggedInUser.id },
        attributes: ATTRIBUTES.POINT_BALANCE,
        raw: true,
      });

      data.earnings = earnings || {};
    }

    return response.handler.success("Data fetched successfully.", data);
  } catch (error) {
    return response.handler.serverError(error);
  }
};

module.exports = {
  getDashboardData,
};
