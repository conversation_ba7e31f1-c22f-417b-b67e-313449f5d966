"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("master_orders", "committed_date_of_delivery", {
      type: Sequelize.DATEONLY,
      after: "owner_email",
    });

    await queryInterface.addColumn("master_orders", "date_of_installation_complete", {
      type: Sequelize.DATEONLY,
      after: "committed_date_of_delivery",
    });

    await queryInterface.addColumn("master_orders", "warranty_id", {
      type: Sequelize.STRING,
      after: "date_of_installation_complete",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
