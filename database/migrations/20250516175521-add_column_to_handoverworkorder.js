"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("handover_work_orders", "image_key", {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn("handover_work_orders", "image_url", {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("handover_work_orders", "image_key");
    await queryInterface.removeColumn("handover_work_orders", "image_url");
  },
};
