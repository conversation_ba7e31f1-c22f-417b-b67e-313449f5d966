"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("master_orders", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      zoho_order_id: {
        type: Sequelize.STRING(30),
        unique: true,
        comment: "Opportunity Id",
      },
      deal_name: {
        type: Sequelize.STRING(50),
        comment: "Opportunity/Order Name",
      },
      customer_id: {
        type: Sequelize.STRING(30),
        comment: "Contact_Name > id",
      },
      customer_name: {
        type: Sequelize.STRING(50),
        comment: "Contact_Name > name",
      },
      email: {
        type: Sequelize.STRING(100),
        comment: "Customer's Email",
      },
      mobile: {
        type: Sequelize.STRING(15),
        comment: "Customer's Mobile",
      },
      phone: {
        type: Sequelize.STRING(15),
        comment: "Customer's Phone",
      },
      gender: {
        type: Sequelize.STRING(10),
        comment: "Customer's gender",
      },
      street_name: {
        type: Sequelize.STRING,
        comment: "Customer's Street Name",
      },
      city: {
        type: Sequelize.STRING(20),
        comment: "Customer's City",
      },
      zip_code: {
        type: Sequelize.STRING(10),
        comment: "Customer's Zip Code",
      },
      state_name: {
        type: Sequelize.STRING(20),
        comment: "Customer's State/Location",
      },
      region: {
        type: Sequelize.STRING(10),
        comment: "Customer's Region(Zone)",
      },
      type_of_house: {
        type: Sequelize.STRING(30),
        comment: "Customer's Universe",
      },
      owner_name: {
        type: Sequelize.STRING(50),
        comment: "Fabricator Name (Owner > name)",
      },
      owner_id: {
        type: Sequelize.STRING(30),
        comment: "Fabricator Id (Owner > id)",
      },
      owner_email: {
        type: Sequelize.STRING(100),
        comment: "Fabricator Email (Owner > email)",
      },
      created_time: {
        type: Sequelize.DATE,
        comment: "Opportunity's created date & time",
      },
      modified_time: {
        type: Sequelize.DATE,
        comment: "Opportunity's modified date & time",
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("master_orders");
  }
};
