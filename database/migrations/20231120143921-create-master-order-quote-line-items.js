"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("master_order_quote_line_items", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      master_order_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "master_orders",
          key: "id",
        },
      },
      zoho_quote_line_item_id: {
        type: Sequelize.STRING(30),
      },
      room: {
        type: Sequelize.STRING,
        comment: "Space",
      },
      product_name: {
        type: Sequelize.STRING,
        comment: "<PERSON><PERSON>'s name",
      },
      specification: {
        type: Sequelize.STRING,
      },
      quantity: {
        type: Sequelize.INTEGER,
        comment: "<PERSON>ope's quantity",
      },
      name: {
        type: Sequelize.STRING,
      },
      created_time: {
        type: Sequelize.DATE,
        comment: "Zoho Quote line item created date & time",
      },
      modified_time: {
        type: Sequelize.DATE,
        comment: "Zoho Quote line item modified date & time",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("master_order_quote_line_items");
  }
};
