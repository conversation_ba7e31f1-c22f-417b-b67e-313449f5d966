"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("report_tags", "is_mandatory", {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    });
    await queryInterface.addColumn("report_tags", "number", {
      type: Sequelize.INTEGER,
      defaultValue: null,
    });
  },

  async down(queryInterface, Sequelize) {},
};
