"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("task_video_answers", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      task_question_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "task_questions",
          key: "id",
        },
      },
      signed_url: {
        type: Sequelize.TEXT("medium"),
      },
      signed_url_exipration_time: {
        type: Sequelize.INTEGER,
        comment: "time in minutes",
      },
      video_key: {
        type: Sequelize.STRING
      },
      video_url: {
        type: Sequelize.STRING
      },
      is_video_uploaded: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: 1
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("task_video_answers");
  }
};
