"use strict";

const { CONSTANTS } = require("../../helpers/constants");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("work_order_type_points", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      work_order_type_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "work_order_types",
          key: "id",
        },
      },
      points_to_assign: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      transaction_type: {
        type: Sequelize.ENUM(CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT, CONSTANTS.POINTS.TRANSACTION_TYPE.DEBIT)
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: 1
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("work_order_type_points");
  }
};
