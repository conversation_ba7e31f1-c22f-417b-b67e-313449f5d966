'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("questions", "report_tag_id", {
      type: Sequelize.INTEGER,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "report_tags",
        key: "id",
      },
      after: "question_type_id",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
