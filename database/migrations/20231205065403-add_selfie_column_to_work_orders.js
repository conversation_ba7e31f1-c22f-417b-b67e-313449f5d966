"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("work_orders", "selfie_image_key", {
      type: Sequelize.STRING,
      after: "is_cus_otp_verified"
    });

    await queryInterface.addColumn("work_orders", "selfie_image_url", {
      type: Sequelize.STRING,
      after: "selfie_image_key"
    });

    await queryInterface.addColumn("work_orders", "selfie_image_base64", {
      type: Sequelize.TEXT("long"),
      after: "selfie_image_url"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
