"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("handover_questions", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      order: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      title: {
        type: Sequelize.TEXT("medium"),
        allowNull: false,
      },
      min_number: {
        allowNull: true,
        type: Sequelize.INTEGER,
      },
      max_number: {
        allowNull: true,
        type: Sequelize.INTEGER,
      },
      question_type: {
        type: Sequelize.ENUM("Single", "Multiple", "Descriptive", "Rating"),
        allowNull: false,
      },
      attachment_required: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("handover_questions");
  },
};
