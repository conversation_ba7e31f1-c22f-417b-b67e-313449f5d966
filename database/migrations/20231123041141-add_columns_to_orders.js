"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("orders", "zoho_order_id", {
      type: Sequelize.STRING(30),
      after: "actual_order_id"
    });

    await queryInterface.addColumn("orders", "fabricator_email", {
      type: Sequelize.STRING(30),
      after: "fabricator_id"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
