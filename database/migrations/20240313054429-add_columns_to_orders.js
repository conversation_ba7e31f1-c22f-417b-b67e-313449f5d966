'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("orders", "zoho_warranty_id", {
      type: Sequelize.STRING,
      after: "order_status_id",
    });

    await queryInterface.addColumn("orders", "profile_warranty_end_date", {
      type: Sequelize.DATEONLY,
      after: "zoho_warranty_id",
      comment: "added 12 year in committed_date_of_delivery/date_of_installation_complete whichever is earlier.",
    });

    await queryInterface.addColumn("orders", "hardware_warranty_end_date", {
      type: Sequelize.DATEONLY,
      after: "profile_warranty_end_date",
      comment: "added 2 year in committed_date_of_delivery/date_of_installation_complete whichever is earlier.",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
