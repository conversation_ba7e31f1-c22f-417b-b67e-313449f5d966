"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("work_order_scopes", "task_count", {
      type: Sequelize.INTEGER,
      after: "count",
      comment: "(Survey -> QC -> Handover) parent workorder's  completed task count",
      defaultValue: 1,
    });

    await queryInterface.sequelize.query(`UPDATE work_order_scopes SET task_count = count`);
  },

  async down(queryInterface, Sequelize) {

  }
};
