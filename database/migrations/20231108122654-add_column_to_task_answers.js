"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("task_answers", "is_active", {
      type: Sequelize.BOOLEAN,
      defaultValue: 1,
      after: "answer"
    });

    await queryInterface.addColumn("task_answers", "is_deleted", {
      type: Sequelize.BOOLEAN,
      defaultValue: 0,
      after: "is_active"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
