"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      "handover_work_orders",
      "work_order_status"
    );
    await queryInterface.addColumn(
      "handover_work_orders",
      "work_order_status",
      {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "work_order_status",
          key: "id",
        },
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      "handover_work_orders",
      "work_order_status",
      {
        type: Sequelize.ENUM("Pending", "Inprogress", "Overdue", "Completed"),
        defaultValue: "Pending",
      }
    );
    await queryInterface.removeColumn(
      "handover_work_orders",
      "work_order_status"
    );
  },
};
