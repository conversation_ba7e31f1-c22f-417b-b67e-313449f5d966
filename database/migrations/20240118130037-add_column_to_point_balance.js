"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("point_balance", "redeemed_points", {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      after: "total_earnings"
    });

    await queryInterface.addColumn("point_balance", "redeemed_earnings", {
      type: Sequelize.DECIMAL(10, 2),
      defaultValue: 0,
      after: "redeemed_points"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
