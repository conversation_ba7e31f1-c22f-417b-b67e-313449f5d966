"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("question_options", "is_active", {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      after: "title",
    });

    await queryInterface.addColumn("question_options", "is_deleted", {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      after: "is_active",
    });

    // Update existing records to have is_active = true and is_deleted = false
    await queryInterface.sequelize.query(`
      UPDATE question_options 
      SET is_active = true, 
          is_deleted = false
      WHERE is_active IS NULL
      AND is_deleted IS NULL
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("question_options", "is_active");
    await queryInterface.removeColumn("question_options", "is_deleted");
  },
};
