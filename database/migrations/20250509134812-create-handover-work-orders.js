"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("handover_work_orders", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      order_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "orders",
          key: "id",
        },
      },
      customer_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "customers",
          key: "id",
        },
      },
      actual_work_order_id: {
        type: Sequelize.STRING,
      },
      work_order_status: {
        type: Sequelize.ENUM("Pending", "Inprogress", "Overdue", "Completed"),
        defaultValue: "Pending",
      },
      assigned_to_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_to_user_role_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_by_user_role_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      scheduled_start_date: {
        type: Sequelize.DATEONLY,
      },
      scheduled_start_time: {
        type: Sequelize.STRING(20),
      },
      actual_start_date: {
        type: Sequelize.DATEONLY,
      },
      actual_start_time: {
        type: Sequelize.STRING(20),
      },
      actual_end_datetime: {
        type: Sequelize.DATE,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("handover_work_orders");
  },
};
