"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn("handover_questions", "question_type", {
      type: Sequelize.ENUM(
        "Single",
        "Multiple",
        "Descriptive",
        "Rating",
        "Boolean"
      ),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn("handover_questions", "question_type", {
      type: Sequelize.ENUM("Single", "Multiple", "Descriptive", "Rating"),
      allowNull: false,
    });
  },
};
