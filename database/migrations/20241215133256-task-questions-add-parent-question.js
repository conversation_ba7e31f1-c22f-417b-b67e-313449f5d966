"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("task_questions", "isSubquestion", {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      after: "question_id",
    });

    await queryInterface.addColumn("task_questions", "parent_question_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "questions",
        key: "id",
      },
      after: "isSubquestion",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("task_questions", "isSubquestion");
    await queryInterface.removeColumn("task_questions", "parent_question_id");
  },
};
