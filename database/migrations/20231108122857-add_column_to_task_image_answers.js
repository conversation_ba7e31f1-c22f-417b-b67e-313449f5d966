'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("task_image_answers", "is_active", {
      type: Sequelize.BOOLEAN,
      defaultValue: 1,
      after: "base64"
    });

    await queryInterface.addColumn("task_image_answers", "is_deleted", {
      type: Sequelize.BOOLEAN,
      defaultValue: 0,
      after: "is_active"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
