"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("question_mapping_set", "question_mapping_touch_point_id", {
      type: Sequelize.INTEGER,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "question_mapping_touch_points",
        key: "id",
      },
      after: "id"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
