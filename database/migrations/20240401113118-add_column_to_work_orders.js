"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("work_orders", "zoho_status", {
      type: Sequelize.STRING,
      after: "rejected_count",
    });

    await queryInterface.addColumn("work_orders", "customer_feedback", {
      type: Sequelize.TEXT("medium"),
      after: "zoho_status",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
