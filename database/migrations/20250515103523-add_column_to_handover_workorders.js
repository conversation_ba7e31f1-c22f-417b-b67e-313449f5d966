"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("handover_work_orders", "check_out_otp", {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addColumn(
      "handover_work_orders",
      "check_out_otp_created_at",
      {
        type: Sequelize.DATE,
        allowNull: true,
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("handover_work_orders", "check_out_otp");
    await queryInterface.removeColumn(
      "handover_work_orders",
      "check_out_otp_created_at"
    );
  },
};
