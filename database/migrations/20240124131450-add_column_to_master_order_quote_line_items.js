"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("master_order_quote_line_items", "height", {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      comment: "scope's height",
      after: "name",
    });

    await queryInterface.addColumn("master_order_quote_line_items", "width", {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      comment: "scope's width",
      after: "height",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
