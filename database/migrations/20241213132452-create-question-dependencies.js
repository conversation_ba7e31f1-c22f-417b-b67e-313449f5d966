"use strict";
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("question_dependencies", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      parent_question_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "questions",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      child_question_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "questions",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      trigger_conditions: {
        type: Sequelize.JSON,
        allowNull: false,
        defaultValue: [],
      },
      logical_operator: {
        type: Sequelize.ENUM("AND", "OR"),
        allowNull: false,
        defaultValue: "AND",
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      "question_dependencies",
      ["parent_question_id"],
      {
        name: "idx_question_dependencies_parent",
      }
    );
    await queryInterface.addIndex(
      "question_dependencies",
      ["child_question_id"],
      {
        name: "idx_question_dependencies_child",
      }
    );
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("question_dependencies");
  },
};
