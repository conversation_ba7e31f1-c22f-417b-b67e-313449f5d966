"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("tasks", "question_mapping_id", {
      type: Sequelize.INTEGER,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "question_mapping",
        key: "id",
      },
      after: "work_order_scope_mapping_id"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
