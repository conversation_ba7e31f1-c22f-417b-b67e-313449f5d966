"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("task_image_answers", "task_id", {
      type: Sequelize.INTEGER,
      defaultValue: null,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "tasks",
        key: "id",
      },
    });

    await queryInterface.addColumn("task_image_answers", "report_tag_id", {
      type: Sequelize.INTEGER,
      defaultValue: null,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "report_tags",
        key: "id",
      },
    });
  },

  async down(queryInterface, Sequelize) {},
};
