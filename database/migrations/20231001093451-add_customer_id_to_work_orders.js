"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("work_orders", "customer_id", {
      type: Sequelize.INTEGER,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "customers",
        key: "id",
      },
      after: "order_id"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
