"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("master_order_quote_line_items", "is_active", {
      type: Sequelize.BOOLEAN,
      after: "created_time",
      defaultValue: 1,
    });

    await queryInterface.addColumn("master_order_quote_line_items", "is_deleted", {
      type: Sequelize.BOOLEAN,
      after: "is_active",
      defaultValue: 0,
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
