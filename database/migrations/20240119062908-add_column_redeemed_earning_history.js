"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("redeemed_earning_history", "point_rate_id", {
      type: Sequelize.INTEGER,
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      references: {
        model: "point_rates",
        key: "id",
      },
      after: "note",
      comment: "to identify how much redeemed against amount"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
