"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("scopes", "height", {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      after: "source",
    });

    await queryInterface.addColumn("scopes", "width", {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      after: "height",
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
