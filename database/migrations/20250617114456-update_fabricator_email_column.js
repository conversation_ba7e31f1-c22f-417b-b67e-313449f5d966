"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn("orders", "fabricator_email", {
      type: Sequelize.STRING(100),
      after: "fabricator_id",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn("orders", "fabricator_email", {
      type: Sequelize.STRING(30),
      after: "fabricator_id",
    });
  },
};
