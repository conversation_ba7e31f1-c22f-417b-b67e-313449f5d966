"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Adding the is_attachment_required column
    await queryInterface.addColumn("questions", "is_attachment_required", {
      type: Sequelize.JSON,
      allowNull: true,
    });

    // Adding the is_subquestion column
    await queryInterface.addColumn("questions", "is_subquestion", {
      type: Sequelize.BOOLEAN,
      defaultValue: 0,
    });

    // Adding the video_required column
    await queryInterface.addColumn("questions", "video_required", {
      type: Sequelize.JSON,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Removing the is_attachment_required column
    await queryInterface.removeColumn("questions", "is_attachment_required");

    // Removing the is_subquestion column
    await queryInterface.removeColumn("questions", "is_subquestion");

    // Removing the video_required column
    await queryInterface.removeColumn("questions", "video_required");
  },
};
