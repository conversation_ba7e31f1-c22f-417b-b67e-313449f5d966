"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("point_history", "is_read", {
      type: Sequelize.BOOLEAN,
      defaultValue: 0,
      comment: "to identify if this data is considered in balance table or not.",
      after: "point_rate_id"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
