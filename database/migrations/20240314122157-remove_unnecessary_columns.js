"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // await queryInterface.removeColumn("order_scopes", "actual_scope_id");
    // await queryInterface.removeColumn(
    //   "master_order_quote_line_items",
    //   "master_quote_header_cpq_id"
    // );
    // await queryInterface.removeColumn(
    //   "master_order_quote_line_items",
    //   "modified_time"
    // );
    // await queryInterface.dropTable("master_order_quote_header_cpq");
    // await queryInterface.removeColumn("question_mapping", "touch_point");
    // await queryInterface.removeColumn("question_mapping_set", "question_mapping_id");
  },

  async down(queryInterface, Sequelize) {},
};
