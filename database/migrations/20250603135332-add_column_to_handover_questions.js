"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("handover_questions", "is_checked", {
      type: Sequelize.BOOLEAN,
      defaultValue: 1,
    });
    await queryInterface.addColumn("handover_questions", "is_mandatory", {
      type: Sequelize.BOOLEAN,
      defaultValue: 1,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("handover_questions", "is_checked");
    await queryInterface.removeColumn("handover_questions", "is_mandatory");
  },
};
