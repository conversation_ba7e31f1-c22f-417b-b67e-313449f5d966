"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("otps", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      work_order_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "work_orders",
          key: "id",
        },
      },
      customer_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "customers",
          key: "id",
        },
      },
      country_code: {
        type: Sequelize.STRING(5),
      },
      mobile: {
        type: Sequelize.STRING(15),
      },
      otp: {
        type: Sequelize.STRING
      },
      is_otp_verified: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      action: {
        type: Sequelize.STRING
      },
      otp_send_to: {
        type: Sequelize.ENUM("MOBILE", "EMAIL"),
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("otps");
  }
};
