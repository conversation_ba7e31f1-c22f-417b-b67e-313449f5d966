"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn("work_orders", "scheduled_start_datetime");
    await queryInterface.removeColumn("work_orders", "actual_start_datetime");

    await queryInterface.addColumn("work_orders", "scheduled_start_date", {
      type: Sequelize.DATEONLY,
      after: "assigned_by_user_role_id"
    });

    await queryInterface.addColumn("work_orders", "scheduled_start_time", {
      type: Sequelize.STRING(20),
      after: "scheduled_start_date"
    });

    await queryInterface.addColumn("work_orders", "actual_start_date", {
      type: Sequelize.DATEONLY,
      after: "scheduled_due_datetime"
    });

    await queryInterface.addColumn("work_orders", "actual_start_time", {
      type: Sequelize.STRING(20),
      after: "actual_start_date"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
