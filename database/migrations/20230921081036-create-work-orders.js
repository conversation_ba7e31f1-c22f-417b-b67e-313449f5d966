"use strict";
/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("work_orders", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      order_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "orders",
          key: "id",
        },
      },
      work_order_status_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "work_order_status",
          key: "id",
        },
      },
      work_order_type_id: {
        type: Sequelize.INTEGER,
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        references: {
          model: "work_order_types",
          key: "id",
        },
      },
      assigned_to_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_to_user_role_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_by_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      assigned_by_user_role_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      scheduled_start_datetime: {
        type: Sequelize.DATE
      },
      scheduled_duration: {
        type: Sequelize.INTEGER(20)
      },
      scheduled_due_datetime: {
        type: Sequelize.DATE
      },
      actual_start_datetime: {
        type: Sequelize.DATE
      },
      actual_duration: {
        type: Sequelize.INTEGER(20)
      },
      actual_due_datetime: {
        type: Sequelize.DATE
      },
      instructions: {
        type: Sequelize.TEXT
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("work_orders");
  }
};
