"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn("scopes", "title");
    await queryInterface.addColumn("scopes", "model", {
      type: Sequelize.STRING,
      after: "id"
    });
    await queryInterface.addColumn("scopes", "name", {
      type: Sequelize.STRING,
      after: "model"
    });
  },

  async down(queryInterface, Sequelize) {

  }
};
