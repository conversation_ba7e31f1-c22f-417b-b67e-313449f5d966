"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class HandoverImageNaswers extends Model {
    static associate(models) {}
  }
  HandoverImageNaswers.init(
    {
      question_id: DataTypes.INTEGER,
      work_order_id: DataTypes.INTEGER,
      image_key: DataTypes.STRING,
      image_url: DataTypes.STRING,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "HandoverImageNaswers",
      tableName: "handover_image_answers",
    }
  );
  return HandoverImageNaswers;
};
