"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class OrderSpaces extends Model {
    static associate(models) {
      this.belongsTo(models.Spaces, { as: "space_data", foreignKey: "space_id" });

      this.hasMany(models.OrderScopes, { as: "order_scopes", foreignKey: "order_space_mapping_id" });
    }
  }
  OrderSpaces.init({
    order_id: DataTypes.INTEGER,
    space_id: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "OrderSpaces",
    tableName: "order_spaces",
  });
  return OrderSpaces;
};
