"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class HandoverWorkOrderAnswers extends Model {
    static associate(models) {}
  }
  HandoverWorkOrderAnswers.init(
    {
      question_id: DataTypes.INTEGER,
      workorder_id: DataTypes.INTEGER,
      answer: DataTypes.STRING,
      comment: DataTypes.STRING,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "HandoverWorkOrderAnswers",
      tableName: "handover_workorder_answers",
    }
  );
  return HandoverWorkOrderAnswers;
};
