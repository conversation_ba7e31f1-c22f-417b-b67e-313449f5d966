"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class RedeemedEarningHistory extends Model {
    static associate(models) {

    }
  }
  RedeemedEarningHistory.init({
    user_id: DataTypes.INTEGER,
    redeem_amount: DataTypes.DECIMAL(10, 2),
    note: DataTypes.TEXT("medium"),
    point_rate_id: DataTypes.INTEGER,
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "RedeemedEarningHistory",
    tableName: "redeemed_earning_history",
  });
  return RedeemedEarningHistory;
};
