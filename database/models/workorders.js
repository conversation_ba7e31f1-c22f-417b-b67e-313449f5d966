"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrders extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderStatus, {
        as: "work_order_status",
        foreignKey: "work_order_status_id",
      });
      this.belongsTo(models.WorkOrderTypes, {
        as: "work_order_type",
        foreignKey: "work_order_type_id",
      });

      this.belongsTo(models.Orders, {
        as: "order_data",
        foreignKey: "order_id",
      });

      this.hasMany(models.WorkOrderSpaces, {
        as: "work_order_spaces",
        foreignKey: "work_order_id",
      });

      this.belongsTo(models.Customers, {
        as: "customer_data",
        foreignKey: "customer_id",
      });

      this.hasMany(models.Tasks, {
        as: "work_order_task",
        foreignKey: "work_order_id",
      });

      this.hasMany(models.WorkOrderReview, {
        as: "work_order_review",
        foreignKey: "work_order_id",
      });

      this.belongsTo(models.Orders, { as: "wo_order", foreignKey: "order_id" });

      this.hasMany(models.WorkOrderScopes, {
        as: "work_order_scopes",
        foreignKey: "work_order_id",
      });

      this.hasOne(models.Warranty, {
        as: "warranty_data",
        foreignKey: "work_order_id",
      });

      this.hasMany(models.WorkOrderReviewer, {
        as: "work_order_reviewer",
        foreignKey: "work_order_id",
      });
    }
  }
  WorkOrders.init(
    {
      actual_work_order_id: DataTypes.STRING,
      inserted_zoho_wo_id: DataTypes.STRING,
      order_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      customer_id: DataTypes.INTEGER,
      work_order_status_id: DataTypes.INTEGER,
      work_order_type_id: DataTypes.INTEGER,
      assigned_to_user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_to_user_role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_by_user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_by_user_role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      scheduled_start_date: DataTypes.DATEONLY,
      scheduled_start_time: DataTypes.STRING(20),
      scheduled_duration: DataTypes.STRING(20),
      scheduled_due_datetime: DataTypes.DATE,
      actual_start_date: DataTypes.DATEONLY,
      actual_start_time: DataTypes.STRING(20),
      actual_duration: DataTypes.STRING(20),
      actual_due_datetime: DataTypes.DATE,
      actual_end_datetime: DataTypes.DATE,
      instructions: DataTypes.TEXT,
      is_cus_otp_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      internal_team_approval_status: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      selfie_image_key: DataTypes.STRING,
      selfie_image_url: DataTypes.STRING,
      selfie_image_base64: DataTypes.TEXT("long"),
      rejected_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      internal_team_id: {
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      zoho_status: DataTypes.STRING,
      customer_feedback: DataTypes.TEXT("medium"),
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "WorkOrders",
      tableName: "work_orders",
    }
  );
  return WorkOrders;
};
