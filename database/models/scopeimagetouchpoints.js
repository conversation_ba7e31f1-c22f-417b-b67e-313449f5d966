"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class ScopeImageTouchPoints extends Model {
    static associate(models) {

    }
  }
  ScopeImageTouchPoints.init({
    scope_image_id: DataTypes.INTEGER,
    touch_point: DataTypes.INTEGER,
    x_coordinate: DataTypes.INTEGER,
    y_coordinate: DataTypes.INTEGER,
    title: DataTypes.STRING,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "ScopeImageTouchPoints",
    tableName: "scope_image_touch_points",
  });
  return ScopeImageTouchPoints;
};
