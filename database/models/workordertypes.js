"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderTypes extends Model {
    static associate(models) {
      this.hasMany(models.WorkOrderTypeSkills, { as: "wo_type_skills", foreignKey: "work_order_type_id" });

      this.hasMany(models.WorkOrders, { as: "work_order_type", foreignKey: "work_order_type_id" });

      this.hasMany(models.WorkOrderTypePoints, { as: "points_data", foreignKey: "work_order_type_id" })
    }
  }
  WorkOrderTypes.init({
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderTypes",
    tableName: "work_order_types",
  });
  return WorkOrderTypes;
};
