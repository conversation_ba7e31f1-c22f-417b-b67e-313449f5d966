"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class QuestionMappingSet extends Model {
    static associate(models) {
      this.belongsTo(models.Questions, { as: "question_data", foreignKey: "question_id" });
    }
  }
  QuestionMappingSet.init({
    question_mapping_touch_point_id: DataTypes.INTEGER,
    question_id: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "QuestionMappingSet",
    tableName: "question_mapping_set",
  });
  return QuestionMappingSet;
};
