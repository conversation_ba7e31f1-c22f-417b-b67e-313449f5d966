"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderSpaces extends Model {
    static associate(models) {
      this.belongsTo(models.Spaces, { as: "space_data", foreignKey: "space_id" });

      this.hasMany(models.WorkOrderScopes, { as: "work_order_scopes", foreignKey: "work_order_space_mapping_id" });
    }
  }
  WorkOrderSpaces.init({
    work_order_id: DataTypes.INTEGER,
    space_id: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderSpaces",
    tableName: "work_order_spaces",
  });
  return WorkOrderSpaces;
};
