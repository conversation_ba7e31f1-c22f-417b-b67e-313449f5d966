"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskQuestionReview extends Model {
    static associate(models) {}
  }
  TaskQuestionReview.init(
    {
      task_id: DataTypes.INTEGER,
      task_touch_point_id: DataTypes.INTEGER,
      touch_point: DataTypes.INTEGER,
      task_question_id: DataTypes.INTEGER,
      question_id: DataTypes.INTEGER,
      assigned_to_user_id: DataTypes.INTEGER,
      reviewer_user_id: DataTypes.INTEGER,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: "TaskQuestionReview",
      tableName: "task_question_review",
    }
  );
  return TaskQuestionReview;
};
