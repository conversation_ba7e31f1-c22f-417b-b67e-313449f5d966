"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderStatus extends Model {
    static associate(models) {
      this.hasMany(models.WorkOrders, { as: "work_order_status", foreignKey: "work_order_status_id" });
    }
  }
  WorkOrderStatus.init({
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderStatus",
    tableName: "work_order_status",
  });
  return WorkOrderStatus;
};
