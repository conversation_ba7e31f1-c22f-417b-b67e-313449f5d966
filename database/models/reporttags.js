"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class ReportTags extends Model {
    static associate(models) {
      this.hasMany(models.TaskImageAnswers, {
        as: "report_task_image_answers",
        foreignKey: "report_tag_id",
      });
    }
  }
  ReportTags.init(
    {
      custom_id: DataTypes.STRING,
      name: DataTypes.STRING,
      is_mandatory: DataTypes.STRING,
      number: DataTypes.INTEGER,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
    },
    {
      sequelize,
      modelName: "ReportTags",
      tableName: "report_tags",
    }
  );
  return ReportTags;
};
