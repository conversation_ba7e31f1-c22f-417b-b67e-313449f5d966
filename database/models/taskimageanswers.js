"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskImageAnswers extends Model {
    static associate(models) {}
  }
  TaskImageAnswers.init(
    {
      task_question_id: DataTypes.INTEGER,
      image_key: DataTypes.STRING,
      image_url: DataTypes.STRING,
      base64: DataTypes.TEXT("long"),
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
      task_id: DataTypes.INTEGER,
      report_tag_id: DataTypes.INTEGER,
    },
    {
      sequelize,
      modelName: "TaskImageAnswers",
      tableName: "task_image_answers",
    }
  );
  return TaskImageAnswers;
};
