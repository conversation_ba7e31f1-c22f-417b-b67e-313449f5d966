"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderReview extends Model {
    static associate(models) {}
  }
  WorkOrderReview.init(
    {
      work_order_id: DataTypes.INTEGER,
      reviewer_user_id: DataTypes.INTEGER,
      status: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: "WorkOrderReviewer",
      tableName: "work_order_reviewer",
    }
  );
  return WorkOrderReview;
};
