"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class ScopeImages extends Model {
    static associate(models) {
      this.hasMany(models.ScopeImageTouchPoints, { as: "touch_points", foreignKey: "scope_image_id" });
    }
  }
  ScopeImages.init({
    scope_id: DataTypes.INTEGER,
    image_key: DataTypes.STRING,
    image_url: DataTypes.STRING,
    base64: DataTypes.TEXT("long"),
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "ScopeImages",
    tableName: "scope_images",
  });
  return ScopeImages;
};
