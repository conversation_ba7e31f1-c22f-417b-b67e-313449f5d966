"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class QuestionMapping extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderTypes, { as: "work_order_type_data", foreignKey: "work_order_type_id" });

      this.belongsTo(models.Scopes, { as: "scope_data", foreignKey: "scope_id" });

      this.hasMany(models.QuestionMappingSet, { as: "question_set_data", foreignKey: "question_mapping_id" })

      this.hasMany(models.QuestionMappingTouchPoints, { as: "question_mapping_touch_points", foreignKey: "question_mapping_id" })
    }
  }
  QuestionMapping.init({
    work_order_type_id: DataTypes.INTEGER,
    scope_id: DataTypes.INTEGER,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "QuestionMapping",
    tableName: "question_mapping",
  });
  return QuestionMapping;
};
