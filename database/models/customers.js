"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Customers extends Model {
    static associate(models) {
      this.hasMany(models.Orders, { as: "customer_data", foreignKey: "customer_id" });
    }
  }
  Customers.init({
    actual_customer_id: {
      type: DataTypes.STRING,
      unique: true,
    },
    zoho_customer_id: DataTypes.STRING(30),
    name: DataTypes.STRING,
    email: DataTypes.STRING,
    mobile: DataTypes.STRING(15),
    phone: DataTypes.STRING(15),
    gender: DataTypes.STRING(10),
    address: DataTypes.TEXT,
    zone: DataTypes.STRING,
    location: DataTypes.STRING,
    universe: DataTypes.STRING,
    zip_code: DataTypes.STRING(10),
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "Customers",
    tableName: "customers",
  });
  return Customers;
};
