"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskQuestions extends Model {
    static associate(models) {
      this.belongsTo(models.Questions, {
        as: "question_data",
        foreignKey: "question_id",
      });
      this.hasMany(models.TaskAnswers, {
        as: "answer_data",
        foreignKey: "task_question_id",
      });
      this.hasMany(models.TaskImageAnswers, {
        as: "image_answer_data",
        foreignKey: "task_question_id",
      });
      this.hasOne(models.TaskVideoAnswers, {
        as: "video_answer_data",
        foreignKey: "task_question_id",
      });

      this.belongsTo(models.TaskTouchPoints, {
        as: "task_touch_point",
        foreignKey: "task_touch_point_id",
      });

      // Add self-referential association for parent-child relationship
      this.belongsTo(models.Questions, {
        as: "parent_question",
        foreignKey: "parent_question_id",
      });
    }
  }
  TaskQuestions.init(
    {
      task_touch_point_id: DataTypes.INTEGER,
      question_id: DataTypes.INTEGER,
      isSubquestion: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      comment: DataTypes.STRING,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
      parent_question_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "Questions",
          key: "id",
        },
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "TaskQuestions",
      tableName: "task_questions",
    }
  );
  return TaskQuestions;
};
