"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Tasks extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderScopes, {
        as: "work_order_task_scope",
        foreignKey: "work_order_scope_mapping_id",
      });

      this.belongsTo(models.WorkOrders, {
        as: "work_order_data",
        foreignKey: "work_order_id",
      });

      this.hasMany(models.TaskTouchPoints, {
        as: "task_touch_points",
        foreignKey: "task_id",
      });

      this.belongsTo(models.WorkOrderScopes, {
        as: "task_scope",
        foreignKey: "work_order_scope_mapping_id",
      });

      this.belongsTo(models.QuestionMapping, {
        as: "question_mapping",
        foreignKey: "question_mapping_id",
      });
    }
  }
  Tasks.init(
    {
      work_order_id: DataTypes.INTEGER,
      work_order_scope_mapping_id: DataTypes.INTEGER,
      actual_task_id: DataTypes.STRING,
      question_mapping_id: DataTypes.INTEGER,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
      internal_team_id: {
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      is_submitted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      additional_info: DataTypes.TEXT("medium"),
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "Tasks",
      tableName: "tasks",
    }
  );
  return Tasks;
};
