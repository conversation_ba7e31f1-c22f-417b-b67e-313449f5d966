"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Questions extends Model {
    static associate(models) {
      this.belongsTo(models.QuestionTypes, {
        as: "question_type",
        foreignKey: "question_type_id",
      });

      this.hasMany(models.QuestionOptions, {
        as: "question_options",
        foreignKey: "question_id",
      });

      this.hasMany(models.TaskQuestions, {
        as: "question_answer",
        foreignKey: "question_id",
      });

      // this.hasMany(models.TaskQuestions, {
      //   as: "parent_task_questions",
      //   foreignKey: "parent_question_id",
      // });

      Questions.hasMany(models.TaskQuestions, {
        foreignKey: "question_id",
        as: "main_task_questions", // For the main path
      });

      Questions.hasMany(models.TaskQuestions, {
        foreignKey: "question_id",
        as: "child_task_questions", // For the child questions path
      });

      this.hasMany(models.QuestionDependencies, {
        foreignKey: "parent_question_id",
        as: "child_dependencies",
      });

      this.hasMany(models.QuestionDependencies, {
        foreignKey: "child_question_id",
        as: "parent_dependencies",
      });

      this.belongsTo(models.ReportTags, {
        as: "report_tag",
        foreignKey: "report_tag_id",
      });
    }
  }
  Questions.init(
    {
      title: {
        type: DataTypes.TEXT("medium"),
        allowNull: false,
      },
      question_type_id: DataTypes.INTEGER,
      report_tag_id: DataTypes.INTEGER,
      attachment_required: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_attachment_required: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      is_numeric: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      is_subquestion: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      video_required: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      is_video_required: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      comment_required: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "Questions",
      tableName: "questions",
    }
  );
  return Questions;
};
