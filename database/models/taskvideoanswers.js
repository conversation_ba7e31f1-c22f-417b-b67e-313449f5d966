"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskVideoAnswers extends Model {
    static associate(models) {

    }
  }
  TaskVideoAnswers.init({
    task_question_id: DataTypes.INTEGER,
    signed_url: DataTypes.TEXT("medium"),
    signed_url_exipration_time: DataTypes.INTEGER,
    video_key: DataTypes.STRING,
    video_url: DataTypes.STRING,
    is_video_uploaded: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "TaskVideoAnswers",
    tableName: "task_video_answers",
  });
  return TaskVideoAnswers;
};
