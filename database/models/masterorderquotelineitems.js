"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class MasterOrderQuoteLineItems extends Model {
    static associate(models) {}
  }
  MasterOrderQuoteLineItems.init(
    {
      master_order_id: DataTypes.INTEGER,
      zoho_quote_line_item_id: DataTypes.INTEGER(30),
      room: DataTypes.STRING,
      product_name: DataTypes.STRING,
      description: DataTypes.STRING,
      quantity: DataTypes.INTEGER,
      specification: DataTypes.TEXT("medium"),
      name: DataTypes.STRING,
      height: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      width: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      reference: DataTypes.STRING,
      created_time: DataTypes.DATE,
      line_item: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      is_part: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
    },
    {
      sequelize,
      modelName: "MasterOrderQuoteLineItems",
      tableName: "master_order_quote_line_items",
    }
  );
  return MasterOrderQuoteLineItems;
};
