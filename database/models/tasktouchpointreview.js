"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskTouchPointReview extends Model {
    static associate(models) {}
  }
  TaskTouchPointReview.init(
    {
      task_id: DataTypes.INTEGER,
      task_touch_point_id: DataTypes.INTEGER,
      touch_point: DataTypes.INTEGER,
      assigned_to_user_id: DataTypes.INTEGER,
      reviewer_user_id: DataTypes.INTEGER,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: "TaskTouchPointReview",
      tableName: "task_touch_point_review",
    }
  );
  return TaskTouchPointReview;
};
