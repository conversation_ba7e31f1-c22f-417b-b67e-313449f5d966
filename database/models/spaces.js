"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Spaces extends Model {
    static associate(models) {
    }
  }
  Spaces.init({
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    source: DataTypes.STRING,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "Spaces",
    tableName: "spaces",
  });
  return Spaces;
};
