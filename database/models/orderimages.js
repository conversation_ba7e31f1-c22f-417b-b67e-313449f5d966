"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class OrderImages extends Model {
    static associate(models) {}
  }
  OrderImages.init(
    {
      order_id: DataTypes.INTEGER,
      image_key: DataTypes.STRING,
      image_url: DataTypes.STRING,
      base64: DataTypes.TEXT("long"),
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE
    },
    {
      sequelize,
      modelName: "OrderImages",
      tableName: "order_images",
    }
  );
  return OrderImages;
};
