"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class PointHistory extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderTypePoints, { as: "wo_points_data", foreignKey: "work_order_type_point_id" });
      this.belongsTo(models.PointRates, { as: "rates_data", foreignKey: "point_rate_id" });
      this.belongsTo(models.WorkOrders, { as: "work_order_data", foreignKey: "work_order_id" });
    }
  }
  PointHistory.init({
    user_id: DataTypes.INTEGER,
    work_order_id: DataTypes.INTEGER,
    work_order_type_point_id: DataTypes.INTEGER,
    point_rate_id: DataTypes.INTEGER,
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "PointHistory",
    tableName: "point_history",
  });
  return PointHistory;
};
