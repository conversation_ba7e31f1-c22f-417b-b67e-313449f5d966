"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Scopes extends Model {
    static associate(models) {
      this.hasOne(models.ScopeImages, {
        as: "scope_image",
        foreignKey: "scope_id",
      });
    }
  }
  Scopes.init(
    {
      model: DataTypes.STRING,
      name: DataTypes.STRING,
      new_name: DataTypes.STRING,
      source: DataTypes.STRING,
      height: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      width: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      specification: DataTypes.TEXT("medium"),
      reference: DataTypes.STRING,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      line_item: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      is_part: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "Scopes",
      tableName: "scopes",
    }
  );
  return Scopes;
};
