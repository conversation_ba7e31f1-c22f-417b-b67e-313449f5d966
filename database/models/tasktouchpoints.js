"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskTouchPoints extends Model {
    static associate(models) {
      this.hasMany(models.TaskQuestions, {
        as: "task_questions",
        foreignKey: "task_touch_point_id",
      });
    }
  }
  TaskTouchPoints.init(
    {
      task_id: DataTypes.INTEGER,
      touch_point: DataTypes.INTEGER,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "TaskTouchPoints",
      tableName: "task_touch_points",
    }
  );
  return TaskTouchPoints;
};
