"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderTypeSkills extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderTypes, { foreignKey: "work_order_type_id" });
    }
  }
  WorkOrderTypeSkills.init({
    work_order_type_id: DataTypes.INTEGER,
    skill_id: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderTypeSkills",
    tableName: "work_order_type_skills",
  });
  return WorkOrderTypeSkills;
};
