"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderScopes extends Model {
    static associate(models) {
      this.belongsTo(models.Scopes, { as: "scope_data", foreignKey: "scope_id" });

      this.belongsTo(models.WorkOrderSpaces, { as: "work_order_task_space", foreignKey: "work_order_space_mapping_id" });
      this.hasMany(models.Tasks, { as: "work_order_task", foreignKey: "work_order_scope_mapping_id" });
    }
  }
  WorkOrderScopes.init({
    work_order_id: DataTypes.INTEGER,
    work_order_space_mapping_id: DataTypes.INTEGER,
    scope_id: DataTypes.INTEGER,
    count: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    task_count: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderScopes",
    tableName: "work_order_scopes",
  });
  return WorkOrderScopes;
};
