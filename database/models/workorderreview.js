"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderReview extends Model {
    static associate(models) {

    }
  }
  WorkOrderReview.init({
    work_order_id: DataTypes.INTEGER,
    reviewer_user_id: DataTypes.INTEGER,
    requester_user_id: DataTypes.INTEGER,
    review_status: DataTypes.BOOLEAN,
    reason: DataTypes.STRING
  }, {
    sequelize,
    modelName: "WorkOrderReview",
    tableName: "work_order_review",
  });
  return WorkOrderReview;
};
