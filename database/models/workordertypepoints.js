"use strict";
const {
  Model
} = require("sequelize");
const { CONSTANTS } = require("../../helpers/constants");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderTypePoints extends Model {
    static associate(models) {
      this.belongsTo(models.WorkOrderTypes, { as: "work_order_type_data", foreignKey: "work_order_type_id" })
    }
  }
  WorkOrderTypePoints.init({
    work_order_type_id: DataTypes.INTEGER,
    points_to_assign: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    transaction_type: DataTypes.ENUM(CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT, CONSTANTS.POINTS.TRANSACTION_TYPE.DEBIT),
    added_by: DataTypes.INTEGER,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderTypePoints",
    tableName: "work_order_type_points",
  });
  return WorkOrderTypePoints;
};
