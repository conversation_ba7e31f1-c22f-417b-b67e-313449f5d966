"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Tokens extends Model {
    static associate(models) {

    }
  }
  Tokens.init({
    token: DataTypes.TEXT,
    type: DataTypes.STRING,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "Tokens",
    tableName: "tokens",
  });
  return Tokens;
};
