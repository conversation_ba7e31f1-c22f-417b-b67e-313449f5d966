"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class WorkOrderAttachments extends Model {
    static associate(models) {

    }
  }
  WorkOrderAttachments.init({
    inserted_zoho_wo_attachment_id: DataTypes.STRING,
    work_order_id: DataTypes.INTEGER,
    work_order_type_id: DataTypes.INTEGER,
    file_key: DataTypes.STRING,
    file_url: DataTypes.STRING,
    action: DataTypes.STRING,
    type: DataTypes.STRING,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: 1
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "WorkOrderAttachments",
    tableName: "work_order_attachments",
  });
  return WorkOrderAttachments;
};
