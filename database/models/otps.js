"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Otps extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Otps.init({
    work_order_id: DataTypes.INTEGER,
    customer_id: DataTypes.INTEGER,
    country_code: DataTypes.STRING(5),
    mobile: DataTypes.STRING(15),
    otp: DataTypes.STRING,
    is_otp_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
    action: DataTypes.STRING,
    otp_send_to: DataTypes.ENUM("MOBILE", "EMAIL"),
  }, {
    sequelize,
    modelName: "Otps",
    tableName: "otps",
  });
  return Otps;
};
