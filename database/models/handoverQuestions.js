"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class HandoverQuestions extends Model {
    static associate(models) {
      this.hasMany(models.HandoverQuestionOptions, {
        as: "handover_question_options",
        foreignKey: "question_id",
      });

      this.hasMany(models.HandoverWorkOrderAnswers, {
        as: "question_answers",
        foreignKey: "question_id",
      });

      this.hasMany(models.HandoverImageNaswers, {
        as: "question_images",
        foreignKey: "question_id",
      });
    }
  }
  HandoverQuestions.init(
    {
      order: { type: DataTypes.INTEGER, allowNull: false },
      min_number: { type: DataTypes.INTEGER, allowNull: true },
      max_number: { type: DataTypes.INTEGER, allowNull: true },
      title: {
        type: DataTypes.TEXT("medium"),
        allowNull: false,
      },
      comment_required: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      question_type: {
        type: DataTypes.ENUM(
          "Single",
          "Multiple",
          "Descriptive",
          "Rating",
          "Boolean"
        ),
        allowNull: false,
      },
      attachment_required: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_checked: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_mandatory: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "HandoverQuestions",
      tableName: "handover_questions",
    }
  );
  return HandoverQuestions;
};
