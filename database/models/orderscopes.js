"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class OrderScopes extends Model {
    static associate(models) {
      this.belongsTo(models.Scopes, { as: "scope_data", foreignKey: "scope_id" });
    }
  }
  OrderScopes.init({
    order_id: DataTypes.INTEGER,
    order_space_mapping_id: DataTypes.INTEGER,
    scope_id: DataTypes.INTEGER,
    count: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "OrderScopes",
    tableName: "order_scopes",
  });
  return OrderScopes;
};
