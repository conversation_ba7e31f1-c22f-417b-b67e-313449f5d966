"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class HandoverQuestionOptions extends Model {
    static associate(models) {}
  }
  HandoverQuestionOptions.init(
    {
      question_id: DataTypes.INTEGER,
      title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        allowNull: false,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "HandoverQuestionOptions",
      tableName: "handover_question_options",
    }
  );
  return HandoverQuestionOptions;
};
