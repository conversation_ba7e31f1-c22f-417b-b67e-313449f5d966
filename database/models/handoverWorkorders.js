"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class HandoverWorkOrders extends Model {
    static associate(models) {
      this.belongsTo(models.Orders, {
        as: "order_data",
        foreignKey: "order_id",
      });

      this.belongsTo(models.Customers, {
        as: "customer_data",
        foreignKey: "customer_id",
      });

      this.belongsTo(models.WorkOrderStatus, {
        as: "handover_work_order_status",
        foreignKey: "work_order_status",
      });

      this.hasMany(models.HandoverWorkOrderAnswers, {
        as: "handover_work_order_answer",
        foreignKey: "workorder_id",
      });
    }
  }
  HandoverWorkOrders.init(
    {
      actual_work_order_id: DataTypes.STRING,
      order_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      customer_id: DataTypes.INTEGER,
      check_in_user_id: DataTypes.INTEGER,
      work_order_status: DataTypes.INTEGER,
      assigned_to_user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_to_user_role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_by_user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      assigned_by_user_role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      scheduled_start_date: DataTypes.DATEONLY,
      scheduled_start_time: DataTypes.STRING(20),
      actual_start_date: DataTypes.DATEONLY,
      actual_start_time: DataTypes.STRING(20),
      actual_end_datetime: DataTypes.DATE,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      zoho_handover_id: DataTypes.STRING,
      check_out_otp: DataTypes.STRING,
      image_key: DataTypes.STRING,
      image_url: DataTypes.STRING,
      check_out_otp_created_at: DataTypes.DATE,
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "HandoverWorkOrders",
      tableName: "handover_work_orders",
    }
  );
  return HandoverWorkOrders;
};
