"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Orders extends Model {
    static associate(models) {
      this.belongsTo(models.Customers, {
        as: "customer_data",
        foreignKey: "customer_id",
      });
      this.belongsTo(models.OrderStatus, {
        as: "order_status",
        foreignKey: "order_status_id",
      });
      this.hasMany(models.OrderSpaces, {
        as: "order_spaces",
        foreignKey: "order_id",
      });
      this.hasMany(models.WorkOrders, {
        as: "work_order_data",
        foreignKey: "order_id",
      });
      this.hasMany(models.OrderImages, {
        as: "order_images",
        foreignKey: "order_id",
      });
      this.hasOne(models.HandoverWorkOrders, {
        as: "handover_work_order",
        foreignKey: "order_id",
      });
    }
  }
  Orders.init(
    {
      actual_order_id: {
        type: DataTypes.STRING,
        unique: true,
      },
      zoho_order_id: DataTypes.STRING(30),
      zoho_stage: DataTypes.STRING(50),
      title: DataTypes.STRING,
      zoho_fabricator_id: DataTypes.STRING,
      fabricator_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      fabricator_email: DataTypes.STRING,
      customer_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      order_status_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      amount: DataTypes.DOUBLE,
      zoho_warranty_id: DataTypes.STRING,
      qc_mode: DataTypes.STRING,
      profile_warranty_end_date: DataTypes.DATEONLY,
      hardware_warranty_end_date: DataTypes.DATEONLY,
      expected_date_of_survey: DataTypes.DATEONLY,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "Orders",
      tableName: "orders",
    }
  );
  return Orders;
};
