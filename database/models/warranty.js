"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class Warranty extends Model {
    static associate(models) {

    }
  }
  Warranty.init({
    actual_warranty_id: DataTypes.STRING,
    work_order_id: DataTypes.INTEGER,
    customer_id: DataTypes.INTEGER,
    file_key: DataTypes.STRING,
    file_url: DataTypes.TEXT("medium"),
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "Warranty",
    tableName: "warranties",
  });
  return Warranty;
};
