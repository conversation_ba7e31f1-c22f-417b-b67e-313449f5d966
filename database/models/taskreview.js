"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskReview extends Model {
    static associate(models) {}
  }
  TaskReview.init(
    {
      task_id: DataTypes.INTEGER,
      reviewer_user_id: DataTypes.INTEGER,
      assigned_to_user_id: DataTypes.INTEGER,
      status: DataTypes.STRING,
      reason: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: "TaskReview",
      tableName: "task_review",
    }
  );
  return TaskReview;
};
