"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class TaskAnswers extends Model {
    static associate(models) {}
  }
  TaskAnswers.init(
    {
      task_question_id: DataTypes.INTEGER,
      answer: DataTypes.STRING,
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: 1,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,
    },
    {
      sequelize,
      modelName: "TaskAnswers",
      tableName: "task_answers",
    }
  );
  return TaskAnswers;
};
