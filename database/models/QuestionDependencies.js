"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class QuestionDependencies extends Model {
    static associate(models) {
      this.belongsTo(models.Questions, {
        foreignKey: "parent_question_id",
        as: "parent_question",
      });

      this.belongsTo(models.Questions, {
        foreignKey: "child_question_id",
        as: "child_question",
      });
    }
  }

  QuestionDependencies.init(
    {
      parent_question_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "questions",
          key: "id",
        },
      },
      child_question_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "questions",
          key: "id",
        },
      },
      trigger_conditions: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
      },
      logical_operator: {
        type: DataTypes.ENUM("AND", "OR"),
        allowNull: false,
        defaultValue: "AND",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      sequelize,
      modelName: "QuestionDependencies",
      tableName: "question_dependencies",
    }
  );

  return QuestionDependencies;
};
