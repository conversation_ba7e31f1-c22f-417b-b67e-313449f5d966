"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class MasterOrders extends Model {
    static associate(models) {
      this.hasMany(models.MasterOrderQuoteLineItems, {
        as: "line_items_data",
        foreignKey: "master_order_id",
      });
    }
  }
  MasterOrders.init(
    {
      zoho_order_id: {
        type: DataTypes.STRING(30),
        unique: true,
      },
      deal_name: DataTypes.STRING(50),
      stage: DataTypes.STRING(50),
      customer_id: DataTypes.STRING(30),
      customer_name: DataTypes.STRING(50),
      email: DataTypes.STRING(100),
      mobile: DataTypes.STRING(15),
      phone: DataTypes.STRING(15),
      gender: DataTypes.STRING(10),
      street_name: DataTypes.STRING,
      city: DataTypes.STRING(20),
      zip_code: DataTypes.STRING(10),
      state_name: DataTypes.STRING(20),
      region: DataTypes.STRING(10),
      type_of_house: DataTypes.STRING(30),
      owner_name: DataTypes.STRING(50),
      owner_id: DataTypes.STRING(30),
      owner_email: DataTypes.STRING(100),
      committed_date_of_delivery: DataTypes.DATEONLY,
      date_of_installation_complete: DataTypes.DATEONLY,
      warranty_id: DataTypes.STRING,
      amount: DataTypes.DOUBLE,
      expected_date_of_survey: DataTypes.DATEONLY,
      created_time: DataTypes.DATE,
      modified_time: DataTypes.DATE,
      is_read: {
        type: DataTypes.BOOLEAN,
        defaultValue: 0,
      },
      qc_mode: DataTypes.STRING,
      createdAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
    },
    {
      sequelize,
      modelName: "MasterOrders",
      tableName: "master_orders",
    }
  );
  return MasterOrders;
};
