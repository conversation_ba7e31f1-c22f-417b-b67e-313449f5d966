"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class QuestionMappingTouchPoints extends Model {
    static associate(models) {
      this.hasMany(models.QuestionMappingSet, { as: "question_set_data", foreignKey: "question_mapping_touch_point_id" })
    }
  }
  QuestionMappingTouchPoints.init({
    question_mapping_id: DataTypes.INTEGER,
    touch_point: DataTypes.INTEGER,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "QuestionMappingTouchPoints",
    tableName: "question_mapping_touch_points",
  });
  return QuestionMappingTouchPoints;
};
