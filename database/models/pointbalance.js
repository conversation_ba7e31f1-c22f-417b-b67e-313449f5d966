"use strict";
const {
  Model
} = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class PointBalance extends Model {
    static associate(models) {

    }
  }
  PointBalance.init({
    user_id: DataTypes.INTEGER,
    total_points: DataTypes.INTEGER,
    total_earnings: DataTypes.DECIMAL(10, 2),
    redeemed_points: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    redeemed_earnings: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE,
  }, {
    sequelize,
    modelName: "PointBalance",
    tableName: "point_balance"
  });
  return PointBalance;
};
