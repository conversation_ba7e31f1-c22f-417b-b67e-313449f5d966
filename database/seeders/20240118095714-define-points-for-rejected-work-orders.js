"use strict";

const { CONSTANTS } = require("../../helpers/constants");
const db = require("../models");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const pointExist = await db.WorkOrderTypePoints.count({
      where: {
        transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.DEBIT,
        is_active: 1
      }
    });

    if (!pointExist) {
      await db.WorkOrderTypePoints.create({
        points_to_assign: 200,
        transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.DEBIT,
      })
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
