"use strict";

const { CONSTANTS } = require("../../helpers/constants");
const db = require("../models");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // check if general space is avaialble or not. if not then only we will create.
      const generalSpace = await db.Spaces.count({
        where: { title: CONSTANTS.SPACE.GENERAL.TITLE }
      });

      if (!generalSpace) {
        await db.Spaces.create({
          title: CONSTANTS.SPACE.GENERAL.TITLE,
          source: CONSTANTS.DATA_SOURCE.CMS
        });
      }
    }
    catch (error) {
      console.log("SEEDER_ERROR: ", error);
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
