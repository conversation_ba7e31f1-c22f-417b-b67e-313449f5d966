"use strict";

const db = require("../models");
const { CONSTANTS } = require("../../helpers/constants");
const common = require("../../controller/commonFunctions");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const workOrderStatus = await common.getWorkOrderStatusByTitle(
        CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED
      );
      if (workOrderStatus) {
        await db.WorkOrderStatus.update(
          { title: CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SE },
          { where: { id: workOrderStatus?.id } }
        );
      }
      await db.WorkOrderStatus.create({
        title: CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_APPROVED_BY_SH,
      });
    } catch (error) {
      console.log("SEEDER_ERROR: ", error);
    }
  },

  async down(queryInterface, Sequelize) {},
};
