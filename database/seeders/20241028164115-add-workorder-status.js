"use strict";

const db = require("../models");
const { CONSTANTS } = require("../../helpers/constants");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await db.WorkOrderStatus.create({
        title: CONSTANTS.WORK_ORDER.STATUS.SERVICE_ENGINEER_APPROVED,
      });
    } catch (error) {
      console.log("SEEDER_ERROR: ", error);
    }
  },

  async down(queryInterface, Sequelize) {},
};
