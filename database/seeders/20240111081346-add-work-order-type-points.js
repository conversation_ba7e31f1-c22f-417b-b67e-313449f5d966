"use strict";

const common = require("../../controller/commonFunctions.js");
const { CONSTANTS } = require("../../helpers/constants");
const db = require("../models");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // get all work order types
    const wo_types = await db.WorkOrderTypes.findAll();

    if (wo_types?.length) {
      for (let i = 0; i < wo_types.length; i++) {
        //check if points exist with wo type
        const pointExist = await common.getPointsOfWoTypeByCondition([{ work_order_type_id: wo_types[i].id }])
        if (!pointExist) {
          await db.WorkOrderTypePoints.create({
            work_order_type_id: wo_types[i].id,
            points_to_assign: 0,
            transaction_type: CONSTANTS.POINTS.TRANSACTION_TYPE.CREDIT,
            added_by: 0
          });
        }
      }
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
