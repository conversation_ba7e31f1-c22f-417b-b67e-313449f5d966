"use strict";

const db = require("../models");
const { CONSTANTS } = require("../../helpers/constants");

/** @type {import("sequelize-cli").Migration} */

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const orderStatus = await Object.values(CONSTANTS.ORDER_STATUS);

      let orderStatusArr = [];
      for (let i = 0; i < orderStatus.length; i++) {
         orderStatusArr.push({
          title: orderStatus[i]
        });
      }

      await db.OrderStatus.bulkCreate(orderStatusArr);
    }
    catch (error) {
      console.log("SEEDER_ERROR: ", error.message);
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
