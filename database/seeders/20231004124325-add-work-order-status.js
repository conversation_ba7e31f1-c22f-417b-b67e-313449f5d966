"use strict";

const db = require("../models");
const { CONSTANTS } = require("../../helpers/constants");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const workOrderStatus = await Object.values(CONSTANTS.WORK_ORDER.STATUS);

      let workOrderStatusArr = [];
      for (let i = 0; i < workOrderStatus.length; i++) {
        await workOrderStatusArr.push({
          title: workOrderStatus[i]
        });
      }

      await db.WorkOrderStatus.bulkCreate(workOrderStatusArr);
    }
    catch (error) {
      console.log("SEEDER_ERROR: ", error);
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
