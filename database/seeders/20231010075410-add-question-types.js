"use strict";

const db = require("../models");
const { CONSTANTS } = require("../../helpers/constants");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const questionTypes = await Object.values(CONSTANTS.QUESTION_TYPES);

      let questionTypesArr = [];
      for (let i = 0; i < questionTypes.length; i++) {
        questionTypesArr.push({
          title: questionTypes[i]
        });
      }

      await db.QuestionTypes.bulkCreate(questionTypesArr);
    }
    catch (error) {
      console.log("SEEDER_ERROR: ", error.message);
    }
  },

  async down(queryInterface, Sequelize) {

  }
};
