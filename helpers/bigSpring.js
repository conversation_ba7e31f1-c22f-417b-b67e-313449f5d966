const axios = require('axios');

const getProfile = async (email) => {
    try {
        // Define the GraphQL query as a string
        const query = `
            query (
                $paginate: BaseClassicPaginate
                $conditions: CompanyProfileSearchConditions
            ) {
                profileSearch(paginate: $paginate, conditions: $conditions) {
                    nodes {
                        id
                        companyId
                        profileId
                        emailAddress
                        language
                        phoneNumber
                        externalId
                        profileExternalId
                        startDate
                        learnerCommsOptOut
                        coachCommsOptOut
                        excludeFromAnalytics
                        timezone
                        createdAt
                        updatedAt
                        archivedAt
                        score
                    }
                    pageInfo {
                        beforeCursor
                        afterCursor
                    }
                    totalCount
                }
            }
        `;

        // Define the variables
        const variables = {
            "paginate": {
                "limit": 10,
                "cursor": null
            },
            "conditions": {
                "searchTerms": {
                    "iLike": email
                }
            }
        };

        // Set the endpoint and headers
        const url = process.env.BS_ADMIN_API_URL;
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.BS_API_TOKEN}`
        };

        // Return the POST request (returning the promise)
        let resp = {};
        await axios.post(
            url,
            { query: query, variables: variables },
            { headers: headers }
        )
            .then((response) => {
                resp = { data: response.data.data.profileSearch.nodes[0] }
            })
            .catch((error) => {
                resp = { error: error.message }
            });

        return resp;
    }
    catch (error) {
        return error;
    }
}

const getProScore = async (profileId) => {
    try {
        // Define the GraphQL query as a string
        const query = `
            query {
                proScoreGetV3 {
                    id
                    score
                    skillsCompletedCount
                    skillsAssignedCount
                    repsCompletedCount
                    repsAssignedCount
                    proScore
                }
            }
        `;

        // Set the endpoint and headers
        const url = process.env.BS_API_URL;
        const headers = {
            'Content-Type': 'application/json',
            'X-Company-Profile-Id': profileId,
            'Authorization': `Bearer ${process.env.BS_API_TOKEN}`
        };

        // Return the POST request (returning the promise)
        let resp = {};
        await axios.post(
            url,
            { query: query },
            { headers: headers }
        )
            .then((response) => {
                resp = { data: response.data.data.proScoreGetV3 }
            })
            .catch((error) => {
                resp = { error: error.message }
            });

        return resp;
    }
    catch (error) {
        return error;
    }
}

module.exports = {
    getProfile,
    getProScore,
}
