const { default: axios } = require("axios");
const { logger } = require("./logger");

module.exports.sendWhatsappMsg = async ({
  campaignName,
  destination,
  userName = "Pawan",
  templateParams,
}) => {
  try {
    const payload = {
      apiKey: process.env.AISENSY_API_KEY,
      campaignName,
      destination,
      userName,
      templateParams,
    };
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: process.env.AISENSY_API_URL,
      headers: {
        "Content-Type": "application/json",
      },
      data: payload,
    };
    const data = await axios(config);
    if (data?.data?.success) {
      return {
        success: true,
        message: "Whatsapp message sent successfully.",
      };
    }
    return {
      success: false,
      message: "Failed to send Whatsapp message.",
    };
  } catch (error) {
    console.log("error: ", error);
    logger.error({
      message: `SEND_WHATSAPP_ERROR: ${error.message}`,
      stack: error.stack,
    });

    return {
      success: false,
      message: `Failed to send Whatsapp message. Error: ${error.message}`,
    };
  }
};
