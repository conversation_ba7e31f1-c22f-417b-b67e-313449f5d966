const { isEmpty } = require("lodash");
const axios = require("axios");

const { API_PATH } = require("./apiEndPoints");
const { logger } = require("./logger");

const getUserDetailsByUserIds = async (data = {}) => {
  try {
    if (data?.user_ids?.length) {
      let userData = [];
      const config = {
        method: "GET",
        url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.USER_DETAILS_BY_ID}`,
        params: {
          user_ids: data?.user_ids,
        },
        headers: {
          token: data?.headers?.token,
          platform: data?.headers?.platform,
        },
      };

      await axios(config)
        .then(async (apiResponse) => {
          if (apiResponse && apiResponse?.data) {
            if (
              apiResponse.data?.status === 1 &&
              apiResponse?.data?.data?.totalCount
            ) {
              userData = await apiResponse?.data?.data?.users;
            } else if (apiResponse?.data?.status === 0) {
              logger.error({ message: apiResponse.data.message });
            }
          }
        })
        .catch((error) => {
          const errorResp = error?.response;
          logger.error({ message: errorResp.data.message, stack: error.stack });
          return null;
        });

      return userData?.length ? userData : null;
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getUsersByZone = async (data) => {
  try {
    if (!isEmpty(data)) {
      let userData = [];

      let params = { zone: data?.zone };
      if (data?.roles?.length) {
        params.roles = data.roles;
      }

      if (data?.other_data) {
        params.other_data = data.other_data;
      }

      const config = {
        method: "GET",
        url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.USERS_BY_ZONE}`,
        params: params,
        headers: {
          token: data?.headers?.token,
          platform: data?.headers?.platform,
        },
      };

      await axios(config)
        .then(async (apiResponse) => {
          if (apiResponse && apiResponse?.data) {
            if (
              apiResponse.data?.status === 1 &&
              apiResponse?.data?.data?.totalCount
            ) {
              let userDeviceTokens = [];
              const userDataAPI = await apiResponse?.data?.data?.users;
              if (userDataAPI.length) {
                await userDataAPI.map(async (user) => {
                  (await user.device_token) &&
                    userDeviceTokens.push(user.device_token);
                });
              }

              userData = userDataAPI;
            } else if (apiResponse?.data?.status === 0) {
              logger.error({ message: apiResponse.data.message });
            }
          }
        })
        .catch((error) => {
          const errorResp = error?.response;
          logger.error({
            message: errorResp?.data?.message,
            stack: error.stack,
          });
          return null;
        });

      return userData?.length ? userData : null;
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getAllSkills = async (data) => {
  try {
    let skillsData = [];
    const config = {
      method: "GET",
      url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.GET_SKILLS}`,
      headers: {
        token: data?.headers?.token,
        platform: data?.headers?.platform,
      },
    };

    await axios(config)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (
            apiResponse.data?.status === 1 &&
            !isEmpty(apiResponse?.data?.data?.skills)
          ) {
            skillsData = await apiResponse?.data?.data?.skills;
          } else if (apiResponse?.data?.status === 0) {
            logger.error({ message: apiResponse?.data?.message });
          }
        }
      })
      .catch((error) => {
        const errorResp = error?.response;
        logger.error({ message: errorResp?.data?.message, stack: error.stack });
        return null;
      });

    return skillsData?.length ? skillsData : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getUsersByRoleName = async (data) => {
  try {
    if (!isEmpty(data)) {
      let userData = [];

      const config = {
        method: "GET",
        url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.USER_BY_ROLES}`,
        params: { roles: data.roles },
        headers: {
          token: data?.headers?.token,
          platform: data?.headers?.platform,
        },
      };

      await axios(config)
        .then(async (apiResponse) => {
          if (apiResponse && apiResponse?.data) {
            if (
              apiResponse.data?.status === 1 &&
              apiResponse?.data?.data?.totalCount
            ) {
              const userDataAPI = await apiResponse?.data?.data?.users;
              if (userDataAPI.length) {
                userData = userDataAPI;
              }
            } else if (apiResponse?.data?.status === 0) {
              logger.error({ message: apiResponse?.data?.message });
            }
          }
        })
        .catch((error) => {
          const errorResp = error?.response;
          logger.error({
            message: errorResp?.data?.message,
            stack: error.stack,
          });
          return null;
        });

      return userData?.length ? userData : null;
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getTemplate = async (data) => {
  try {
    if (isEmpty(data)) {
      return null;
    }

    const { scope, type, action, headers } = data;

    const params = {
      ...(scope && { scope }),
      ...(type && { type }),
      ...(action && { action }),
    };

    const config = {
      method: "GET",
      url: `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.GET_TEMPLATE}`,
      params,
      headers: {
        token: headers?.token,
        platform: headers?.platform,
      },
    };

    let templateData;

    await axios(config)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (
            apiResponse.data?.status === 1 &&
            apiResponse?.data?.data?.templates
          ) {
            const templateDataAPI = await apiResponse?.data?.data?.templates;
            if (templateDataAPI) {
              templateData = templateDataAPI;
            }
          } else if (apiResponse?.data?.status === 0) {
            logger.error({ message: apiResponse?.data?.message });
          }
        }
      })
      .catch((error) => {
        const errorResp = error?.response;
        logger.error({ message: errorResp?.data?.message, stack: error.stack });
        return null;
      });

    return templateData?.length ? templateData : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const addAlerts = async (data) => {
  try {
    if (isEmpty(data)) {
      return null;
    }

    const url = `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.ADD_ALERTS}`;

    const params = {
      sender_user_id: data.sender_user_id,
      title: data.title,
      body: data.body,
      action: data.action,
      receiver_and_send_to: data.receiver_and_send_to,
      type: data.type,
      is_sent: data.is_sent,
    };

    const headers = {
      headers: {
        token: data?.headers?.token,
        platform: data?.headers?.platform,
      },
    };

    let respData;
    await axios
      .post(url, params, headers)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (apiResponse.data?.status === 1 && apiResponse?.data?.data) {
            respData = apiResponse?.data?.data;
          } else if (apiResponse?.data?.status === 0) {
            logger.error({ message: apiResponse?.data?.message });
          }
        }
      })
      .catch((error) => {
        const errorResp = error?.response;
        logger.error({ message: errorResp?.data?.message, stack: error.stack });
        return null;
      });

    return respData || null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const updateAlert = async (data) => {
  try {
    if (isEmpty(data)) {
      return null;
    }

    const url = `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.UPDATE_ALERTS}`;

    const params = {
      id: data.id,
      is_sent: data.is_sent,
      response_data: data.response,
    };

    const headers = {
      headers: {
        token: data?.headers?.token,
        platform: data?.headers?.platform,
      },
    };

    let resp = false;
    await axios
      .post(url, params, headers)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (apiResponse.data?.status === 1) {
            resp = true;
          } else if (apiResponse?.data?.status === 0) {
            logger.error({ message: apiResponse?.data?.message });
            resp = false;
          }
        }
      })
      .catch((error) => {
        const errorResp = error?.response;
        logger.error({ message: errorResp?.data?.message, stack: error.stack });
        return false;
      });

    return resp;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const deleteAlert = async (data) => {
  try {
    if (isEmpty(data)) {
      return null;
    }

    const url = `${process.env.USER_AUTH_SERVICE_API_URL}${API_PATH.USER_AUTH_SERVICE_APIS.DELETE_ALERTS}`;

    const params = {
      id: data.id,
    };

    const headers = {
      headers: {
        token: data?.headers?.token,
        platform: data?.headers?.platform,
      },
    };

    let resp = false;
    await axios
      .post(url, params, headers)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          if (apiResponse.data?.status === 1) {
            resp = true;
          } else if (apiResponse?.data?.status === 0) {
            logger.error({ message: apiResponse?.data?.message });
            resp = false;
          }
        }
      })
      .catch((error) => {
        const errorResp = error?.response;
        logger.error({ message: errorResp?.data?.message, stack: error.stack });
        return false;
      });

    return resp;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

module.exports = {
  getUserDetailsByUserIds,
  getUsersByZone,
  getAllSkills,
  getUsersByRoleName,
  getTemplate,
  addAlerts,
  updateAlert,
  deleteAlert,
};
