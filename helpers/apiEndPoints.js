const API_PATH = {
  APK: {
    BASE: "/apk",
    VERSION: "/version",
  },
  USER_AUTH_SERVICE_APIS: {
    AUTHENTICATE_USER: "/authenticate-user",
    GET_SKILLS: "/get-skills",
    USER_DETAILS_BY_ID: "/get-user-details-by-id",
    USERS_BY_ZONE: "/get-users-by-zone",
    USER_BY_ROLES: "/get-users-by-roles",
    GET_TEMPLATE: "/get-template",
    ADD_ALERTS: "/add-alerts",
    UPDATE_ALERTS: "/update-alerts",
    DELETE_ALERTS: "/delete-alerts",
  },

  ORDER_MANAGEMENT_SERVICE_APIS: {
    BASE: "/user-authentication-service",
    IS_ASSIGNED_ORDER_WO_COMPLETED: "/is-assigned-order-wo-completed",
    GET_ASSIGNED_ORDERS: "/get-assigned-orders",
    GET_ASSIGNED_ORDERS_BY_STATUS: "/get-assigned-orders-by-status",
    GET_ASSIGNED_WORK_ORDERS: "/get-assigned-work-orders",
    GET_POINTS_EARNINGS_OF_USER: "/get-points-earnings-of-user",
    UPDATE_FABRICATOR_IN_ORDER: "/update-fabricator-in-order",
  },

  ORDERS: {
    BASE: "/orders",
    GET: "/get",
    GET_SPACE_SCOPE: "/get-space-scope",
    GET_STATUS: "/get-status",
    SYNC: "/sync",
    FETCH_ZOHO: "/fetch-from-zoho",
    FETCH_SYNC_ZOHO_ORDERS: "/fetch-sync",
    SYNC_MASTER_ORDER_AND_ORDER: "/sync-master-order-and-order",
    SYNC_MASTER_ORDER_AND_ORDER_LINE_ITEM:
      "/sync-master-order-and-order-line-item",
    SYNC_ORDER_STATUS: "/sync-order-status",
    UPLOAD_IMAGE: "/upload-image",
    DELETE_IMAGE: "/delete-image",
    HANDOVER_EMAIL: "/send-handover-email",
    GET_HANDOVER_LINK: "/get-handover-link",
    SEND_WARRANTY_EMAIL: "/send-warranty-email",
    SYNC_ORDER_ZOHO_STAGE: "/sync-order-zoho-stage",
  },

  SPACES: {
    BASE: "/spaces",
    GET: "/get",
    ADD: "/add",
    UPDATE: "/update",
    CHANGE_STATUS: "/change-status",
  },

  SCOPES: {
    BASE: "/scopes",
    GET: "/get",
    ADD: "/add",
    // UPDATE: "/update",
    CHANGE_STATUS: "/change-status",
  },

  WORK_ORDER_TYPE: {
    BASE: "/work-order-type",
    ADD: "/add",
    UPDATE: "/update",
    GET: "/get",
    CHANGE_STATUS: "/change-status",
  },

  WORK_ORDER: {
    BASE: "/work-order",
    ADD: "/add",
    GET: "/get",
    UPDATE: "/update",
    GET_ASSIGNED: "/get-assigned",
    GET_ASSIGNED_WITHOUT_PAGINATION: "/get-assigned-without-pagination",
    CHECK_IN: "/check-in",
    CHECK_OUT: "/check-out",
    SEND_OTP_FOR_SUBMIT_WO: "/send-otp-for-submit-wo",
    VERIFY_OTP_FOR_SUBMIT_WO: "/verify-otp-for-submit-wo",
    REVIEW_LIST: "/review-list",
    REVIEW: "/review",
    GET_STATUS: "/get-status",
    SEND_PDF: "/send-pdf",
    DELETE: "/delete/:id",
    ACCEPT_REJECT_TAG: "/accept-reject-tag",
    REVIEW_QUESTION_TOUCH_POINT: "/review-question-tp",
    SE_SH_CHECK_IN: "/se-sh-check-in",
    SE_SH_CANCEL_CHECK_IN: "/cancel-se-sh-check-in",
    SE_SH_TAG_REQUEST: "/se-sh-tag-request",
  },

  QUESTION_TYPE: {
    BASE: "/question-type",
    GET: "/get",
  },

  QUESTIONS: {
    BASE: "/questions",
    CREATE: "/create",
    GET: "/get",
    UPDATE: "/update",
    CHANGE_STATUS: "/change-status",
  },

  QUESTION_DEPENCENDY: {
    BASE: "/question-dependency",
    MAP: "/map",
    UNMAP: "/unmap",
    EDIT: "/edit",
  },

  QUESTION_SET: {
    BASE: "/question-set",
    CREATE: "/create",
    CHANGE_STATUS: "/change-status",
    GET: "/get",
    GET_BY_WOTYPE_SCOPE: "/get-by-wo-type-scope",
    EDIT: "/edit",
  },

  TASKS: {
    BASE: "/tasks",
    GET: "/get",
    SUBMIT_ANSWER: "/submit-answers",
    UPLOAD_IMAGE_ANSWER: "/upload-image-answer",
    GENERATE_VIDEO_ANSWER_SIGNED_URL: "/generate-video-answer-signed-url",
    UPDATE_VIDEO_URL: "/update-video-url",
    DELETE_VIDEO_ANSWER_SIGNED_URL: "/delete-video-answer-signed-url",
    REVIEW: "/review",
  },

  DASHBOARD: {
    BASE: "/dashboard",
    GET: "/get",
  },

  POINTS: {
    BASE: "/points",
    GET: "/get",
    GET_BY_WO_TYPE: "/get-by-wo-type",
    HISTORY: "/history",
    BALANCE: "/balance",
    RATE: {
      BASE: "/rate",
      DEFINE_PER_POINTS: "/define-per-points",
      GET: "/get",
    },
    DEFINE_POINTS_PER_WO_TYPE: "/define-per-wo-type",
    REDEEM: "/redeem-earnings",
    REDEEM_HISTORY: "/redeemed-earnings-history",
  },

  REPORTS: {
    BASE: "/reports",
    ORDER: "/order",
  },

  SPOT_LIGHT: {
    BASE: "/spotlight",
    PRO_SCORE: "/pro-score",
  },

  REPORT_TAGS: {
    BASE: "/report-tags",
    CREATE: "/create",
    GET: "/get",
    EDIT: "/edit",
  },

  HANDOVER_QUESTION: {
    BASE: "/handover-question",
    CREATE: "/create",
    GET: "/get",
    UPDATE_STATUS: "/update-status",
  },

  HANDOVER_WORKORDER: {
    BASE: "/handover-workorder",
    ADD: "/add",
    GET: "/get",
    GET_WORK_ORDER_QUESTION: "/question",
    SUBMIT_ANSWER: "/submit-answer",
    CHECK_IN: "/check-in",
    CHECK_OUT: "/check-out",
    SEND_OTP: "/send-otp",
    VERIFY_OTP: "/verify-otp",
    ADD_SIGNATURE: "/add-signature",
    UPDATE: "/update",
    REJECT: "/reject",
    RAISE_COMPLAINT: "/raise-complaint",
  },
};

module.exports = {
  API_PATH,
};
