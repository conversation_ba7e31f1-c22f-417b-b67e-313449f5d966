const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");

// Create a transport for daily log files
const dailyRotateTransport = new DailyRotateFile({
    filename: "logs/%DATE%.log",
    datePattern: "YYYYMMDD",
    zippedArchive: true,
    // maxSize: "20m", // optional: rotate files if they exceed 20 megabytes
    // maxFiles: "14d",  // optional: keep logs for 14 days
    auditFile: "logs/audit.json", // optional: keeps track of rotated files
});

// Define a custom format
const customFormat = winston.format.printf(({ level, message, timestamp, label, stack }) => {
    const logObject = { level, message, timestamp, label };

    // Add stack trace information if available (for errors)
    if (stack) {
        logObject.stack = stack;
    }

    return JSON.stringify(logObject, null, 2); // 2 spaces for indentation
});

// Configure Winston with custom format
const logger = winston.createLogger({
    level: "error", // Log only errors
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }), // Capture stack trace for errors
        customFormat
    ),
    transports: [
        dailyRotateTransport,
        new winston.transports.Console(),
    ]
});

module.exports = {
    logger
}
