const cron = require("node-cron");
const zoho = require("./zohoApis.js");
const { Op } = require("sequelize");

const db = require("../database/models");
const common = require("../controller/commonFunctions.js");
const { CONSTANTS } = require("./constants");
const { logger } = require("./logger");
const { sync } = require("../controller/orderController");
const { ATTRIBUTES } = require("./dbAttributes");
const userService = require("./userAuthenticationServiceApis.js");
// const { getCronExpression } = require("./functions");
const aws = require("../helpers/awsS3.js");
const { replaceData } = require("./functions.js");
const { sendNotification } = require("./notification.js");
const axios = require("axios");

const fetchOverdueWorkOrderCron = async () => {
  try {
    // const cronExpression = await getCronExpression("daily", 1);
    // fetch all work orders with pending status & past schedule date.
    const pendingWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.PENDING
    );

    const work_orders = await db.WorkOrders.findAll({
      where: {
        work_order_status_id: pendingWOStatus?.id,
        scheduled_start_date: { [Op.lt]: new Date() },
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (work_orders?.length) {
      const wo_ids = await work_orders.map((wo) => wo.id);

      if (wo_ids?.length) {
        const overdueWOStatus = await common.getWorkOrderStatusByTitle(
          CONSTANTS.WORK_ORDER.STATUS.OVERDUE
        );

        await db.WorkOrders.update(
          { work_order_status_id: overdueWOStatus?.id },
          { where: { id: { [Op.in]: wo_ids } } }
        );
      }
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const fetchZohoOrderCron = async () => {
  try {
    // const cronExpression = await getCronExpression("minutes", 20);
    await zoho.getNewAndUpdatedOrders();
    await sync();
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const fetchSurveyFeedbackAndApprovalCron = async () => {
  try {
    // const cronExpression = await getCronExpression("minutes", 20);
    const surveyWoType = await common.getWorkOrderTypeByTitle(
      CONSTANTS.WORK_ORDER.TYPE.SURVEY
    );

    if (surveyWoType) {
      // get all work orders which have survey type
      const workOrders = await db.WorkOrders.findAll({
        where: {
          work_order_type_id: surveyWoType.id,
          zoho_status: {
            [Op.or]: [
              null,
              CONSTANTS.WORK_ORDER.ZOHO_STATUS.ONGOING,
              CONSTANTS.WORK_ORDER.ZOHO_STATUS.PENDING,
              CONSTANTS.WORK_ORDER.ZOHO_STATUS.IN_PROGRESS,
            ],
          },
        },
        attributes: ATTRIBUTES.WORK_ORDERS.filter(
          (value) => value !== "selfie_image_base64"
        ),
      });

      if (workOrders.length) {
        // Work Order Status
        const woStatus = await db.WorkOrderStatus.findAll({
          attributes: ATTRIBUTES.WORK_ORDER_STATUS,
          raw: true,
        });

        const [completedWoStatus, rejectedWoStatus] = await Promise.all([
          woStatus.find(
            (wos) => wos.title === CONSTANTS.WORK_ORDER.STATUS.COMPLETED
          ),
          woStatus.find(
            (wos) => wos.title === CONSTANTS.WORK_ORDER.STATUS.REJECTED
          ),
        ]);

        for (let i = 0; i < workOrders.length; i++) {
          if (workOrders[i].inserted_zoho_wo_id) {
            const zohoWoData = await zoho.fetchZohoWorkOrderDetails(
              workOrders[i].inserted_zoho_wo_id
            );

            if (zohoWoData) {
              if (
                zohoWoData.Status === CONSTANTS.WORK_ORDER.ZOHO_STATUS.COMPLETED
              ) {
                // auto approve work order
                await db.WorkOrders.update(
                  {
                    work_order_status_id: completedWoStatus.id,
                    zoho_status: zohoWoData.Status,
                  },
                  { where: { id: workOrders[i].id } }
                );
              } else if (
                zohoWoData.Status === CONSTANTS.WORK_ORDER.ZOHO_STATUS.REJECTED
              ) {
                // auto reject work order
                await db.WorkOrders.update(
                  {
                    work_order_status_id: rejectedWoStatus.id,
                    zoho_status: zohoWoData.Status,
                    customer_feedback: zohoWoData?.Customer_Feedback,
                  },
                  { where: { id: workOrders[i].id } }
                );
              } else if (
                zohoWoData.Customer_Feedback &&
                [
                  CONSTANTS.WORK_ORDER.ZOHO_STATUS.PENDING,
                  CONSTANTS.WORK_ORDER.ZOHO_STATUS.ONGOING,
                  CONSTANTS.WORK_ORDER.ZOHO_STATUS.IN_PROGRESS,
                ].includes(zohoWoData.Status)
              ) {
                await db.WorkOrders.update(
                  {
                    customer_feedback: zohoWoData.Customer_Feedback,
                    zoho_status: zohoWoData.Status,
                  },
                  { where: { id: workOrders[i].id } }
                );
              }
            }
          }
        }
      }
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const deleteInactiveTaskImageAnswers = async () => {
  try {
    const deletedTaskImageAnswers = await db.TaskImageAnswers.findAll({
      where: { is_active: 0, is_deleted: 1 },
      attributes: ["id", "image_key"],
      raw: true,
    });

    if (deletedTaskImageAnswers.length) {
      for (const data of deletedTaskImageAnswers) {
        await aws.deleteFileS3(data.image_key);
      }

      const image_answer_ids = deletedTaskImageAnswers.map((data) => data.id);
      await db.TaskImageAnswers.destroy({
        where: { id: { [Op.in]: image_answer_ids } },
      });
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const deleteInactiveTaskVideoAnswers = async () => {
  try {
    const deletedTaskVideoAnswers = await db.TaskVideoAnswers.findAll({
      where: {
        [Op.or]: [{ is_active: 0, is_deleted: 1 }, { is_video_uploaded: 0 }],
      },
      attributes: ["id", "video_key"],
      raw: true,
    });

    if (deletedTaskVideoAnswers.length) {
      for (const data of deletedTaskVideoAnswers) {
        await aws.deleteFileS3(data.video_key);
      }

      const video_answer_ids = deletedTaskVideoAnswers.map((data) => data.id);
      await db.TaskVideoAnswers.destroy({
        where: { id: { [Op.in]: video_answer_ids } },
      });
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const sendNotificationForWorkOrderReview = async () => {
  try {
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const fortyEightHoursAgo = new Date();
    fortyEightHoursAgo.setHours(fortyEightHoursAgo.getHours() - 48);

    const workOrderReviewrData = await db.WorkOrderReviewer.findAll({
      where: {
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
        createdAt: {
          [Op.between]: [fortyEightHoursAgo, twentyFourHoursAgo],
        },
      },
    });

    //send reminder to review workorder
    if (workOrderReviewrData?.length) {
      const userIds = [
        ...new Set(workOrderReviewrData?.map((data) => data?.reviewer_user_id)),
      ];
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: [userIds],
        headers: {
          platform: "ios",
          token: "123",
        },
      });

      const templateAPI = await userService.getTemplate({
        headers: {
          platform: "ios",
          token: "123",
        },
        action: [
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REMINDER,
        ],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        workOrderReviewrData?.forEach(async (item) => {
          let replaceBody = await replaceData(
            body,
            "{WORK_ORDER_ID}",
            item?.work_order_id
          );

          const userData = await userAPI.find(
            (data) => data.id === item?.reviewer_user_id
          );

          const workOrderData = await db.WorkOrders.findOne({
            where: { id: item?.work_order_id },
          });

          const addAlertAPI = await userService.addAlerts({
            headers: {
              platform: "ios",
              token: "123",
            },
            sender_user_id: workOrderData?.assigned_to_user_id,
            title,
            body: replaceBody,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
                .WORK_ORDER_REVIEW_REMINDER,
            receiver_and_send_to: [
              {
                receiver_user_id: userData?.id,
                send_to: userData?.device_token,
                loginData: userData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (userData?.loginHistory) {
            notificationResponse = await sendNotification(
              [userData?.device_token],
              title,
              replaceBody
            );

            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        });

        console.log("--- WORK ORDER REVIEW NOTIFICATION SENT SUCCESSFULLY ---");
      }
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const updateWorkOrderReviewerStatus = async () => {
  try {
    const fortyEightHoursAgo = new Date();
    fortyEightHoursAgo.setHours(fortyEightHoursAgo.getHours() - 48);

    const workOrderReviewrData = await db.WorkOrderReviewer.findAll({
      where: {
        status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.ONGOING,
        createdAt: {
          [Op.lt]: fortyEightHoursAgo,
        },
      },
    });

    //update status to rejected and send notification to se/sh
    if (workOrderReviewrData?.length) {
      const ids = workOrderReviewrData?.map((item) => item?.id);

      //update status to rejected
      await db.WorkOrderReviewer.update(
        { status: CONSTANTS.WORK_ORDER.SE_SH_REVIEW_STATUS.REJECTED },
        {
          where: { id: { [Op.in]: ids } },
        }
      );

      //send notification
      const userIds = [
        ...new Set(workOrderReviewrData?.map((data) => data?.reviewer_user_id)),
      ];

      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: [userIds],
        headers: {
          platform: "ios",
          token: "123",
        },
      });

      const templateAPI = await userService.getTemplate({
        headers: {
          platform: "ios",
          token: "123",
        },
        action: [
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REMOVAL,
        ],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (templateAPI) {
        const { title, body } = templateAPI[0];
        workOrderReviewrData?.forEach(async (item) => {
          let replaceBody = await replaceData(
            body,
            "{WORK_ORDER_ID}",
            item?.work_order_id
          );

          const userData = await userAPI.find(
            (data) => data.id === item?.reviewer_user_id
          );

          const workOrderData = await db.WorkOrders.findOne({
            where: { id: item?.work_order_id },
          });

          const addAlertAPI = await userService.addAlerts({
            headers: {
              platform: "ios",
              token: "123",
            },
            sender_user_id: workOrderData?.assigned_to_user_id,
            title,
            body: replaceBody,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_REVIEW_REMOVAL,
            receiver_and_send_to: [
              {
                receiver_user_id: userData?.id,
                send_to: userData?.device_token,
                loginData: userData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (userData?.loginHistory) {
            notificationResponse = await sendNotification(
              [userData?.device_token],
              title,
              replaceBody
            );

            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        });

        console.log(
          "--- WORK ORDER REVIEW REMOVAL UPDATED AND SEND NOTIFICATION SENT SUCCESSFULLY ---"
        );
      }
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const updateInternalTeamDataInWorkOrder = async () => {
  try {
    const pendingWoStatus = await db.WorkOrderStatus.findOne({
      where: { title: CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS },
    });
    const workOrderData = await db.WorkOrders.findAll({
      where: {
        work_order_status_id: pendingWoStatus?.id,
        internal_team_id: { [Op.ne]: null },
        internal_team_approval_status: true,
      },
    });

    if (workOrderData?.length) {
      const ids = workOrderData?.map((data) => data?.id);

      //updae the internal team id and staus
      await db.WorkOrders.update(
        { internal_team_id: null, internal_team_approval_status: false },
        {
          where: { id: { [Op.in]: ids } },
        }
      );

      const assigneUserIds = workOrderData?.map(
        (data) => data?.assigned_to_user_id
      );
      const internalTeamIds = workOrderData?.map(
        (data) => data?.internal_team_id
      );
      const userIds = [...new Set([...assigneUserIds, ...internalTeamIds])];

      //get user data
      const userAPI = await userService.getUserDetailsByUserIds({
        user_ids: userIds,
        headers: {
          platform: "ios",
          token: "123",
        },
      });

      //send notification to supervisor
      const supervisorTemplateAPI = await userService.getTemplate({
        headers: {
          platform: "ios",
          token: "123",
        },
        action: [
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
            .WORK_ORDER_TAG_REMOVAL_SUPERVISOR,
        ],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      //send notification to SE/SH
      const SeShTemplateAPI = await userService.getTemplate({
        headers: {
          platform: "ios",
          token: "123",
        },
        action: [
          CONSTANTS.TEMPLATES.ACTION.NOTIFICATION.WORK_ORDER_TAG_REMOVAL_SE_SH,
        ],
        type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
      });

      if (supervisorTemplateAPI) {
        const { title, body } = supervisorTemplateAPI[0];
        workOrderData?.forEach(async (wo) => {
          let replaceBody = await replaceData(
            body,
            "{WORK_ORDER_ID}",
            wo?.actual_work_order_id
          );

          const userData = await userAPI.find(
            (data) => data.id == wo?.assigned_to_user_id
          );

          const internalTeamData = await userAPI.find(
            (data) => data.id == wo?.internal_team_id
          );

          replaceBody = await replaceData(
            replaceBody,
            "{USER_NAME}",
            `${internalTeamData?.first_name} ${internalTeamData?.last_name}`
          );

          const addAlertAPI = await userService.addAlerts({
            headers: {
              platform: "ios",
              token: "123",
            },
            sender_user_id: wo?.assigned_by_user_id,
            title,
            body: replaceBody,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
                .WORK_ORDER_TAG_REMOVAL_SUPERVISOR,
            receiver_and_send_to: [
              {
                receiver_user_id: userData?.id,
                send_to: userData?.device_token,
                loginData: userData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (userData?.loginHistory) {
            notificationResponse = await sendNotification(
              [userData?.device_token],
              title,
              replaceBody
            );

            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        });
      }

      if (SeShTemplateAPI) {
        const { title, body } = SeShTemplateAPI[0];
        workOrderData?.forEach(async (wo) => {
          let replaceBodySeSh = await replaceData(
            body,
            "{WORK_ORDER_ID}",
            wo?.actual_work_order_id
          );

          const userData = await userAPI.find(
            (data) => data.id == wo?.internal_team_id
          );

          const addAlertAPI = await userService.addAlerts({
            headers: {
              platform: "ios",
              token: "123",
            },
            sender_user_id: wo?.assigned_to_user_id,
            title,
            body: replaceBodySeSh,
            action:
              CONSTANTS.TEMPLATES.ACTION.NOTIFICATION
                .WORK_ORDER_TAG_REMOVAL_SE_SH,
            receiver_and_send_to: [
              {
                receiver_user_id: userData?.id,
                send_to: userData?.device_token,
                loginData: userData?.loginHistory,
              },
            ],
            type: CONSTANTS.TEMPLATES.TYPES.NOTIFICATION,
          });

          let notificationResponse;
          if (userData?.loginHistory) {
            notificationResponse = await sendNotification(
              [userData?.device_token],
              title,
              replaceBodySeSh
            );

            if (notificationResponse[0].success) {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 1,
                response: notificationResponse[0].message,
              });
            } else {
              await userService.updateAlert({
                headers: {
                  platform: "ios",
                  token: "123",
                },
                id: addAlertAPI.inserted_data[0].id,
                is_sent: 0,
                response: notificationResponse[0].message.toString(),
              });
            }
          }
        });
      }

      console.log("--- WORK ORDER TAG REMOVAL SUCCESSFULLY ---");
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const changeOrderStatusToCompleteBasedOnZohoStatus = async () => {
  try {
    const token = await zoho.getZohoAuthToken();
    const fields = "id,Stage";
    let allRecords = [];
    let page = 1;
    let moreRecords = true;
    let pageToken = null;

    //get all the order with Handover stage
    while (moreRecords) {
      const criteria = "(Stage:equals:Handover)";
      const url = `${
        process.env.ZOHO_API_URL
      }v5/Deals/search?fields=${fields}&criteria=${encodeURIComponent(
        criteria
      )}&${pageToken ? `page_token=${pageToken}` : `page=${page}`}`;
      console.log("url: ", url);
      const config = {
        method: "GET",
        url,
        headers: {
          Authorization: `Zoho-oauthtoken ${token}`,
        },
      };
      const response = await axios(config);
      if (response?.data?.data) {
        allRecords = allRecords.concat(response?.data?.data);
        page++; // Move to next page
        moreRecords = response?.data?.info?.more_records; // Check if more data exists
        pageToken = response?.data?.info?.next_page_token;
      } else {
        moreRecords = false; // Stop if no data found
      }
    }

    if (allRecords?.length) {
      const ids = allRecords?.map((item) => item?.id);

      const newOrderStatus = await common.getOrderStatusByTitle(
        CONSTANTS.ORDER_STATUS.NEW
      );

      //get all the order from db were status is new and id array
      const newOrderData = await db.Orders.findAll({
        where: {
          order_status_id: newOrderStatus?.id,
          is_active: 1,
          is_deleted: 0,
          zoho_order_id: { [Op.in]: ids },
        },
        include: [
          {
            model: db.WorkOrders,
            as: "work_order_data",
          },
        ],
      });

      if (!newOrderData?.length) {
        console.log("--- NO ORDER FOUND ---");
        return;
      }

      //filter order without work order
      const orderDataWithOutWorkOrder = newOrderData?.filter(
        (item) => item?.work_order_data?.length === 0
      );

      if (orderDataWithOutWorkOrder?.length > 0) {
        //get completd order status data
        const completdStatus = await common.getOrderStatusByTitle(
          CONSTANTS.ORDER_STATUS.COMPLETED
        );

        await db.Orders.update(
          { order_status_id: completdStatus?.id },
          { where: { zoho_order_id: { [Op.in]: handoverOrder } } }
        );
        console.log("--- ORDER STATUS UPDATED SUCCESSFULLY ---");
      } else {
        console.log("--- NO ORDER FOUND FOR STATUS UPDATE ---");
      }
    } else {
      console.log("--- NO ORDER FOUND FOR STATUS UPDATE ---");
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const fetchOverdueHandoverWorkOrderCron = async () => {
  try {
    // fetch all handover work orders with pending status & past schedule date.
    const pendingWOStatus = await common.getWorkOrderStatusByTitle(
      CONSTANTS.WORK_ORDER.STATUS.PENDING
    );

    const work_orders = await db.HandoverWorkOrders.findAll({
      where: {
        work_order_status: pendingWOStatus?.id,
        scheduled_start_date: { [Op.lt]: new Date() },
        is_active: 1,
        is_deleted: 0,
      },
    });

    if (work_orders?.length) {
      const wo_ids = await work_orders.map((wo) => wo.id);

      if (wo_ids?.length) {
        const overdueWOStatus = await common.getWorkOrderStatusByTitle(
          CONSTANTS.WORK_ORDER.STATUS.OVERDUE
        );

        await db.HandoverWorkOrders.update(
          { work_order_status: overdueWOStatus?.id },
          { where: { id: { [Op.in]: wo_ids } } }
        );
      }
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const checkoutHandoverWorkorder = async () => {
  try {
    const inProgressWoStatus = await db.WorkOrderStatus.findOne({
      where: { title: CONSTANTS.WORK_ORDER.STATUS.IN_PROGRESS },
    });
    const workOrderData = await db.HandoverWorkOrders.findAll({
      where: {
        work_order_status: inProgressWoStatus?.id,
      },
    });

    if (workOrderData?.length) {
      const ids = workOrderData?.map((data) => data?.id);

      //updae the internal team id and staus
      await db.HandoverWorkOrders.update(
        { check_in_user_id: null },
        {
          where: { id: { [Op.in]: ids } },
        }
      );
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

const deleteInactiveHandoverImageAnswers = async () => {
  try {
    const deleteHandoverImageAnswers = await db.HandoverImageNaswers.findAll({
      where: { is_active: 0, is_deleted: 1 },
      attributes: ["id", "image_key"],
      raw: true,
    });

    if (deleteHandoverImageAnswers.length) {
      for (const data of deleteHandoverImageAnswers) {
        await aws.deleteFileS3(data.image_key);
      }

      const image_answer_ids = deleteHandoverImageAnswers.map(
        (data) => data.id
      );
      await db.HandoverImageNaswers.destroy({
        where: { id: { [Op.in]: image_answer_ids } },
      });
    }
  } catch (error) {
    logger.error({
      message: `CRON_ERROR: ${error.message}`,
      stack: error.stack,
    });
  }
};

cron.schedule(
  "0 0 */1 * *",
  async () => {
    await fetchOverdueWorkOrderCron();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "*/20 * * * *",
  async () => {
    await fetchZohoOrderCron();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "*/20 * * * *",
  async () => {
    await fetchSurveyFeedbackAndApprovalCron();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "*/30 * * * *",
  async () => {
    await deleteInactiveTaskImageAnswers();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "*/30 * * * *",
  async () => {
    await deleteInactiveTaskVideoAnswers();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 * * *",
  async () => {
    await sendNotificationForWorkOrderReview();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 * * *",
  async () => {
    await updateWorkOrderReviewerStatus();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 * * *",
  async () => {
    await updateInternalTeamDataInWorkOrder();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 * * *",
  async () => {
    await changeOrderStatusToCompleteBasedOnZohoStatus();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 */1 * *",
  async () => {
    await fetchOverdueHandoverWorkOrderCron();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "0 0 */1 * *",
  async () => {
    await checkoutHandoverWorkorder();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);

cron.schedule(
  "*/30 * * * *",
  async () => {
    await deleteInactiveHandoverImageAnswers();
  },
  {
    scheduled: true,
    timezone: CONSTANTS.TIMEZONE.IST,
  }
);
