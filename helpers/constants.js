const CONSTANTS = {
  SORT_BY: {
    ASC: "ASC",
    DESC: "DESC",
  },
  RESPONSE_STATUS: {
    SUCCESS: 1,
    FAILED: 0,
  },

  PAGINATION: {
    PAGE: 1,
    LIMIT: 10,
  },

  HEADERS: {
    PLATFORM: {
      KEY: "platform",
      VALUES: {
        ANDROID: "android",
        IOS: "ios",
        WEB: "web",
      },
    },
  },

  DATA_SOURCE: {
    CMS: "CMS",
    APP: "APP",
    ZOHO: "ZOHO",
  },

  ROLES: {
    NAMES: {
      SUPER_ADMIN: "Super Admin",
      ADMIN: "Admin",
      INTERNAL_TEAM: "Internal Team",
      SERVICE_HEAD: "Service Head",
      SERVICE_ENGINEER: "Service Engineer",
      BUSINESS_ASSOCIATES: "Business Associates",
      FABRICATOR: "Fabricator",
      SUPERVISOR: "Supervisor",
    },
    SCOPE: {
      APP: "APP",
      CMS: "CMS",
      BOTH: "BOTH",
    },
    TYPES: {
      PARENT: "PARENT",
      CHILD: "CHILD",
    },
  },

  ORDER_STATUS: {
    NEW: "New",
    ONGOING: "Ongoing",
    COMPLETED: "Completed",
  },

  ORDER_IMAGE_UPLOAD_STATUS: ["QC In Progress", "Handover", "QC Approval"],

  ORDER_AMOUNT: 500000,
  TASK_STATUS: {
    APPROVED: "Approved",
    PENDING: "Pending",
    COMPLETED: "Completed",
    REJECTED: "Rejected",
    NOT_REVIEWED: "Not Reviewed",
    APPROVED_BY_SE: "Approved By Service Engineer",
    APPROVED_BY_SH: "Approved By Service Head",
  },

  WORK_ORDER: {
    STATUS: {
      PENDING: "Pending", //newly created work order have default pending status
      IN_PROGRESS: "In Progress", // If supervisor check In the Work Order, its status will change to In progress until Check out
      UNDER_APPROVAL: "Under Approval", // Post Work Order checkout, Work Order needs to be approved by Internal team this stage is under approval
      REJECTED: "Rejected", // Internal team rejects the Work Order
      COMPLETED: "Completed", // Internal team approves the work Order
      OVERDUE: "Overdue", // If Work Order is not started and scheduled date passed
      RE_ASSIGNED: "Re-assigned",
      ARCHIVED: "Archived",
      PARTIALLY_SUBMITTED: "Partially Submitted",
      SERVICE_ENGINEER_APPROVED: "Service Engineer Approved",
      PARTIALLY_APPROVED: "Partially Approved",
      PARTIALLY_APPROVED_BY_SE: "Partially Approved By Service Engineer",
      PARTIALLY_APPROVED_BY_SH: "Partially Approved By Service Head",
    },
    SE_SH_REVIEW_STATUS: {
      ONGOING: "Ongoing",
      COMPLETED: "Completed",
      REJECTED: "Rejected",
      CANCELLED: "Cancelled",
    },

    TYPE: {
      SURVEY: "Survey",
      QUALITY_CHECK: "Quality Check",
      HANDOVER: "Handover",
    },

    ORDER_BY: {
      ID: "id",
      UPDATED_AT: "updatedAt",
      CREATED_AT: "createdAt",
    },
  },

  ZOHO_STATUS: {
    PENDING: "Pending",
    ONGOING: "Ongoing",
    COMPLETED: "Completed",
    REJECTED: "Rejected",
    IN_PROGRESS: "In Progress",
  },

  QUESTION_TYPES: {
    SINGLE: "Single",
    MULTIPLE: "Multiple",
    DESCRIPTIVE: "Descriptive",
  },

  HANDOVER_QUESTION_TYPES: {
    SINGLE: "Single",
    MULTIPLE: "Multiple",
    DESCRIPTIVE: "Descriptive",
    RATING: "Rating",
    BOOLEAN: "Boolean",
  },

  HANDOVER_WORK_ORDER_STATUS: {
    PENDING: "Pending",
    IN_PROGRESS: "In Progress",
    COMPLETED: "Completed", // Internal team approves the work Order
    OVERDUE: "Overdue", // If Work Order is not started and scheduled date passed
  },

  QUESTION_CONDITION: {
    ALWAYS: "ALWAYS",
    EQUALS: "EQUALS",
    GREATER_THAN: "GREATER_THAN",
    LESS_THAN: "LESS_THAN",
    CONTAINS: "CONTAINS",
    NOT_EQUALS: "NOT_EQUALS",
  },

  DATE_FORMATS: {
    APPEND_TO_FILE: "yyyyMMDDhhmmss",
  },

  OTP: {
    LENGTH: 6,
    ACTION: {
      SUBMIT_WORK_ORDER: "SUBMIT_WORK_ORDER",
    },
    EXPIRES_IN: 5, // minutes
    RESEND_OPT_DURATION: {
      FIRST: 30, // seconds
      SECOND: 60, // seconds
      THIRD_OR_GREATER: 180, // seconds
    },
    REQUEST_COUNT: 3,
    SEND_OPTIONS: {
      MOBILE: "MOBILE",
      EMAIL: "EMAIL",
    },
    HANDOVER_EXPIRES_IN: 30,
  },

  FILE_SIZE_LIMIT: {
    // NOTE: Values mentioned in MB.
    DEFAULT_MAX: 5,
    IMAGE: {
      MAX: 5,
    },
  },

  ZOHO: {
    ORDER_STAGE: {
      ORDER_CONFIRMED: {
        SYNC_REQUIRED: true,
        TITLE: "Order Confirmed",
      },
      FABRICATION_ONGOING: {
        SYNC_REQUIRED: true,
        TITLE: "Fabrication Ongoing",
      },
      INSTALLATION_ONGOING: {
        SYNC_REQUIRED: true,
        TITLE: "Installation Ongoing",
      },
      QC_IN_PROGRESS: {
        SYNC_REQUIRED: true,
        TITLE: "QC In Progress",
        ID: "158127000002118508",
      },
      QC_APPROVAL: {
        SYNC_REQUIRED: true,
        TITLE: "QC Approval",
        ID: "158127000037256117",
        CHECKLIST: [
          {
            "I have completed quality check and found the installation on site to be as per Eternia guidelines and I have handed over the site to the cus": true,
          },
          {
            "I Have uploaded the QC files and handover file on zoho": true,
          },
          {
            "Eternia has the right to review the QC file and direct corrections to be made at site": true,
          },
        ],
      },
      HANDOVER: {
        SYNC_REQUIRED: true,
        TITLE: "Handover",
      },

      REVIEW_REQUIREMENT_GATHERING: {
        SYNC_REQUIRED: false,
        TITLE: "Review & Requirement Gathering",
      },
      QUOTATION_IN_PROGRESS: {
        SYNC_REQUIRED: false,
        TITLE: "Quotation in Progress",
      },
      ORDER_CONFIRMATION_AWAITED: {
        SYNC_REQUIRED: false,
        TITLE: "Order Confirmation Awaited",
      },
      WARRANTY: {
        SYNC_REQUIRED: false,
        TITLE: "Warranty",
        ID: "158127000037265384",
        CHECKLIST: [
          {
            "I Have uploaded the  handover file on zoho": true,
          },
        ],
      },
      ETERNIA_CARE: {
        SYNC_REQUIRED: false,
        TITLE: "Eternia Care",
      },
      CLOSED_LOST: {
        SYNC_REQUIRED: false,
        TITLE: "Closed Lost",
      },
      CLOSED_LOST_TO_COMPETITION: {
        SYNC_REQUIRED: false,
        TITLE: "Closed-Lost to Competition",
      },
    },
    AUTH_TOKEN_REGENERATE_DURATION: 55, // minutes
    QC_MODE: {
      APP: "App",
      MANUAL: "Manual",
    },
    HANDOVER_QUESTION: [
      {
        question:
          "After experiencing the full journey, what did you like the most about Eternia?",
        key: "What_did_you_like_the_most_about_Eternia",
      },
      {
        question: "What attracted you to Eternia?",
        key: "What_attracted_you_to_Eternia",
      },
      {
        question: "What did you like the least about Eternia?",
        key: "What_did_you_like_least_about_Eternia",
      },
      {
        question:
          "How likely are you to recommend Eternia to your friends and family? (0-6 is never, 7-8: not sure, 9-10 definitely)",
        key: "Recommend_Eternia_to_friends_and_family",
      },
    ],
  },

  DASHBOARD: {
    LIST_LENGTH: 5,
  },

  CURRENCY: {
    INR: "INR",
  },

  POINTS: {
    TRANSACTION_TYPE: {
      CREDIT: "Credit",
      DEBIT: "Debit",
    },
  },

  MONTH_NAMES_ARR: [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ],

  TEMPLATES: {
    SCOPE: {
      APP: "APP",
      CMS: "CMS",
      BOTH: "BOTH",
    },
    AUTH_TOKEN_REGENERATE_DURATION: 55, // minutes
    TYPES: {
      PAGE: "PAGE",
      EMAIL: "EMAIL",
      NOTIFICATION: "NOTIFICATION",
      SMS: "SMS",
    },

    ACTION: {
      PAGE: {
        PRIVACY_POLICY: "PRIVACY_POLICY",
        TERMS_CONDITIONS: "TERMS_CONDITIONS",
        FAQ: "FAQ",
        ABOUT: "ABOUT",
        INTRO: "INTRO",
        CONTACT_US: "CONTACT_US",
      },
      EMAIL: {
        SEND_RESET_PASSWORD_OTP: "SEND_RESET_PASSWORD_OTP",
        SEND_EMAIL_VERIFICATION_OTP: "SEND_EMAIL_VERIFICATION_OTP",
        PROFILE_VERIFICATION_REQUEST: "PROFILE_VERIFICATION_REQUEST",
        PROFILE_VERIFICATION_APPROVED: "PROFILE_VERIFICATION_APPROVED",
        PROFILE_VERIFICATION_REJECTED: "PROFILE_VERIFICATION_REJECTED",
        PROFILE_VERIFICATION_REMINDER: "PROFILE_VERIFICATION_REMINDER",
        ADDRESS_CHANGE_REQUEST: "ADDRESS_CHANGE_REQUEST",
        WORK_ORDER_WARRANTY: "WORK_ORDER_WARRANTY",
        SEND_HANDOVER_WORKORDER_OTP: "SEND_HANDOVER_WORKORDER_OTP",
        HANDOVER_LINK: "HANDOVER_LINK",
      },
      SMS: {
        SEND_MOBILE_VERIFICATION_OTP: "SEND_MOBILE_VERIFICATION_OTP",
        SEND_RESET_PASSWORD_OTP: "SEND_RESET_PASSWORD_OTP",
        SEND_HANDOVER_WORKORDER_OTP: "SEND_HANDOVER_WORKORDER_OTP",
        SEND_HANDOVER_LINK: "SEND_HANDOVER_LINK",
      },
      NOTIFICATION: {
        PROFILE_VERIFICATION_REQUEST: "PROFILE_VERIFICATION_REQUEST",
        PROFILE_VERIFICATION_APPROVED: "PROFILE_VERIFICATION_APPROVED",
        PROFILE_VERIFICATION_REJECTED: "PROFILE_VERIFICATION_REJECTED",
        PROFILE_VERIFICATION_REMINDER: "PROFILE_VERIFICATION_REMINDER",
        ADDRESS_CHANGE_REQUEST: "ADDRESS_CHANGE_REQUEST",
        ADDRESS_CHANGE_REQUEST_APPROVED: "ADDRESS_CHANGE_REQUEST_APPROVED",
        ADDRESS_CHANGE_REQUEST_REJECTED: "ADDRESS_CHANGE_REQUEST_REJECTED",
        WORK_ORDER_ASSIGNED: "WORK_ORDER_ASSIGNED",
        WORK_ORDER_REVIEW_REQUEST: "WORK_ORDER_REVIEW_REQUEST",
        WORK_ORDER_APPROVED: "WORK_ORDER_APPROVED",
        WORK_ORDER_REJECTED: "WORK_ORDER_REJECTED",
        WORK_ORDER_UPDATED: "WORK_ORDER_UPDATED",
        WORK_ORDER_UNASSIGNED: "WORK_ORDER_UNASSIGNED",
        SUPERVISOR_TAG_REQUEST: "SUPERVISOR_TAG_REQUEST",
        SE_SH_TAG_ACCEPTED: "SE_SH_TAG_ACCEPTED",
        SE_SH_TAG_REJECTED: "SE_SH_TAG_REJECTED",
        WORK_ORDER_REVIEW_REMINDER: "WORK_ORDER_REVIEW_REMINDER",
        WORK_ORDER_REVIEW_REMOVAL: "WORK_ORDER_REVIEW_REMOVAL",
        WORK_ORDER_TAG_REMOVAL_SE_SH: "WORK_ORDER_TAG_REMOVAL_SE_SH",
        WORK_ORDER_TAG_REMOVAL_SUPERVISOR: "WORK_ORDER_TAG_REMOVAL_SUPERVISOR",
        HANDOVER_WORK_ORDER_ASSIGNED: "HANDOVER_WORK_ORDER_ASSIGNED",
      },
    },
  },

  SPACE: {
    GENERAL: {
      TITLE: "General",
    },
  },

  SCOPE: {
    GENERAL: {
      MODEL: "GE",
      NAME: "General",
    },
    DEFAULT_MODEL: "Default",
  },

  S3_BUCKET: {
    FOLDERS: {
      SCOPE: "scope",
      WORK_ORDER: {
        BASE: "work_order",
        SELFIE: "selfie",
        WARRANTY: "warranty",
      },
      HANDOVER_WORK_ORDER: {
        BASE: "handover_work_order",
        SIGNATURE: "signature",
      },
      TASK: {
        BASE: "task",
        VIDEOS: "videos",
      },
      ORDER: {
        BASE: "order",
      },
      PDF: {
        BASE: "pdf",
        WARRANTY: "warranty",
        WORK_ORDER: "work_order",
      },
    },
    SIGNED_URL: {
      EXIPRATION_TIME: 5, // minutes
    },
  },

  PDF: {
    JS_REPORT_TEMPLATE: {
      WARRANTY: "/warranty/warranty",
      WORK_ORDER: "/work-order/work-order",
    },

    SURVEY: {
      ACTION: {
        DRAFT: "Draft",
        FINAL: "Final",
      },
    },

    TEMPLATE_DATA: {
      WORK_ORDER: {
        TERMS: [
          "<strong>Purpose:</strong> The report provides an assessment of window drafts and is for informational use only.",
          "<strong>Confidentiality :</strong> Information in the report is confidential and intended for the client's personal use.",
          "<strong>Accuracy & Limitation of Liability:</strong> Efforts are made to ensure accuracy however, no liability for inaccuracies or omissions is accepted.",
          "<strong>Ownership:</strong> The report and all findings remain the property of the surveyor/company",
          "<strong>Use: </strong> The report should not be used for legal purposes without prior written consent",
        ],
      },
    },
  },

  TIMEZONE: {
    IST: "Asia/Kolkata",
  },

  RESRTICTED_PRODUCT_FILE_NAME: "restricted_products.json",

  HANDOVER_EMAIL_LINK: process.env.HANDOVER_EMAIL_LINK,

  WHATSAPP: {
    TEMPLATE_NAME: {
      HANDOVER_WORK_ORDER_INVITATION: "handover_invitation_2",
      HANDOVER_WORK_ORDER_OTP: "handover_otp_3",
      HANDOVER_WORK_ORDER_SUBMIT: "handover_submit_1",
    },
  },

  INSTALLATION_TASK_STEP: {
    TBS: "TBS",
    MATERIAL_SHORTFALL: "Material Shortfall",
    SILL_NOT_READY: "Sill Not Ready",
    ONLY_MESH_PENDING: "Only Mesh Pending",
    FINISHING_PENDING: "Finishing Pending",
    ONLY_FRAME_INSTALLED: "Only Frame Installed",
    CANCELLED: "Cancelled",
    DONE: "Done",
  },

  MASTER_DATA_TYPE: {
    STRING: "string",
    NUMBER: "number",
  },

  MASTER_DATA: {
    WORKFLOW_COMPLETION_TIME: {
      title: "Workflow Completion Time",
      value: 30,
      type: "number",
    },
  },

  SUMMARY_TYPE: {
    INSTALLATION: "installation",
    QC: "qc",
  },

  ZOHO_TICKET: {
    DEPARTMENT_ID: "33821000008015178",
    CF: {
      cf: {
        cf_issue_type: "During Order Execution",
        cf_issue_subtype: "Partner Behavior",
        cf_issue_list: "Handover filled without my consent",
      },
    },
  },

  ZOHO_TAG: {
    HANDOVER_FEEDBACK_ISSUE: "Handover Feedback Issue",
  },
};

module.exports = {
  CONSTANTS,
};
