const { CONSTANTS } = require("./constants");

const TRY_AGAIN = "Please try again later.";

const MESSAGE = {
  APK_UPDATE:
    "We’ve rolled out a new update with some exciting improvements. Update now to enjoy the latest version!",
  GENERAL_ERROR: `Something went wrong, ${TRY_AGAIN}`,
  TOKEN_NOT_FOUND: "Authorization token not found.",
  INVALID_TOKEN: "Invalid token.",
  INVALID_ACCESS: "Invalid Access.",
  HEADER: {
    MISSING: "Please provide required headers.",
    INVALID: "Invalid header.",
  },
  AUTHORIZATION_ERROR: `Something went wrong while authorizing request. ${TRY_AGAIN}`,
  ORDERS: {
    NOT_EXIST: "Order not exist.",
    ZOHO_ID_NOT_EXIST: "Zoho id not exist for this order.",
    FOUND: {
      SUCCESS: "Orders found successfully.",
      ERROR: `Something went wrong while fetching orders. ${TRY_AGAIN}`,
      FAILED: "No orders found.",
    },
    ZOHO_ORDERS: {
      FOUND: {
        ERROR: `Something went wrong while fetching order for sync. ${TRY_AGAIN}`,
        FAILED: "No orders available for sync.",
        SUCCESS: "Orders fetched successfully.",
      },
      UPDATE: {
        SUCCESS: "Orders updated successfully.",
        ERROR: `Something went wrong while updating orders. ${TRY_AGAIN}`,
      },
    },
    DELETE: {
      ID: "Please provide valid id.",
      NOT_EXIST: "Image doesn't exist. Please provide valid id.",
      SUCCESS: "Image deleted successfully.",
    },
    HANDOVER_EMAIL: {
      SUCCESS: "Email sent successfully.",
    },
    FETCH_SUCCESS: "Data fetched successfully.",
    NOT_FOUND: "Data not found.",
    OPPORTUNITY_ID_REQUIRED: "Opportunity id is required.",
  },
  SPACE: {
    EXIST: "Space exist. Please enter different space name.",
    NOT_EXIST: "Space not exist.",
    FOUND: {
      SUCCESS: "Spaces found successfully.",
      ERROR: `Something went wrong while fetching spaces. ${TRY_AGAIN}`,
      FAILED: "No spaces found.",
    },
    ADD: {
      SUCCESS: "Space added successfully.",
      ERROR: `Something went wrong while adding space. ${TRY_AGAIN}`,
    },
    UPDATE: {
      SUCCESS: "Space updated successfully.",
      ERROR: `Something went wrong while updating space. ${TRY_AGAIN}`,
    },
    STATUS_CHANGE: {
      SUCCESS: "Space status changed successfully.",
      ERROR: `Something went wrong while changing space status. ${TRY_AGAIN}`,
      FAILED_DUE_TO_LINKED_WO:
        "Space status can not be changed due to space is linked with Work Order.",
    },
  },
  SCOPE: {
    EXIST: "Scope exist. Please enter different scope model & name.",
    NOT_EXIST: "Scope not exist.",
    FOUND: {
      SUCCESS: "Scopes found successfully.",
      ERROR: `Something went wrong while fetching scopes. ${TRY_AGAIN}`,
      FAILED: "No scopes found.",
    },
    ADD: {
      SUCCESS: "Scope added successfully.",
      ERROR: `Something went wrong while adding scope. ${TRY_AGAIN}`,
    },
    UPDATE: {
      SUCCESS: "Scope updated successfully.",
      ERROR: `Something went wrong while updating scope. ${TRY_AGAIN}`,
    },
    STATUS_CHANGE: {
      SUCCESS: "Scope status changed successfully.",
      ERROR: `Something went wrong while changing scope status. ${TRY_AGAIN}`,
      FAILED_DUE_TO_SAME_EXIST:
        "This scope status can not be changed due to same scope is already exist and active.",
      FAILED_DUE_TO_LINKED_TO_WO_OR_QUESET:
        "Scope status can not be changed due to scope is linked with work order or question set.",
    },
  },
  WORK_ORDER_TYPE: {
    EXIST:
      "Work order type exist. Please enter different work order type name.",
    NOT_EXIST: "Work Order Type not exist.",
    ADD: {
      ERROR: `Something went wrong while adding work order type. ${TRY_AGAIN}`,
    },
    FOUND: {
      ERROR: `Something went wrong while fetching work order types. ${TRY_AGAIN}`,
      FAILED: "No work order types found.",
      SUCCESS: "Work order types found successfully.",
    },
    UPDATE: {
      SUCCESS: "Work order type updated successfully.",
      ERROR: `Something went wrong while updating work order type. ${TRY_AGAIN}`,
    },
    STATUS_CHANGE: {
      SUCCESS: "Work order type status changed successfully.",
      ERROR: `Something went wrong while changing work order type status. ${TRY_AGAIN}`,
      FAILED_DUE_TO_SAME_EXIST:
        "This work order type status can not be changed due to same wotk order type is already exist and active.",
      FAILED_DUE_TO_LINKED_TO_WO_OR_QUESET:
        "Work Order Type status can not be changed due to work order type is linked with work order or question set.",
    },
  },
  WORK_ORDER_TYPE_SKILL: {
    ADD: {
      SUCCESS: "Work order type & skills added successfully.",
      ERROR: `Something went wrong while adding work order type & skills. ${TRY_AGAIN}`,
    },
  },
  WORK_ORDER: {
    NOT_EXIST: "Work Order not exist.",
    ADD: {
      SUCCESS: "Work Order created successfully.",
      ERROR: `Something went wrong while creating work order. ${TRY_AGAIN}`,
      FAILED: {
        QUE_SET_NOT_EXIST: (data) =>
          `Scopes (${data}) does not have Question Mapping, please create mapping first to create work order.`,
        QC_NOT_COMPLETE:
          "Work order with handover type can not be created, as Quality Check is not completed for requested scopes.",
        PARENT_WO_NOT_AVAILABLE: (parentWo) =>
          `Work Order can not be created due to ${parentWo} not available for any scopes.`,
        PARENT_WO_NOT_COMPLETED: (parentWo) =>
          `Work Order can not be created due to ${parentWo} is not completed for any scopes.`,
        PARENT_WO_TASK_NOT_COMPLETED: (parentWo) =>
          `Work Order can not be created due to ${parentWo} tasks are not completed for any scopes.`,
      },
      IMAGE_TP_PENDNG: (data) =>
        `Scopes (${data}) doen not have image & touch points, please add them first to create the work order.`,

      EXIST_WITH_SCOPES_AND_SPACES: (data) =>
        `Work order already exists with this space id and scope id: ${data}`,
    },
    FOUND: {
      SUCCESS: "Work Order found successfully.",
      ERROR: `Something went wrong while fetching work orders. ${TRY_AGAIN}`,
      FAILED: "No work orders found.",
    },
    UPDATE: {
      ERROR_DUE_TO_STARTED:
        "This Work order can not be update due to it is started.",
      ERROR: `Something went wrong while updating work order. ${TRY_AGAIN}`,
      SUCCESS: "Work Order updated successfully.",
    },
    CHECKED_IN: {
      REQUEST_SEND_TO_SE_SH: "Internal team tag request sent.",
      REQUEST_ACCEPTED_SE_SH:
        "Internal team tag request accepted successfully.",
      REQUEST_REJECTED_SE_SH:
        "Internal team tag request rejected successfully.",
      ALREADY: "This work order is already checked in.",
      FAILED:
        "Work order can not be checked in as it is not in pending status.",
      SUCCESS: "Work order checked in successfully.",
      ERROR: `Something went wrong while checked in work order. ${TRY_AGAIN}`,
      UPLOAD_SELFIE: {
        EMPTY: "Please upload selfie to check in the work order.",
      },
    },
    TAG_REQUEST: {
      REQUEST_SEND_TO_SE_SH: "Internal team tag request sent.",
      ERROR: `Something went wrong while tag request in work order. ${TRY_AGAIN}`,
      FAILED: "Work order tag request cannot be processed.",
      ALREADY: "This work order already has a tag request.",
    },
    CHECK_OUT: {
      ZONE_NOT_FOUND: "Customer zone not found.",
      FAILED_1:
        "You can not check out this work order as Customer Otp verification is pending.",
      FAILED_2:
        "This work order can not be checked out due to all tasks are not completed yet.",
      FAILED_3:
        "This work order cannot be checked out as no tasks have been completed yet.",
      NO_USER_FOR_REVIEW: "No users available for review the work order.",
      SUCCESS: "Work order check out successfully.",
      ERROR: `Something went wrong while check out work order. ${TRY_AGAIN}`,
    },
    REVIEW: {
      FAILED: `This work order can not be reviewed. As it is not in ${CONSTANTS.WORK_ORDER.STATUS.UNDER_APPROVAL} or ${CONSTANTS.WORK_ORDER.STATUS.PARTIALLY_SUBMITTED}.`,
      ERROR: "Device not found for sending notificaction.",
      APPROVED: "Work order approved successfully.",
      REJECT: "Work order rejected successfully.",
      FAILED_DUE_TO_SURVEY: "Survey type work order can not be reviewed.",
      ALREADY_APPROVED_BY_SERVICE_ENGINEER:
        "Work order is already approved by service enginner.",
      ALL_TASK_COMPLETED:
        "All tasks must be completed to mark the work order as complete.",
      PLEASE_CHECK_IN: "Please check in first before reviewing this work order",
    },
    STATUS: {
      NOT_EXIST: "Work Order status not exist.",
      FOUND: {
        SUCCESS: "Work Order status found successfully.",
        ERROR: `Something went wrong while fetching work order status. ${TRY_AGAIN}`,
      },
    },
    SEND_PDF: {
      ERROR: `Something went wrong while generating pdf. ${TRY_AGAIN}`,
      FAILED: "Only Survey Work order PDF can be sent.",
      SUCCESS: "Pdf send successfully.",
    },
    DELETE: {
      ID_REQUIRE: "Work order id is required.",
      ERROR: "You can not delete thid Work order.It is not in pending status.",
      SUCCESS: "Work order deleted successfully.",
    },
    REVIEW_QUESTION_TP: {
      TASK_NOT_EXIST: "Task not exist.",
      TOUCH_POINT_NOT_EXIST: (tpIds) =>
        `This Touch point is not exist in this task: ${tpIds}`,
      QUESTION_NOT_EXIST: (queIds) =>
        `This Question is not exist with this touchpoint in this task: ${queIds}`,
      UPDATED: "Task question and touchpoint updated successfully.",
    },
    SE_SH_REVIEW: {
      FAILED:
        "Work order can not be checked in as it is not in under approval/ service engineer approved status.",
      ALREADY: "This work order is already checked in by se/sh.",
      SUCCESS: "Work order checked in successfully.",
      CANCEL: "Work order checked in cancelled successfully.",
      NOT_EXIST: "Reviewer data not exist.",
    },
  },
  QUESTION_TYPE: {
    NOT_EXIST: "Question Type not exist.",
    FOUND: {
      SUCCESS: "Question Types found successfully.",
      ERROR: `Something went wrong while fetching question types. ${TRY_AGAIN}`,
      FAILED: "No question types found.",
    },
  },
  QUESTION: {
    EXIST: "Question exist. Please enter different question.",
    ORDER_EXIST: "Question order exist. Please enter different order.",
    NOT_EXIST: "Question not exist.",
    DEPENDENT_QUESTION_REQUIRED: "Subquestion require dependent question",
    CONDITION_REQUIRED: "Condition is required for subquestion",
    OPTIONS: {
      REQUIRED: "Question's options are required.",
    },
    CREATE: {
      SUCCESS: "Question created successfully.",
      ERROR: `Something went wrong while creating question. ${TRY_AGAIN}`,
    },
    FOUND: {
      SUCCESS: "Questions found successfully.",
      ERROR: `Something went wrong while fetching questions. ${TRY_AGAIN}`,
      FAILED: "No questions found.",
    },
    UPDATE: {
      SUCCESS: "Question updated successfully.",
      ERROR: `Something went wrong while updating question. ${TRY_AGAIN}`,
    },
    COMMENT_INVALID_OPTION:
      "Please provide valid option for comment condition.",
    STATUS_CHANGE: {
      SUCCESS: "Question status changed successfully.",
      ERROR: `Something went wrong while changing question status. ${TRY_AGAIN}`,
      FAILED_DUE_TO_SAME_EXIST:
        "This question status can not be changed due to same question is already exist and active.",
      FAILED_DUE_TO_QUESTION_MAPPING_EXIST:
        "This question status can not be changed due to question exists in question mapping.",
    },
  },
  QUESTION_SET: {
    TOUCH_POINT_NOT_EXIST_IN_SCOPE:
      "This Touch point is not exist in this scope.",
    QUESTION_NOT_AVAILABLE:
      "Some of the question not exist. Please provide valid questions.",
    NOT_EXIST: "Question set not exist.",
    EXIST: "Question set with requested work order type & scope already exist.",
    CREATE: {
      SUCCESS: "Question set created successfully.",
      ERROR: `Something went wrong while creating question set. ${TRY_AGAIN}`,
    },
    EDIT: {
      SUCCESS: "Question set edited successfully.",
      TOUCH_POINT_NOT_EXIST_IN_SCOPE: (tpIds) =>
        `This Touch point is not exist in this scope: ${tpIds}`,
      QUESTION_NOT_AVAILABLE: (qIds) =>
        `This questions does not exist : ${qIds}. Please provide valid questions.`,
      QUESTION_EXIST_FOR_TOUCH_POINTS:
        "Some of the question exist for touchpoints. Please provide valid questions.",
      DELETE_QUESTION_NOT_EXIST:
        "Some of the delete question not exist for touchpoints. Please provide valid questions.",
    },
    STATUS_CHANGE: {
      SUCCESS: "Question set status changed successfully.",
      ERROR: `Something went wrong while changing question set status. ${TRY_AGAIN}`,
      FAILED_DUE_TO_SAME_EXIST:
        "This question set status can not be changed due to question set with same work order type & scope is already exist and active.",
      FAILED_DUE_TO_LINKED_TASK:
        "Question Set status can not be changed due to question set linked with task.",
    },
    FOUND: {
      SUCCESS: "Question set found successfully.",
      ERROR: `Something went wrong while fetching question set. ${TRY_AGAIN}`,
      FAILED: "No question set found.",
    },
  },
  TASK: {
    NOT_EXIST: "Task not exist.",
    IDS_NOT_EXIST: (ids) =>
      `This task id not exist: ${ids}, Please provide valid task ids.`,
    PROVIDE_ALL_IDS: "Please provide all task ids.",
    ONE_TASK_SHOULD_BE_APPROVE:
      "One task must be approved to approve this work order.",
    FOUND: {
      SUCCESS: "Tasks found successfully.",
      ERROR: `Something went wrong while fetching tasks. ${TRY_AGAIN}`,
      FAILED: "No tasks found.",
    },
    SUBMIT: {
      SUCCESS: "Task submitted successfully.",
      ERROR: `Something went wrong while submitting tasks. ${TRY_AGAIN}`,
      ALREADY_SUBMITTED: "Task answers are already submitted.",
      INVALID_IMAGE_COUNT: "Please upload the required number of images.",
    },
    UPLOAD_IMAGE: {
      SUCCESS: "Image uploaded successfully.",
      ERROR: `Something went wrong while uploading image. ${TRY_AGAIN}`,
    },
    SIGNED_URL: {
      NOT_EXIST: "Video doesn't exist. Please provide valid id.",
      UPDATE: {
        SUCCESS: "Video url updated successfully.",
        ERROR: `Something went wrong while generating signed url. ${TRY_AGAIN}`,
      },
      GENERATE: {
        SUCCESS: "Signed url generated successfully.",
        ERROR: `Something went wrong while generating signed url. ${TRY_AGAIN}`,
      },
      DELETE: {
        ID: "Please provide valid id.",
        NOT_EXIST: "Video doesn't exist. Please provide valid id.",
        SUCCESS: "Signed url deleted successfully.",
      },
    },
  },
  OTP: {
    SENT: {
      FAILED: "OTP can not be sent due to all tasks are not completed.",
      SUCCESS: "OTP sent successfully.",
      ERROR: `Something went wrong while sending OTP. ${TRY_AGAIN}`,
    },
    RESEND_DURATION: {
      FIRST: `You can request the new OTP after ${CONSTANTS.OTP.RESEND_OPT_DURATION.FIRST} seconds.`,
      SECOND: `You can request the new OTP after ${CONSTANTS.OTP.RESEND_OPT_DURATION.SECOND} seconds.`,
      THIRD_OR_GREATER: `You can request the new OTP after ${CONSTANTS.OTP.RESEND_OPT_DURATION.THIRD_OR_GREATER} seconds.`,
    },
    VERIFICATION: {
      SUCCESS: "OTP verified successfully.",
      ERROR: `Something went wrong while verifying OTP. ${TRY_AGAIN}`,
    },
    INVALID: "Invalid OTP.",
    EXPIRED: "OTP expired.",
  },
  ORDER: {
    HANDOVER_LINK_SUCCESS: "Handover link sent successfully.",
    NOT_EXIST: "Order not exist.",
    ZOHO_STATUS_NOT_FOUND: "Zoho status not found for this order.",
    STATUS: {
      NOT_EXIST: "Order status not exist.",
      FOUND: {
        SUCCESS: "Order status found successfully.",
        ERROR: `Something went wrong while fetching order status. ${TRY_AGAIN}`,
      },
    },
    UPDATE_FABRICATOR: {
      SUCCESS: "Fabricator data updated successfully.",
      ERROR: `Something went wrong while updating fabricator data. ${TRY_AGAIN}`,
      NO_DATA: "No orders available for update fabricator data.",
    },
    UPLOAD_IMAGE: {
      STATUS_INVALID: "Order status is invalid",
      SUCCESS: "Image uploaded successfully.",
      ERROR: `Something went wrong while uploading image. ${TRY_AGAIN}`,
    },
    HANDOVER_EMAIL: {
      SUCCESS: "Email sent successfully.",
    },
  },
  ASSIGNED_ORDER_AND_WO: {
    FOUND: {
      SUCCESS: "Assigned Work order or Order count found successfully.",
      ERROR: `Something went wrong while fetching count for assigned Work order or Order. ${TRY_AGAIN}`,
    },
  },
  POINTS: {
    RATE: {
      EXIST_WITH_INACTIVE_STATUS:
        "Point rate exits with inactive status. Please activate it.",
      EXIST: "Point rate already exist.",
      DEFINE: {
        SUCCESS: "Point rates defined successfullly.",
        ERROR: `Something went wrong while defining point rate. ${TRY_AGAIN}.`,
      },
      FOUND: {
        SUCCESS: "Rate found successfully.",
        ERROR: `Something went wrong while fetching rate. ${TRY_AGAIN}.`,
        FAILED: "No rates found.",
      },
    },
    DEFINE_WITH_WO_TYPE: {
      EXIST: "Points with work order type already exist.",
      SUCCESS: "Points for work order type defined successfully.",
      ERROR: `Something went wrong while defining points for work order type. ${TRY_AGAIN}.`,
    },
    FOUND: {
      SUCCESS: "Points found successfully.",
      ERROR: `Something went wrong while fetching points. ${TRY_AGAIN}`,
      FAILED: "No points found.",
    },
    HISTORY: {
      FOUND: {
        SUCCESS: "Points history found successfully.",
        ERROR: `Something went wrong while fetching points history. ${TRY_AGAIN}`,
        FAILED: "No points history found.",
      },
    },
    BALANCE: {
      FOUND: {
        SUCCESS: "Balance found successfully.",
        ERROR: `Something went wrong while fetching balance. ${TRY_AGAIN}`,
      },
    },
    EARNINGS: {
      REDEEM: {
        EMPTY: "User does not have any earnings to redeem.",
        INSUFFICIENT: "User does not have sufficient earnings to redeem.",
        SUCCESS: "Earnings redeemed successfully.",
        ERROR: `Something went wrong while redeeming earnings. ${TRY_AGAIN}`,
        HISTORY: {
          FOUND: {
            SUCCESS: "Redeemed earning history found successfully.",
            ERROR: `Something went wrong while fetching redeemed earning history. ${TRY_AGAIN}`,
            FAILED: "No Earning redeemed history found.",
          },
        },
      },
    },
  },
  REPORT_TAG: {
    EXIST: "Report tag exist. Please enter different name.",
    NOT_EXIST: "Report tag not exist.",
    FOUND: {
      SUCCESS: "Report tags found successfully.",
      ERROR: `Something went wrong while fetching Report tags. ${TRY_AGAIN}`,
      FAILED: "No Report tags found.",
    },
    CREATE: {
      SUCCESS: "Report tag created successfully.",
      ERROR: `Something went wrong while creating Report Tag. ${TRY_AGAIN}`,
    },
    EDIT: {
      SUCCESS: "Report tag edited successfully.",
      ERROR: `Something went wrong while editing Report Tag. ${TRY_AGAIN}`,
    },
  },
  HANDOVER_WORK_ORDER: {
    NOT_FOUND: "Handover work order not found.",
    FOUND: "Handover work order fetched successfully.",
    ALREADY_EXIST: "Handover Work Order exist for this order.",
    INVALID_ZOHO_STAGE:
      "For handover work order creation zoho stage can not be warranty.",
    SCOPE_NOT_COMPLETED:
      "Can not create handover work order as all the scope for this orders work order is not completed",
    ADD: {
      SUCCESS: "Handover Work Order created successfully.",
      ERROR: `Something went wrong while creating handover work order. ${TRY_AGAIN}`,
    },
    INVALID_QUESTION_IDS: "Please provide valid question ids.",
    SUBMIT_ANSWER_SUCCESS: "Answer submitted successfully.",
    INVALID_WO_STATUS:
      "Work order must be in inprogress for submitting answer.",
    CHECK_IN: {
      FAILED:
        "Work order can not be checked in as it is not in pending or in progress status.",
      SUCCESS: "Work order checked in successfully.",
    },
    CHECK_OUT: {
      SUCCESS: "Work order checked out successfully.",
      CHECK_IN_FIRST: "Work order is not checked in.",
    },
    SEND_OTP: {
      INVALID_STATUS: "Can not send otp as work order is not in inprogress.",
      SUCCESS: "OTP sent successfully.",
      ERROR: `Something went wrong while sending OTP. ${TRY_AGAIN}`,
    },
    OTP: {
      INVALID: "Otp doesn't match.",
    },
    UPLOAD_SIGNATURE: {
      SUCCESS: "Signature uploaded successfully.",
      EMPTY: "Please upload signature to check in the work order.",
    },
    UPDATE: {
      SUCCESS: "Handover work order updated successfully.",
      ERROR: `Something went wrong while updating handover work order. ${TRY_AGAIN}`,
    },
    LINK: {
      ERROR: "Something went wrong while generating handover work order link.",
    },
    INVALID_ZOHO_STAGE_FOR_COMPLAINT: "Invalid zoho stage.",
  },
};

module.exports = {
  MESSAGE,
};
