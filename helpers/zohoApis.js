const { Op } = require("sequelize");
const axios = require("axios");
const moment = require("moment-timezone");
const { isEmpty } = require("lodash");
const { getTimeDiff, capitalizeFirstLetter } = require("./functions");
const db = require("../database/models");
const { CONSTANTS } = require("./constants");
const { logger } = require("./logger");
const common = require("../controller/commonFunctions.js");
const { readJsonFile } = require("./awsS3.js");

const generateAuthenticationToken = async () => {
  try {
    const url = `${process.env.ZOHO_AUTH_URL}?refresh_token=${process.env.ZOHO_REFRESH_TOKEN}&client_id=${process.env.ZOHO_CLIENT_ID}&client_secret=${process.env.ZOHO_CLIENT_SECRET}&grant_type=${process.env.ZOHO_GRANT_TYPE}`;

    let respData;
    await axios
      .post(url)
      .then(async (apiResponse) => {
        if (apiResponse && apiResponse?.data) {
          respData = apiResponse?.data;
        }
      })
      .catch((error) => {
        logger.error({ message: `ZOHO_API_ERROR: ${error?.message || error}` });
        return null;
      });

    return respData || null;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return null;
  }
};

const fetchOrderById = async (orderId, insertedOrderId, authToken) => {
  try {
    const fields = "id,Product_Subform";

    const restrictedProductName = await readJsonFile(
      CONSTANTS.RESRTICTED_PRODUCT_FILE_NAME
    );
    const url = `${process.env.ZOHO_API_URL}v5/Deals/${orderId}?fields=${fields}`;

    const config = {
      method: "GET",
      url: url,
      headers: {
        Authorization: `Zoho-oauthtoken ${authToken}`,
      },
    };

    await axios(config)
      .then(async (apiResponse) => {
        if (
          apiResponse?.data?.data?.length &&
          apiResponse?.data?.data[0]?.Product_Subform?.length
        ) {
          const subFormData = apiResponse?.data?.data?.[0].Product_Subform;
          const nullItems = ["", " ", "NA", "N/A", "undefined", undefined];
          const filterData = (data) => (nullItems.includes(data) ? null : data);

          const isPart = (data) =>
            JSON.parse(restrictedProductName)?.includes(data) ? true : false;

          if (subFormData.length) {
            let keepLineItemIds = [];
            for (let j = 0; j < subFormData.length; j++) {
              // check if line item data exist or not.
              const data = {
                master_order_id: insertedOrderId,
                zoho_quote_line_item_id: filterData(subFormData[j].id),
                room: filterData(subFormData[j].Room),
                product_name: filterData(subFormData[j].Products?.name),
                description: filterData(subFormData[j].Description),
                quantity: filterData(subFormData[j].Qty),
                specification: filterData(subFormData[j].Specification),
                name: filterData(subFormData[j].Quote_Line_Items?.name),
                height: filterData(subFormData[j].Height),
                width: filterData(subFormData[j].Width),
                reference: filterData(subFormData[j].Reference),
                line_item: filterData(subFormData[j].Line_item),
                created_time: filterData(subFormData[j].Created_Time),
                is_part: isPart(subFormData[j].Products?.name),
              };

              const lineItemExist = await common.isLineItemExist(data);

              if (lineItemExist) {
                keepLineItemIds.push(lineItemExist.id);
              } else {
                const addLineItems = await db.MasterOrderQuoteLineItems.create(
                  data,
                  { logging: false }
                );
                keepLineItemIds.push(addLineItems.id);
              }
            }

            /*
                        // delete line items which are previously available but after update it is not came.
                        const lineItems = await db.MasterOrderQuoteLineItems.findAll({
                            where: {
                                master_order_id: insertedOrderId,
                                is_active: 1,
                                is_deleted: 0,
                            },
                            attributes: ATTRIBUTES.MASTER_ORDER_QUOTE_LINE_ITEMS,
                            logging: false
                        });

                        const lineItemIds = await lineItems.map((li) => li.id);
                        const deleteLineItemIds = await lineItemIds.filter(element => !keepLineItemIds.includes(element));

                        if (deleteLineItemIds.length) {
                            await db.MasterOrderQuoteLineItems.update(
                                { is_deleted: 1 },
                                {
                                    where: { id: { [Op.in]: deleteLineItemIds } },
                                    logging: false
                                }
                            );
                        }
                        */

            await db.MasterOrderQuoteLineItems.update(
              { is_deleted: 1 },
              {
                where: {
                  id: { [Op.notIn]: keepLineItemIds },
                  master_order_id: insertedOrderId,
                },
                logging: false,
              }
            );
          }
        }
      })
      .catch((error) => {
        console.error("ZOHO_API_ERROR:", error?.message);
      });

    return true;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const fetchAllOrders = async (
  modified_since_date = null,
  next_page_token = null
) => {
  try {
    const token = await getZohoAuthToken();

    const fields =
      "id,Deal_Name,Contact_Name,Email,Mobile,Phone,Gender,Street_Name,City,Zip_Code,State_Name,Region,Type_of_House,Owner,Created_Time,Modified_Time,Stage,Description,Committed_date_of_Delivery,Date_Of_Installation_Complete,Warranty_ID,Expected_Date_of_Survey,Grand_Total,Amount,QC_Mode";

    let url = `${process.env.ZOHO_API_URL}v5/Deals?fields=${fields}`;

    if (next_page_token) {
      url += `&page_token=${next_page_token}`;
    }

    let header = { Authorization: `Zoho-oauthtoken ${token}` };
    if (modified_since_date) {
      header = { ...header, "If-Modified-Since": modified_since_date };
    }

    const config = {
      method: "GET",
      url: url,
      headers: header,
    };

    let returnData = {};
    await axios(config)
      .then(async (apiResponse) => {
        if (
          apiResponse &&
          apiResponse?.data?.data &&
          apiResponse?.data?.data?.length
        ) {
          // console.log("PAGE: ", apiResponse.data.info.page);
          if (apiResponse.data.info.page <= 1) {
            console.log("ZOHO_ORDERS_FETCHING_STARTED : ", new Date());
          }

          const nullItems = ["", " ", "NA", "N/A", "undefined", undefined];
          const filterData = (data) => (nullItems.includes(data) ? null : data);

          for (let i = 0; i < apiResponse?.data?.data?.length; i++) {
            const result = apiResponse?.data?.data?.[i];

            const [masterOrder, created] = await db.MasterOrders.upsert(
              {
                zoho_order_id: result?.id,
                deal_name: filterData(result?.Deal_Name),
                stage: filterData(result?.Stage),
                customer_id: filterData(result?.Contact_Name?.id),
                customer_name:
                  filterData(result?.Contact_Name?.name) ||
                  filterData(result?.Deal_Name),
                email: filterData(result?.Email),
                mobile: filterData(result?.Mobile),
                phone: filterData(result?.Phone),
                gender: filterData(result?.Gender),
                street_name: filterData(result?.Street_Name),
                city: filterData(result?.City),
                zip_code: filterData(result?.Zip_Code),
                state_name: filterData(result?.State_Name),
                region: filterData(result?.Region),
                type_of_house: filterData(result?.Type_of_House),
                owner_name: filterData(result?.Owner?.name),
                owner_id: filterData(result?.Owner?.id),
                owner_email: filterData(result?.Owner?.email),
                committed_date_of_delivery: filterData(
                  result?.Committed_date_of_Delivery
                ),
                date_of_installation_complete: filterData(
                  result?.Date_Of_Installation_Complete
                ),
                warranty_id: filterData(result?.Warranty_ID),
                expected_date_of_survey: filterData(
                  result?.Expected_Date_of_Survey
                ),
                created_time: filterData(result?.Created_Time),
                modified_time: filterData(result?.Modified_Time),
                is_read: 0,
                amount: filterData(result?.Grand_Total || result?.Amount),
                qc_mode: filterData(result?.QC_Mode),
              },
              {
                where: { zoho_order_id: result?.id },
                // onDuplicate: [], // speecify fields to update if needed
                returning: ["id"],
                logging: false,
              }
            );

            if (masterOrder?.id) {
              await fetchOrderById(result?.id, masterOrder?.id, token);
            }
          }

          if (apiResponse?.data?.info?.next_page_token) {
            await fetchAllOrders(
              header["If-Modified-Since"] || null,
              apiResponse.data.info.next_page_token
            );
          } else {
            console.log("ZOHO_ORDERS_FETCHING_COMPLETED : ", new Date());
            returnData = {
              success: true,
              message: "Order fetched successfully.",
            };
          }
        } else {
          console.log(
            "--- NO NEW/UPDATED ORDERS AVAILABLE TO FETCH ---",
            new Date()
          );
          returnData = {
            success: false,
            message: "No New or Updated orders available in Zoho.",
          };
        }
      })
      .catch((error) => {
        console.error("ZOHO_API_ERROR:", error?.message);
        returnData = {
          success: false,
          message:
            error?.message ||
            "Something went wrong while fetching orders from zoho. Please try again later.",
        };
      });

    return returnData;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return {
      success: false,
      message: error.message,
    };
  }
};

const getZohoAuthToken = async () => {
  try {
    const tokenData = await db.Tokens.findOne({
      where: { is_active: 1, is_deleted: 0 },
      order: [["id", "DESC"]],
      limit: 1,
      raw: true,
      logging: false,
    });

    if (!isEmpty(tokenData)) {
      let token = tokenData?.token;
      const timeDiff = await getTimeDiff(new Date(), tokenData.createdAt);

      if (timeDiff.minutes > CONSTANTS.ZOHO.AUTH_TOKEN_REGENERATE_DURATION) {
        const generateToken = await generateAuthenticationToken();
        if (generateToken) {
          console.log("NEW_TOKEN_GENERATED: ", new Date());
          await addToken(generateToken?.access_token);
          token = generateToken?.access_token;
        }
      }
      return token;
    } else {
      const generateToken = await generateAuthenticationToken();
      if (generateToken) {
        console.log("NEW_TOKEN_GENERATED: ", new Date());
        await addToken(generateToken?.access_token);
        return generateToken?.access_token;
      }
      return null;
    }
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return null;
  }
};

const addToken = async (token) => {
  if (token) {
    const createToken = await db.Tokens.create(
      { token: token },
      { logging: false }
    );

    if (createToken) {
      // await db.Tokens.update(
      //     { is_active: 0, is_deleted: 1 },
      //     { where: { id: { [Op.ne]: createToken.id } } }
      // );
      await db.Tokens.destroy({ where: { id: { [Op.ne]: createToken.id } } });
    }
  }
};

const getNewAndUpdatedOrders = async () => {
  try {
    /*
            to get latest data from zoho, we need to pass the datetime in with If-Modified-Since header. And we need to pass datetime in below format.
            FORMAT: yyyy-mm-ddThh:mm:ss+5:30
                - date time should be in local time (IST)
                - +5:30 denotes the difference of UTC.
        */

    // get latest modified date
    const orderData = await db.MasterOrders.findOne({
      limit: 1,
      order: [["modified_time", "DESC"]],
    });

    const gmtDateObj = moment.utc(
      orderData.modified_time,
      "YYYY-MM-DD HH:mm:ss"
    );
    const istDateTimeString = gmtDateObj
      .tz("Asia/Kolkata")
      .format("YYYY-MM-DDTHH:mm:ss");
    const modifiedSinceDate = `${istDateTimeString}+05:30`;

    const fetchOrders = await fetchAllOrders(modifiedSinceDate);
    return fetchOrders;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return {
      success: false,
      message: error.message,
    };
  }
};

const sendWorkOrderToZoho = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const {
      zoho_order_id,
      response,
      scopes,
      spaces,
      start_date,
      work_order_status,
      work_order_type,
      actual_work_order_id,
      zoho_fabricator_id,
    } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v5/Work_Orders`;

    const params = [
      {
        Opportunity_Name: zoho_order_id, //zoho order id
        // Response: response, // all task's question & answer
        // Response_PDF: "",
        Scope: scopes, // comma seprated all scopes
        Space: spaces, // comma seprated all spaces
        Start_Date: start_date,
        Status: work_order_status, // wo status
        Type: work_order_type, // wo type
        Name: actual_work_order_id, // wo id
        Owner: {
          id: zoho_fabricator_id,
        },
        Server_Stage: await capitalizeFirstLetter(process.env.NODE_ENV),
      },
    ];

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    let inserted_wo_id;
    await axios
      .post(url, { data: params }, headers)
      .then(async (apiResponse) => {
        const result = apiResponse?.data?.data?.[0].details;
        if (result?.id) {
          inserted_wo_id = result?.id;
          await db.WorkOrders.update(
            { inserted_zoho_wo_id: inserted_wo_id },
            { where: { actual_work_order_id } }
          );
        }
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${error?.message}`,
          stack: error.stack,
        });
        return false;
      });

    return inserted_wo_id;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const sendWorkOrderPdfToZoho = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_work_order_id, attachment_url, work_order_attachment_id } =
      data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v5/Work_Orders/${zoho_work_order_id}/Attachments`;

    let formData = new FormData();
    formData.append("attachmentUrl", attachment_url);

    const headers = {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    let inserted_wo_attachment_id;
    await axios
      .post(url, formData, headers)
      .then(async (apiResponse) => {
        if (apiResponse?.data?.data?.[0]?.code === "SUCCESS") {
          const result = apiResponse?.data?.data?.[0].details;
          if (result?.id) {
            inserted_wo_attachment_id = result?.id;
            await db.WorkOrderAttachments.update(
              { inserted_zoho_wo_attachment_id: inserted_wo_attachment_id },
              { where: { id: work_order_attachment_id } }
            );
          }
        }
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${error?.message}`,
          stack: error.stack,
        });
        return false;
      });

    return inserted_wo_attachment_id;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const fetchZohoWorkOrderDetails = async (zoho_work_order_id) => {
  try {
    const authToken = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v5/Work_Orders/${zoho_work_order_id}`;

    const config = {
      method: "GET",
      url: url,
      headers: {
        Authorization: `Zoho-oauthtoken ${authToken}`,
      },
    };

    let woData;
    await axios(config)
      .then(async (apiResponse) => {
        if (apiResponse?.data?.data?.length) {
          woData = apiResponse.data.data[0];
        }
      })
      .catch((error) => {
        console.error("ZOHO_API_ERROR:", error?.message);
        return null;
      });

    return woData;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return null;
  }
};

const sendHandOverWorkOrderToZoho = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, email, id } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v8/Handover_Data`;

    const params = [
      {
        OpportunityNo: zoho_order_id, //zoho order id
        Email: email,
        Handover_ID: id,
      },
    ];

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    await axios
      .post(url, { data: params }, headers)
      .then(async (apiResponse) => {
        const result = apiResponse?.data?.data?.[0].details;
        await db.HandoverWorkOrders.update(
          { zoho_handover_id: result?.id },
          { where: { actual_work_order_id: id } }
        );
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${error?.message}`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const updateQcModeInZoho = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, mode = CONSTANTS.ZOHO.QC_MODE.APP } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v8/Deals/${zoho_order_id}`;

    const params = [
      {
        QC_Mode: mode,
      },
    ];

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    await axios
      .put(url, { data: params }, headers)
      .then(async (apiResponse) => {
        const result = apiResponse?.data?.data?.[0].details;
        console.log("result: ", result);
        await db.MasterOrders.update(
          { qc_mode: mode },
          { where: { zoho_order_id } }
        );
        await db.Orders.update({ qc_mode: mode }, { where: { zoho_order_id } });
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${error?.message}`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const checkAndUpdateZohoStage = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id } = data;
    const token = await getZohoAuthToken();
    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    //get current zoho satge
    const currentStageUrl = `${process.env.ZOHO_API_URL}v8/Deals/${zoho_order_id}?fields=Stage`;
    const stageData = await axios.get(currentStageUrl, headers);
    const currentStage = stageData?.data?.data?.[0]?.Stage;

    if (currentStage === CONSTANTS.ZOHO.ORDER_STAGE.HANDOVER.TITLE) {
      return true;
    }

    switch (currentStage) {
      case CONSTANTS.ZOHO.ORDER_STAGE.QC_IN_PROGRESS.TITLE:
        const date = new Date().toISOString().split("T")[0];
        const payload = {
          blueprint: [
            {
              transition_id: CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.ID,
              data: {
                CheckLists: CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.CHECKLIST,
                Actual_Date_of_QC: date,
              },
            },
          ],
        };
        await updateZohoStage({ zoho_order_id, token, payload });
        break;

      case CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.TITLE:
        await updateZohoStageToHandover({ zoho_order_id, token });
        break;

      default:
        break;
    }

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const updateZohoStage = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, token, payload } = data;
    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    //we need to get current transition stage
    const url = `${process.env.ZOHO_API_URL}v8/Deals/${zoho_order_id}/actions/blueprint`;

    await axios
      .put(url, payload, headers)
      .then(async (apiResponse) => {
        await db.Orders.update(
          { zoho_stage: CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.TITLE },
          { where: { zoho_order_id } }
        );
        payload?.blueprint?.[0]?.transition_id ==
          CONSTANTS.ZOHO.ORDER_STAGE.QC_APPROVAL.ID &&
          (await updateZohoStageToHandover({ zoho_order_id, token }));

        payload?.blueprint?.[0]?.transition_id ==
          CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.ID &&
          (await db.Orders.update(
            { zoho_stage: CONSTANTS.ZOHO.ORDER_STAGE.WARRANTY.TITLE },
            { where: { zoho_order_id } }
          ));
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${
            error?.response?.data?.message || error?.message
          }`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const updateZohoStageToHandover = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, token } = data;
    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    //we need to get current transition stage
    const url = `${process.env.ZOHO_API_URL}v6/Deals/${zoho_order_id}`;
    const params = [
      {
        Stage: CONSTANTS.ZOHO.ORDER_STAGE.HANDOVER.TITLE,
      },
    ];

    await axios
      .put(url, { data: params }, headers)
      .then(async (apiResponse) => {
        await db.Orders.update(
          { zoho_stage: CONSTANTS.ZOHO.ORDER_STAGE.HANDOVER.TITLE },
          { where: { zoho_order_id } }
        );
      })
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${
            error?.response?.data?.message || error?.message
          }`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const updateHandoverQuestionAnswer = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_handover_id, answerPayload } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v8/Handover_Data/${zoho_handover_id}`;

    const params = [
      {
        ...answerPayload,
      },
    ];

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    await axios
      .put(url, { data: params }, headers)
      .then(async (apiResponse) => {})
      .catch((error) => {
        logger.error({
          message: `ZOHO_API_ERROR: ${
            error?.response?.data?.message || error?.message
          }`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const raiseTicket = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { description, subject, customer_data } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_DESK_URL}/v1/tickets`;

    const params = {
      departmentId: CONSTANTS.ZOHO_TICKET.DEPARTMENT_ID,
      ...CONSTANTS.ZOHO_TICKET.CF,
      subject,
      description,
      contact: {
        email: customer_data?.email,
        lastName: customer_data?.name,
        firstName: customer_data?.name,
        phone: customer_data?.mobile,
      },
    };

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    const apiResponse = await axios.post(url, params, headers);
    console.log("complaint added successfully");
    return apiResponse?.data;
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const addTagInOpportunity = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, tag_name } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v6/Deals/actions/add_tags`;

    const params = {
      tags: [
        {
          name: tag_name,
        },
      ],
      ids: [zoho_order_id],
    };

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    await axios
      .post(url, params, headers)
      .then(async (apiResponse) => {
        console.log("Tag added successfully");
      })
      .catch((error) => {
        console.log("error: ", error);
        logger.error({
          message: `ZOHO_API_ERROR: ${
            error?.response?.data?.message || error?.message
          }`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

const removeTagInOpportunity = async (data) => {
  try {
    if (isEmpty(data)) {
      return false;
    }

    const { zoho_order_id, tag_name } = data;
    const token = await getZohoAuthToken();
    const url = `${process.env.ZOHO_API_URL}v6/Deals/actions/remove_tags`;

    const params = {
      tags: [
        {
          name: tag_name,
        },
      ],
      ids: [zoho_order_id],
    };

    const headers = {
      headers: {
        Authorization: `Zoho-oauthtoken ${token}`,
      },
    };

    await axios
      .post(url, params, headers)
      .then(async (apiResponse) => {
        console.log("Tag removed successfully");
      })
      .catch((error) => {
        console.log("error: ", error);
        logger.error({
          message: `ZOHO_API_ERROR: ${
            error?.response?.data?.message || error?.message
          }`,
          stack: error.stack,
        });
        return false;
      });

    return "";
  } catch (error) {
    logger.error({
      message: `ZOHO_API_ERROR: ${
        error?.response?.data?.message || error.message
      }`,
      stack: error.stack,
    });
    return false;
  }
};

module.exports = {
  generateAuthenticationToken,
  fetchOrderById,
  fetchAllOrders,
  getZohoAuthToken,
  getNewAndUpdatedOrders,
  sendWorkOrderToZoho,
  sendWorkOrderPdfToZoho,
  fetchZohoWorkOrderDetails,
  sendHandOverWorkOrderToZoho,
  updateQcModeInZoho,
  updateZohoStageToHandover,
  checkAndUpdateZohoStage,
  updateHandoverQuestionAnswer,
  updateZohoStage,
  raiseTicket,
  addTagInOpportunity,
  removeTagInOpportunity,
};
