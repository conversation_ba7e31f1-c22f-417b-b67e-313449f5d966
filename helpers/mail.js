require("dotenv").config();
const nodemailer = require("nodemailer");
const { logger } = require("./logger");

const sendMail = async (recieverEmail, subject, mailContent, attachment = null, ccMails = null, bccMails = null) => {
    try {
        const transpoter = nodemailer.createTransport({
            service: process.env.MAIL_SERVICE,
            auth: {
                user: process.env.MAIL_FROM,
                pass: process.env.MAIL_PASSWORD,
            },
        });

        let mailOptions = {
            from: `${process.env.APP_NAME} <${process.env.MAIL_FROM}>`,
            to: recieverEmail,
            subject: subject,
            html: mailContent,
        }

        if (attachment) {
            mailOptions.attachments = [{
                filename: attachment.name,
                path: attachment.url,
                href: attachment.url,
            }];
        }

        if (ccMails) {
            mailOptions.cc = ccMails;
        }

        if (bccMails) {
            mailOptions.bcc = bccMails;
        }

        await transpoter.sendMail(mailOptions);

        return {
            success: true,
            message: "Mail sent successfully."
        }
    }
    catch (error) {
        logger.error({ message: `SEND_MAIL_ERROR: ${error.message}`, stack: error.stack });
        return {
            success: false,
            message: error.message
        }
    }
}

module.exports = { sendMail };
