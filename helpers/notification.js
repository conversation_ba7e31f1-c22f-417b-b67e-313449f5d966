const firebase = require("firebase-admin");
const { logger } = require("./logger");

const notification_config = {
    "type": process.env.FIREBASE_TYPE,
    "project_id": process.env.FIREBASE_PROJECT_ID,
    "private_key_id": process.env.FIREBASE_PRIVATE_KEY_ID,
    "private_key": process.env.FIREBASE_PRIVATE_KEY,
    "client_email": process.env.FIREBASE_CLIENT_EMAIL,
    "client_id": process.env.FIREBASE_CLIENT_ID,
    "auth_uri": process.env.FIREBASE_AUTH_URI,
    "token_uri": process.env.FIREBASE_TOKEN_URI,
    "auth_provider_x509_cert_url": process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
    "client_x509_cert_url": process.env.FIREBASE_CLIENT_X509_CERT_URL,
    "universe_domain": process.env.FIREBASE_UNIVERSE_DOMAIN
}

firebase.initializeApp({ credential: firebase.credential.cert(notification_config) });

const sendNotificationBKP = async (deviceTokens, title, body, content) => {
    try {
        if (deviceTokens && title && body) {
            const payload = {
                data: {
                    title: title || "",
                    body: body || "",
                    content: content || "",
                    click_action: "FLUTTER_NOTIFICATION_CLICK",
                },
                // token: await deviceTokens
                tokens: await deviceTokens
            }

            // await firebase.messaging().send(payload);
            const notificationResponse = await firebase.messaging().sendEachForMulticast(payload);

            let errMsg = [];
            if (notificationResponse?.failureCount > 0 && notificationResponse?.successCount < 1) {
                if (notificationResponse?.responses?.length) {
                    await notificationResponse?.responses.map((err) => errMsg.push(err.error.message))
                }
                logger.error({ message: `SEND_NOTIFICATION_ERROR: ${error.message}` });
                return false;
            }
            else if (notificationResponse?.failureCount < 1 && notificationResponse?.successCount > 0) {
                return true;
            }
            else {
                return false;
            }
        }
        return false;
    }
    catch (error) {
        logger.error({ message: `SEND_NOTIFICATION_ERROR : ${error.message}`, stack: error.stack });
        return false;
    }
}

const sendNotification = async (deviceTokens, title, body, content) => {
    try {
        if (!deviceTokens || !title || !body) {
            return [];
        }

        const results = await Promise.all(deviceTokens.map(async (token) => {
            const payload = {
                data: {
                    title: title || "",
                    body: body || "",
                    content: content || "",
                    click_action: "FLUTTER_NOTIFICATION_CLICK",
                },
                tokens: [token]
            }

            try {
                const notificationResponse = await firebase.messaging().sendEachForMulticast(payload);

                if (notificationResponse?.failureCount > 0) {
                    const errMsg = notificationResponse.responses.map((err) => err.error.message);
                    logger.error({ message: `SEND_NOTIFICATION_ERROR for token: ${token}: ${errMsg}` });
                    return { token, success: false, message: errMsg };
                }
                else {
                    return { token, success: true, message: "Notification sent successfully." };
                }
            }
            catch (error) {
                logger.error({ message: `SEND_NOTIFICATION_ERROR for token: ${token}: ${error.message}`, stack: error.stack });
                return { token, success: false, message: error.message };
            }
        }));

        return results;
    }
    catch (error) {
        logger.error({ message: `SEND_NOTIFICATION_ERROR : ${error.message}`, stack: error.stack });
        return [];
    }
}

module.exports = {
    sendNotification,
}
