const ORDER_STATUS = ["id", "title", "is_active", "is_deleted"];

const ORDERS = [
  "id",
  "actual_order_id",
  "zoho_order_id",
  "zoho_stage",
  "title",
  "zoho_fabricator_id",
  "fabricator_id",
  "fabricator_email",
  "customer_id",
  "order_status_id",
  "zoho_warranty_id",
  "profile_warranty_end_date",
  "hardware_warranty_end_date",
  "expected_date_of_survey",
  "createdAt",
  "amount",
  "qc_mode",
];

const ORDER_SPACES = ["id", "order_id", "space_id"];

const ORDER_SCOPES = [
  "id",
  "order_id",
  "order_space_mapping_id",
  "scope_id",
  "count",
];

const CUSTOMERS = [
  "id",
  "actual_customer_id",
  "zoho_customer_id",
  "name",
  "email",
  "mobile",
  "phone",
  "gender",
  "address",
  "zone",
  "location",
  "universe",
  "zip_code",
];

const SPACES = [
  "id",
  "title",
  "source",
  "is_active",
  "is_deleted",
  "createdAt",
];

const SCOPES = [
  "id",
  "model",
  "name",
  "source",
  "height",
  "width",
  "specification",
  "reference",
  "is_active",
  "is_deleted",
  "is_part",
  "new_name",
  "line_item",
];

const SCOPE_IMAGES = ["id", "scope_id", "image_key", "image_url", "base64"];

const SCOPE_IMAGE_TOUCH_POINTS = [
  "scope_image_id",
  "touch_point",
  "x_coordinate",
  "y_coordinate",
  "title",
];

const WORK_ORDER_TYPE_SKILLS = ["work_order_type_id", "skill_id"];

const WORK_ORDER_TYPES = ["id", "title", "is_active", "is_deleted"];

const WORK_ORDER_STATUS = ["id", "title", "is_active", "is_deleted"];

const WORK_ORDERS = [
  "id",
  "inserted_zoho_wo_id",
  "actual_work_order_id",
  "order_id",
  "customer_id",
  "work_order_status_id",
  "work_order_type_id",
  "assigned_to_user_id",
  "assigned_to_user_role_id",
  "assigned_by_user_id",
  "assigned_by_user_role_id",
  "scheduled_start_date",
  "scheduled_start_time",
  "scheduled_duration",
  "scheduled_due_datetime",
  "actual_start_date",
  "actual_start_time",
  "actual_duration",
  "actual_due_datetime",
  "actual_end_datetime",
  "instructions",
  "is_cus_otp_verified",
  "selfie_image_key",
  "selfie_image_url",
  "selfie_image_base64",
  "rejected_count",
  "zoho_status",
  "customer_feedback",
  "internal_team_id",
  "internal_team_approval_status",
  "is_active",
  "is_deleted",
  "createdAt",
  "updatedAt",
];

const WORK_ORDER_SPACES = ["id", "work_order_id", "space_id"];

const WORK_ORDER_SCOPES = [
  "work_order_id",
  "work_order_space_mapping_id",
  "scope_id",
  "count",
  "task_count",
];

const QUESTION_TYPES = ["id", "title", "is_active", "is_deleted"];

const QUESTIONS = [
  "id",
  "title",
  "question_type_id",
  "report_tag_id",
  "attachment_required",
  "is_attachment_required",
  "is_numeric",
  "is_video_required",
  "video_required",
  "is_active",
  "is_deleted",
  "comment_required",
];

const HANDOVER_QUESTIONS = [
  "id",
  "title",
  "question_type",
  "attachment_required",
  "is_active",
  "min_number",
  "max_number",
  "comment_required",
  "is_checked",
  "is_mandatory",
];
const HANDOVER_QUESTION_OPTIONS = ["id", "question_id", "title"];

const QUESTION_OPTIONS = [
  "id",
  "question_id",
  "title",
  "is_active",
  "is_deleted",
];

const QUESTION_MAPPING = [
  "id",
  "work_order_type_id",
  "scope_id",
  "is_active",
  "is_deleted",
];

const QUESTION_MAPPING_TOUCH_POINTS = [
  "id",
  "question_mapping_id",
  "touch_point",
];

const QUESTION_MAPPING_SET = [
  "question_mapping_touch_point_id",
  "question_id",
  "createdAt",
];

const TASKS = [
  "id",
  "work_order_id",
  "work_order_scope_mapping_id",
  "question_mapping_id",
  "actual_task_id",
  "status",
  "is_submitted",
  "additional_info",
  "updatedAt",
  "reason",
  "internal_team_id",
];

const TASK_TOUCH_POINTS = ["id", "task_id", "touch_point", "reason", "status"];

const TASK_QUESTIONS = [
  "id",
  "task_touch_point_id",
  "question_id",
  "reason",
  "status",
  "comment",
];

const TASK_ANSWERS = ["task_question_id", "answer"];

const TASK_IMAGE_ANSWERS = [
  "id",
  "task_question_id",
  "image_key",
  "image_url",
  "base64",
];

const TASK_VIDEO_ANSWERS = [
  "id",
  "task_question_id",
  "signed_url",
  "signed_url_exipration_time",
  "video_key",
  "video_url",
  "is_video_uploaded",
  "is_active",
  "is_deleted",
];

const OTPS = [
  "id",
  "work_order_id",
  "customer_id",
  "country_code",
  "mobile",
  "otp",
  "is_otp_verified",
  "action",
  "otp_send_to",
];

const WORK_ORDER_REVIEW = [
  "id",
  "work_order_id",
  "reviewer_user_id",
  "requester_user_id",
  "review_status",
  "reason",
];

const MASTER_ORDERS = [
  "id",
  "zoho_order_id",
  "deal_name",
  "stage",
  "customer_id",
  "customer_name",
  "email",
  "mobile",
  "phone",
  "gender",
  "street_name",
  "city",
  "zip_code",
  "state_name",
  "region",
  "type_of_house",
  "owner_name",
  "owner_id",
  "owner_email",
  "committed_date_of_delivery",
  "date_of_installation_complete",
  "warranty_id",
  "expected_date_of_survey",
  "created_time",
  "modified_time",
  "is_read",
];

const MASTER_ORDER_QUOTE_LINE_ITEMS = [
  "id",
  "master_order_id",
  "zoho_quote_line_item_id",
  "room",
  "product_name",
  "description",
  "quantity",
  "specification",
  "name",
  "height",
  "width",
  "reference",
  "created_time",
  "is_active",
  "is_deleted",
];

const TOKENS = ["token", "type", "is_active", "is_deleted"];

const POINT_RATES = [
  "id",
  "points",
  "rate",
  "currency",
  "added_by",
  "is_active",
  "is_deleted",
  "createdAt",
];

const WORK_ORDER_TYPE_POINTS = [
  "id",
  "work_order_type_id",
  "points_to_assign",
  "transaction_type",
  "added_by",
  "is_active",
  "is_deleted",
  "createdAt",
];

const POINT_HISTORY = [
  "user_id",
  "work_order_id",
  "work_order_type_point_id",
  "point_rate_id",
  "is_read",
  "createdAt",
];

const POINT_BALANCE = [
  "user_id",
  "total_points",
  "total_earnings",
  "redeemed_points",
  "redeemed_earnings",
];

const WARRANTY = [
  "id",
  "actual_warranty_id",
  "work_order_id",
  "customer_id",
  "file_key",
  "file_url",
];

const REDEEMED_EARNING_HISTORY = [
  "user_id",
  "redeem_amount",
  "note",
  "point_rate_id",
  "is_read",
  "createdAt",
];

const WORK_ORDER_ATTACHMENT = [
  "id",
  "inserted_zoho_wo_attachment_id",
  "work_order_id",
  "work_order_type_id",
  "file_key",
  "file_url",
  "action",
  "type",
  "is_active",
  "is_deleted",
  "createdAt",
  "updatedAt",
];

const REPORT_TAGS = [
  "id",
  "custom_id",
  "name",
  "is_mandatory",
  "number",
  "is_active",
];

const WORK_ORDER_REVIEWER = [
  "id",
  "work_order_id",
  "reviewer_user_id",
  "status",
  "createdAt",
  "updatedAt",
];

const ORDER_IMAGES = [
  "id",
  "order_id",
  "image_key",
  "image_url",
  "base64",
  "is_active",
  "is_deleted",
  "createdAt",
  "updatedAt",
];

const HANDOVER_WORK_ORDERS = [
  "id",
  "order_id",
  "customer_id",
  "check_in_user_id",
  "actual_work_order_id",
  "zoho_handover_id",
  "work_order_status",
  "assigned_to_user_id",
  "assigned_to_user_role_id",
  "assigned_by_user_id",
  "assigned_by_user_role_id",
  "scheduled_start_date",
  "scheduled_start_time",
  "actual_start_date",
  "actual_start_time",
  "actual_end_datetime",
  "is_active",
  "is_deleted",
  "createdAt",
  "updatedAt",
];

const HANDOVER_WORK_ORDER_ANSWERS = [
  "id",
  "question_id",
  "workorder_id",
  "answer",
  "is_active",
  "is_deleted",
  "comment",
  "createdAt",
  "updatedAt",
];

const HANDOVER_IAMGE_ANSWERS = [
  "id",
  "question_id",
  "work_order_id",
  "image_key",
  "image_url",
  "is_active",
  "is_deleted",
  "createdAt",
  "updatedAt",
];

module.exports = {
  ATTRIBUTES: {
    ORDER_STATUS,
    ORDERS,
    ORDER_IMAGES,
    ORDER_SPACES,
    ORDER_SCOPES,
    CUSTOMERS,
    SPACES,
    SCOPES,
    SCOPE_IMAGES,
    SCOPE_IMAGE_TOUCH_POINTS,
    WORK_ORDER_TYPE_SKILLS,
    WORK_ORDER_TYPES,
    WORK_ORDER_STATUS,
    WORK_ORDERS,
    WORK_ORDER_SPACES,
    WORK_ORDER_SCOPES,
    QUESTION_TYPES,
    QUESTIONS,
    QUESTION_OPTIONS,
    QUESTION_MAPPING,
    QUESTION_MAPPING_TOUCH_POINTS,
    QUESTION_MAPPING_SET,
    TASKS,
    TASK_TOUCH_POINTS,
    TASK_QUESTIONS,
    TASK_ANSWERS,
    TASK_IMAGE_ANSWERS,
    TASK_VIDEO_ANSWERS,
    OTPS,
    WORK_ORDER_REVIEW,
    MASTER_ORDERS,
    MASTER_ORDER_QUOTE_LINE_ITEMS,
    TOKENS,
    POINT_RATES,
    WORK_ORDER_TYPE_POINTS,
    POINT_HISTORY,
    POINT_BALANCE,
    WARRANTY,
    REDEEMED_EARNING_HISTORY,
    WORK_ORDER_ATTACHMENT,
    REPORT_TAGS,
    WORK_ORDER_REVIEWER,
    HANDOVER_QUESTIONS,
    HANDOVER_QUESTION_OPTIONS,
    HANDOVER_WORK_ORDERS,
    HANDOVER_WORK_ORDER_ANSWERS,
    HANDOVER_IAMGE_ANSWERS,
  },
};
