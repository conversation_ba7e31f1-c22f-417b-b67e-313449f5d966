require("dotenv").config();
const axios = require("axios");

const { logger } = require("./logger");

const generatePDF = async (payload) => {
    try {
        const token = Buffer.from(`${process.env.JS_REPORT_USER_NAME}:${process.env.JS_REPORT_PASSWORD}`, "utf-8").toString("base64");

        const response = await axios({
            url: process.env.JS_REPORT_BASE_URL,
            responseType: 'arraybuffer',
            method: 'post',
            headers: {
                "Authorization": `Basic ${token}`,
                "Accept": "application/pdf",
            },
            data: payload
        });

        return { pdfBuffer: response.data }
    }
    catch (error) {
        logger.error({ message: `PDF_ERROR: ${error.message}`, stack: error.stack });
        return { error: error.message }
    }
}

module.exports = {
    generatePDF,
}
