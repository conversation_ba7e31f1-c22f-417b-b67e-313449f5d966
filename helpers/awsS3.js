const { promisify } = require("util");
require("dotenv").config();
const AWS = require("aws-sdk");
const path = require("path");
const { getCurrentDateTimeUTC } = require("./functions");
const { CONSTANTS } = require("./constants");
const { logger } = require("../helpers/logger.js");
const sharp = require("sharp");
const fs = require("fs");

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  region: process.env.AWS_S3_REGION,
});

const createFolderS3 = async (folderName) => {
  try {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: `${folderName}`,
    };

    await s3.putObject(params, (error, data) => {
      if (error) {
        logger.error({ message: `S3_FOLDER_CREATE_ERROR: ${error.message}` });
        return false;
      } else {
        return true;
      }
    });
  } catch (error) {
    logger.error({
      message: `S3_FOLDER_CREATE_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const isFolderExistS3 = async (folder) => {
  try {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Prefix: folder,
    };

    const data = await s3.listObjectsV2(params).promise();

    if (data.Contents.length > 0) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    logger.error({
      message: `S3_CHECK_FOLDER_EXIST_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const uploadFileS3 = async (
  key,
  blob,
  folder,
  metadata = {},
  mimetype = null
) => {
  try {
    const name = path.parse(key).name;
    const extension = path.parse(key).ext;
    const current_dateTime = await getCurrentDateTimeUTC(
      CONSTANTS.DATE_FORMATS.APPEND_TO_FILE
    );
    const fileName = `${name}_${current_dateTime}${extension}`;

    // compress image
    let buffer = blob;
    if (mimetype && mimetype.match(/^image/)) {
      buffer = await sharp(blob).jpeg({ quality: 60 }).toBuffer();
    }

    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: folder ? `${folder}/${fileName}` : fileName,
      Body: buffer,
      Metadata: metadata,
      ContentType: mimetype,
    };

    return new Promise((resolve, reject) => {
      s3.upload(params, (error, data) => {
        if (error) {
          logger.error({ message: `FILE_UPLOAD_ERROR: ${error}` });
          reject(error);
        } else {
          resolve(data);
        }
      });
    });
  } catch (error) {
    logger.error({
      message: `FILE_UPLOAD_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return error.message;
  }
};

const deleteFileS3 = async (fileKey) => {
  try {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: fileKey,
    };

    await s3.deleteObject(params, (error, data) => {
      if (error) {
        logger.error({ message: `DELETE_FILE_ERROR: ${error}` });
        return null;
      } else {
        return data;
      }
    });
  } catch (error) {
    logger.error({
      message: `DELETE_FILE_ERROR: ${error.message}`,
      stack: error.stack,
    });
    return false;
  }
};

const fetchS3Content = async (key) => {
  try {
    const getObject = promisify(s3.getObject.bind(s3));
    const response = await getObject({
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
    });
    return response.Body;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return false;
  }
};

const convertS3UrlToBase64 = async (key) => {
  try {
    const content = await fetchS3Content(key);
    const base64Data = await content.toString("base64");
    return base64Data;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getS3PutObjectSignedUrl = async (pathName, fileName) => {
  try {
    const Key = ["", "/"].includes(pathName)
      ? fileName
      : `${pathName}/${fileName}`;
    const Expires = CONSTANTS.S3_BUCKET.SIGNED_URL.EXIPRATION_TIME * 60;

    const s3Params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key,
      Expires, // time in seconds
    };
    const signedUrl = await s3.getSignedUrl("putObject", s3Params);
    return signedUrl;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const isObjectExistOnS3 = async (key) => {
  try {
    const params = { Bucket: process.env.AWS_S3_BUCKET, Key: key };

    const resp = await new Promise((resolve, reject) => {
      s3.headObject(params, (error, data) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      });
    });

    return resp;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return false;
  }
};

const getS3FileUrl = async (fileKey) => {
  try {
    return `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_S3_REGION}.amazonaws.com/${fileKey}`;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const readJsonFile = async (fileName) => {
  try {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: `${fileName}`,
    };
    const resp = await new Promise((resolve, reject) => {
      s3.getObject(params, (error, data) => {
        if (error) {
          reject(error);
        } else {
          const fileContent = data.Body.toString("utf-8");
          resolve(fileContent);
        }
      });
    });

    return resp;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

module.exports = {
  createFolderS3,
  isFolderExistS3,
  uploadFileS3,
  deleteFileS3,
  convertS3UrlToBase64,
  getS3PutObjectSignedUrl,
  isObjectExistOnS3,
  getS3FileUrl,
  readJsonFile,
};
