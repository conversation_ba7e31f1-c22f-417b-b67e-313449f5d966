const { CONSTANTS } = require("./constants.js");
const { HTTP_CODES } = require("./httpCodes");
const { logger } = require("./logger.js");

class ResponseHandler {
    constructor(req, res) {
        this.req = req;
        this.res = res;
    }

    sender(isSuccess, httpCode, message, data = {}, error) {
        if (error) {
            logger.error({ message: (error?.message || error), stack: error?.stack });
        }

        this.res
            .status(httpCode)
            .json({
                status: isSuccess,
                message: error ? (error?.message || error) : message,
                data: data,
            })

    }

    custom(...args) { this.sender(...args) }

    // 2XX SUCCESS
    success(message, data) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.SUCCESS,
            HTTP_CODES.SUCCESS,
            message || "SUCCESS",
            data
        )
    }

    created(message, data) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.SUCCESS,
            HTTP_CODES.CREATED,
            message || "CREATED",
            data
        )
    }

    // 4XX CLIENT ERROR
    badRequest(message) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.BAD_REQUEST,
            message || "BAD_REQUEST"
        )
    }

    unauthorized(message) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.UNAUTHORIZED,
            message || "UNAUTHORIZED",
            {}
        )
    }

    notFound(message) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.NOT_FOUND,
            message || "NOT_FOUND",
            {}
        )
    }

    conflict(message) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.CONFLICT,
            message || "CONFLICT",
            {}
        )
    }

    validationError(message) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.VALIDATION_ERROR,
            message || "VALIDATION_ERROR",
            {}
        )
    }

    // 5XX SERVER ERROR
    serverError(error) {
        this.sender(
            CONSTANTS.RESPONSE_STATUS.FAILED,
            HTTP_CODES.SERVER_ERROR,
            "SERVER_ERROR",
            {},
            error
        )
    }
}

module.exports = ResponseHandler;
