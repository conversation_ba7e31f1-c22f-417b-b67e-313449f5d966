const { logger } = require("./logger");

require("dotenv").config();
const twilio = require("twilio")(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

module.exports.sendSMS = async (receiverNumber, body) => {
  try {
    await twilio.messages.create({
      body: body,
      from: process.env.TWILIO_FROM_NUMBER,
      to: receiverNumber,
    });

    return {
      success: true,
      message: "SMS sent successfully.",
    };
  } catch (error) {
    logger.error({
      message: `SEND_SMS_ERROR: ${error.message}`,
      stack: error.stack,
    });

    return {
      success: false,
      message: `Failed to send SMS. Error: ${error.message}`,
    };
  }
};
