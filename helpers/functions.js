const moment = require("moment");
const crypto = require("crypto");

const { logger } = require("./logger");
const algorithm = "aes-256-cbc";
const secretKey = Buffer.from(process.env.SECRET_KEY, "utf-8");
const iv = crypto.randomBytes(16); // Initialization vector

const getResponse = (response, statusCode, isSuccess, message, payload) => {
  response.status(statusCode).json({
    status: isSuccess,
    message: message.message ? message.message : message,
    data: payload ? payload : {},
  });
};

const getCurrentDateTimeUTC = async (format) => {
  try {
    return format ? moment.utc().format(format) : null;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getTimeDiff = async (startDate, endDate) => {
  try {
    if (!startDate || !endDate) {
      return null;
    }

    let secondsDiff =
      (new Date(endDate.getTime()) - new Date(startDate.getTime())) / 1000;
    const minutesDiff = secondsDiff / 60;
    const hoursDiff = minutesDiff / 60;

    return {
      seconds: Math.abs(Math.round(secondsDiff)),
      minutes: Math.abs(Math.round(minutesDiff)),
      hours: Math.abs(Math.round(hoursDiff)),
    };
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const filterMobileNumber = async (number) => {
  try {
    if (!number) {
      return null;
    }

    let data = number;
    if (number.startsWith("+91")) {
      data = await number.slice(3);
    } else if (/[\(\)\-\s]/.test(number)) {
      // Check if the phone number contains open/close brackets, hyphen, or space
      // If the phone number contains any of these characters, remove them
      data = await number.replace(/[\(\)\-\s]/g, "");
    }
    return data;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getUniqueAndNotNullValueFromArray = async (array) => {
  try {
    let data = [];
    if (array.length) {
      data = await array.filter((value, index, self) => {
        return value !== null && self.indexOf(value) === index;
      });
    }
    return data;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getUniqueValueFromArray = async (array) => {
  try {
    let data = [];
    if (array.length) {
      data = await array.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
    }
    return data;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const replaceData = async (data, replaceKey, replaceValue) => {
  try {
    const replace = await data.replace(replaceKey, replaceValue);
    return replace;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const generateRandomAlphaNumericString = async (length = 10) => {
  try {
    const charset =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      result += charset.charAt(randomIndex);
    }
    return result;
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const convertBufferToBase64 = (buffer) => {
  try {
    if (!buffer) {
      return null;
    }

    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    let binary = "";

    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }

    return Buffer.from(binary, "binary").toString("base64");
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getPadString = async (
  value,
  prefix = null,
  padString = "0",
  length = 4
) => {
  try {
    if (value) {
      if (typeof value === "number") {
        value = value.toString();
      }

      return `${prefix}${value.padStart(length, padString)}`;
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const capitalizeFirstLetter = async (string) => {
  try {
    return string.replace(/\b\w/g, function (char) {
      return char.toUpperCase();
    });
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const getCronExpression = async (frequency, value) => {
  try {
    switch (frequency) {
      // Run every "value" minutes
      case "minutes":
        return `*/${value} * * * *`;

      // Run every "value" hour
      case "hour":
        return `0 */${value} * * *`;

      // Run every "value" day at midnight
      case "daily":
        return `0 0 */${value} * *`;

      // Run every "value" day of the week at midnight
      case "weekly":
        return `0 0 * * ${value}`;

      // Run every "value" month on the 1st day at midnight
      case "monthly":
        return `0 0 1 */${value} *`;

      // Run every "value" year on January 1st at midnight
      case "yearly":
        return `0 0 1 1 */${value}`;

      // Run every weekday (Monday to Friday) at midnight
      case "weekdays":
        return `0 0 * * 1-5`;

      // Run every weekend (Saturday and Sunday) at midnight
      case "weekends":
        return `0 0 * * 6,0`;

      default:
        throw new Error("Invalid frequency");
    }
  } catch (error) {
    logger.error({ message: error.message, stack: error.stack });
    return null;
  }
};

const encrypt = (text) => {
  const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");
  return iv.toString("hex") + ":" + encrypted;
};

const decrypt = (encryptedText) => {
  const [ivHex, encrypted] = encryptedText.split(":");
  const ivBuffer = Buffer.from(ivHex, "hex");
  const decipher = crypto.createDecipheriv(algorithm, secretKey, ivBuffer);
  let decrypted = decipher.update(encrypted, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
};

module.exports = {
  getResponse,
  getCurrentDateTimeUTC,
  getTimeDiff,
  filterMobileNumber,
  getUniqueAndNotNullValueFromArray,
  getUniqueValueFromArray,
  replaceData,
  generateRandomAlphaNumericString,
  convertBufferToBase64,
  getPadString,
  capitalizeFirstLetter,
  getCronExpression,
  encrypt,
  decrypt,
};
