name: Build and deploy api
#added a comment to test
on:
  push:
    branches: [production]
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: eternia-digitalhub/serviceapp-order-service
jobs:
  build-and-push-image:
    runs-on: runner-self-hosted
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Log in to the Container registry
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
      - name: Build and push test Docker image
        uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
        with:
          context: .
          push: true
          build-args: |
            APP_NAME=serviceapp-order-service
            NODE_ENV=production
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      # - name: Configure Production-One SSH
      #   run: |
      #     mkdir -p ~/.ssh/
      #     echo "$SSH_KEY" > ~/.ssh/production_one.key
      #     chmod 600 ~/.ssh/production_one.key
      #     cat >>~/.ssh/config <<END
      #       Host production_one
      #       HostName $SSH_HOST
      #       User $SSH_USER
      #       IdentityFile ~/.ssh/production_one.key
      #       StrictHostKeyChecking no
      #     END
      #   env:
      #     SSH_USER: ${{ secrets.SSH_USER }}
      #     SSH_KEY: ${{ secrets.SSH_KEY_PROD }}
      #     SSH_HOST: ${{ secrets.SSH_HOST_PRODUCTION_ONE }}

      # - name: Deploy Production-One Build
      #   run: |
      #     ssh production_one 'echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u "${{ github.actor }}" --password-stdin'
      #     ssh production_one 'cd /var/www/html/serviceapp-order-service; docker system prune -f; docker-compose pull; docker-compose up -d --force-recreate'

      - name: Configure Production-Two SSH
        run: |
          mkdir -p ~/.ssh/
          echo "$SSH_KEY" > ~/.ssh/production_two.key
          chmod 600 ~/.ssh/production_two.key
          cat >>~/.ssh/config <<END
            Host production_two
            HostName $SSH_HOST
            User $SSH_USER
            IdentityFile ~/.ssh/production_two.key
            StrictHostKeyChecking no
          END
        env:
          SSH_USER: ${{ secrets.SSH_USER }}
          SSH_KEY: ${{ secrets.SSH_KEY_PROD }}
          SSH_HOST: ${{ secrets.SSH_HOST_PRODUCTION_TWO }}

      - name: Deploy Production-Two Build
        run: |
          ssh production_two 'echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u "${{ github.actor }}" --password-stdin'
          ssh production_two 'cd /var/www/html/serviceapp-order-service; docker system prune -f; docker-compose pull; docker-compose up -d --force-recreate'
        if: always()
